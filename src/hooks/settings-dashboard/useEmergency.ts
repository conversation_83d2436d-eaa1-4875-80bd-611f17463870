import axios from '@/services/api-client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

const getEmergency = async () => {
  const response = await axios.get('/emergency-information/', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('rT')}`,
    },
  });
  return response.data;
};

const updateEmergency = async (data: EmergencyInfo) => {
  const response = await axios.post('/emergency-information/', data, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('rT')}`,
    },
  });
  return response.data;
};
export const useGetEmergency = () => {
  return useQuery<EmergencyInfo>({
    queryKey: ['emergency'],
    queryFn: getEmergency,
  });
};

export const useUpdateEmergency = () => {
  const queryClient = useQueryClient();
  return useMutation<EmergencyInfo, Error, EmergencyInfo>({
    mutationKey: ['update-emergency'],
    mutationFn: updateEmergency,
    onSuccess: () => {
      toast.success('Emergency information updated successfully');
      queryClient.invalidateQueries({ queryKey: ['emergency'] });
    },
    onError: (error) => {
      toast.error(error.message || 'Emergency information update failed');
    },
  });
};
