import axios from "@/services/api-client";
import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const changePassword = async (email: string) => {
  const response = await axios.post(
    "/password-reset/",
    { email },
  );
  return response.data;
};

export const useChangePassword = () => {
  return useMutation({
    mutationFn: changePassword,
    onSuccess: (response: any) => {
      toast.success(response.message);
    },
    onError: (error: any) => {
      toast.error(error.response.data.email[0]);
    },
  });
};
