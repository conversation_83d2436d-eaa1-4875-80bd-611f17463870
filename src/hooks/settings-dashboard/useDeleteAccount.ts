import axios from "@/services/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
const deleteAccount = async () => {
  const response = await axios.post(
    "/delete-account/",
    {},
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    }
  );
  return response.data;
};

export const useDeleteAccount = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteAccount,
    onSuccess: (response: any) => {
      localStorage.removeItem("rT");
      localStorage.removeItem("rrT");
      localStorage.removeItem("store");
      toast.success(response.message);
      queryClient.invalidateQueries({ queryKey: ["user"] });
      window.location.href = "/";
    },
    onError: (error: any) => {
      toast.error(error.response.data.detail);
    },
  });
};
