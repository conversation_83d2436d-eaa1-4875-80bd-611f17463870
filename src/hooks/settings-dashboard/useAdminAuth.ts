"use client";
import { useEffect, useState } from "react";

const ADMIN_PASSWORD = process.env.NEXT_PUBLIC_ADMIN_PASSWORD;

export const useAdminAuth = () => {
  const [isAdminAuthenticated, setIsAdminAuthenticated] = useState(false);
  const [adminPassword, setAdminPassword] = useState("");
  const [showDialog, setShowDialog] = useState(false);
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  // Check authentication status when component mounts
  useEffect(() => {
    // Move this to client-side only
    if (typeof window !== "undefined") {
      const adminPasswordStorage = localStorage.getItem("adminPassword");
      if (adminPasswordStorage === ADMIN_PASSWORD) {
        setAdminPassword(adminPasswordStorage);
        setIsAdminAuthenticated(true);
        setShowDialog(false); // Ensure dialog is hidden if authenticated
      } else {
        setAdminPassword("");
        setIsAdminAuthenticated(false);
        setShowDialog(true);
      }
    }
  }, []); // Only run on mount

  const handleAuthentication = async () => {
    if (password === ADMIN_PASSWORD) {
      setIsAdminAuthenticated(true);
      setAdminPassword(password);
      setShowDialog(false);
      setError("");
      localStorage.setItem("adminPassword", password);
      setPassword("");
      return true;
    } else {
      setError("Incorrect password");
      setPassword("");
      setIsAdminAuthenticated(false);
      localStorage.removeItem("adminPassword"); // Clear any existing authentication
      return false;
    }
  };

  return {
    isAdminAuthenticated,
    showDialog,
    setShowDialog,
    password,
    setPassword,
    adminPassword,
    handleAuthentication,
    error,
  };
};
