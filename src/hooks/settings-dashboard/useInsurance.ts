import { InsuranceFormType } from "@/lib/utils/validations";
import axios from "@/services/api-client";
import { useStore } from "@/store/store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

const commonFunctions = () => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { setUser } = useStore();

  const getInsurance = async () => {
    const response = await axios.get("/insurance/", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const createInsurance = async (data: InsuranceFormType) => {
    const response = await axios.post("/insurance/", data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  };

  const updateInsurance = async (data: InsuranceFormType) => {
    const response = await axios.put(`/insurance/${data.id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  };

  const deleteInsurance = async (id: string) => {
    const response = await axios.delete(`/insurance/${id}/`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
  };

  return {
    getInsurance,
    createInsurance,
    updateInsurance,
    deleteInsurance,
    queryClient,
    router,
    setUser,
  };
};

export const useGetInsurance = () => {
  const { getInsurance } = commonFunctions();
  return useQuery({
    queryKey: ["insurance"],
    queryFn: getInsurance,
  });
};

export const useCreateInsurance = () => {
  const { createInsurance, queryClient } = commonFunctions();
  return useMutation({
    mutationFn: createInsurance,
    onSuccess: () => {
      toast.success("Insurance created successfully");
      queryClient.invalidateQueries({ queryKey: ["insurance"] });
    },
  });
};

export const useUpdateInsurance = () => {
  const { updateInsurance, queryClient } = commonFunctions();
  return useMutation({
    mutationFn: updateInsurance,
    onSuccess: () => {
      toast.success("Insurance updated successfully");
      queryClient.invalidateQueries({ queryKey: ["insurance"] });
    },
  });
};

export const useDeleteInsurance = () => {
  const { deleteInsurance, queryClient } = commonFunctions();
  return useMutation({
    mutationFn: deleteInsurance,
    onSuccess: () => {
      toast.success("Insurance deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["insurance"] });
    },
  });
};
