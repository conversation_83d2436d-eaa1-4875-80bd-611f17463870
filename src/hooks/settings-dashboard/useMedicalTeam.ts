import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { PractitionerFormValues, RoleMedicalTeamFormValues } from "@/lib/utils/validations";



const useCommonMedicalTeam = () => {
  const queryClient = useQueryClient();

  const getMedicalTeam = async () => {
    const token = localStorage.getItem("rT");
    const response = await axios.get<RoleMedicalTeamFormValues>(
      "/medical-practitioners/",
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  };

  const updateMedicalTeam = async (data: PractitionerFormValues[]) => {
    const token = localStorage.getItem("rT");
    if (!token) {
      throw new Error("No authentication token found");
    }
    // console.log(data);

    try {
      // Separate practitioners into those that need PATCH vs POST
      const existingPractitioners = data.filter(
        (practitioner) => practitioner.id
      );
      const newPractitioners = data.filter((practitioner) => !practitioner.id);

      const requests = [];

      // Handle existing practitioners (PATCH) if any
      if (existingPractitioners.length > 0) {
        const patchRequests = existingPractitioners.map((practitioner) =>
          axios.patch(
            `/medical-practitioners/${practitioner.id}/`,
            practitioner,
            { headers: { Authorization: `Bearer ${token}` } }
          )
        );
        requests.push(Promise.all(patchRequests));
      }

      if (newPractitioners.length > 0) {
        const postRequests = newPractitioners.map((practitioner) =>
          axios.post("/medical-practitioners/", practitioner, {
            headers: { Authorization: `Bearer ${token}` },
          })
        );
        requests.push(Promise.all(postRequests));
      }

      // Wait for all requests to complete
      const results = await Promise.allSettled(requests);

      // Check for any failures
      const failures = results.filter((result) => result.status === "rejected");
      if (failures.length > 0) {
        throw new Error(`${failures.length} batch operations failed`);
      }

      return results;
    } catch (error) {
      console.error("Medical team update failed:", error);
      throw error;
    }
  };

  const deletePractitioner = async (id: string) => {
    const token = localStorage.getItem("rT");
    if (!token) {
      throw new Error("No authentication token found");
    }
    await axios.delete(`/medical-practitioners/${id}/`, { headers: { Authorization: `Bearer ${token}` } });
  };

  return {
    getMedicalTeam,
    updateMedicalTeam,
    deletePractitioner,
    queryClient,
  };
};

export const useGetMedicalTeam = () => {
  const { getMedicalTeam } = useCommonMedicalTeam();

  return useQuery({
    queryKey: ["medical-team"],
    queryFn: getMedicalTeam,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export const useUpdateMedicalTeam = () => {
  const { updateMedicalTeam, queryClient } = useCommonMedicalTeam();

  return useMutation({
    mutationKey: ["update-medical-team"],
    mutationFn: updateMedicalTeam,
    onSuccess: () => {
      toast.success("Medical team updated successfully");
      queryClient.invalidateQueries({ queryKey: ["medical-team"] });
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update medical team");
    },
  });
};

export const useDeletePractitioner = () => {
  const { deletePractitioner, queryClient } = useCommonMedicalTeam();

  return useMutation({
    mutationKey: ["delete-practitioner"],
    mutationFn: deletePractitioner,
    onSuccess: () => {
      toast.success("Practitioner deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["medical-team"] });
    },
  });
};
