import axios from "@/services/api-client";
import { useQuery } from "@tanstack/react-query";

const sharedQRValue = async (token: string) => {
  const response = await axios.post("/verify-token/", { token });
  return response.data;
};

export const useShareQR = (token: string) => {
  const { data, isLoading, isError } = useQuery({
    queryKey: ["sharedQRValue", token],
    queryFn: () => sharedQRValue(token),
  });
  return { data, isLoading, isError };
};
