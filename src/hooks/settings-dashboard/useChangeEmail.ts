import axios from "@/services/api-client";
import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const changeEmail = async (new_email: string) => {
  const response = await axios.post(
    "/email-change/",
    { new_email },
    {
      headers: {
        Authorization: `Bear<PERSON> ${localStorage.getItem("rT")}`,
      },
    }
  );
  return response.data;
};

export const useChangeEmail = () => {
  return useMutation({
    mutationFn: changeEmail,
    onSuccess: (response: any) => {
      toast.success(response.message);
    },
    onError: (error: any) => {
      toast.error(error.response.data.new_email[0]);
    },
  });
};
