import axios from "@/services/api-client";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const commonFunction = () => {
  const shareAccess = async (id: string) => {
    const response = await axios.post("/roles/access-tokens/", {
      granted_to_id: id,
    });
    return response.data;
  };

  const requestAccess = async (email: string) => {
    console.log("requestAccess function called with email:", email);
    try {
      const response = await axios.post("/roles/access-tokens/request-access/", {
        email: email,
      });
      console.log("requestAccess API response:", response.data);
      return response.data;
    } catch (error) {
      console.error("requestAccess API error:", error);
      throw error;
    }
  };

  const getSharedAccess = async () => {
    const response = await axios.get(`/roles/access-tokens/`);
    return response.data;
  };

  const revokeAccess = async (id: string) => {
    const response = await axios.post(`/roles/access-tokens/${id}/revoke/`);
    return response.data;
  };

  return { shareAccess, getSharedAccess, requestAccess, revokeAccess };
};

export const useRevokeAccess = () => {
  const { revokeAccess } = commonFunction();
  return useMutation({
    mutationFn: revokeAccess,
  });
};

export const useAccess = () => {
  const { shareAccess } = commonFunction();
  return useMutation({
    mutationFn: shareAccess,
    onSuccess: (response: any) => {
      toast.success("Access granted successfully");
    },
    onError: (error: any) => {
      toast.error(error.response.data.email[0]);
    },
  });
};

export const useGetSharedAccess = () => {
  const { getSharedAccess } = commonFunction();
  return useQuery({
    queryKey: ["shared-access"],
    queryFn: getSharedAccess,
  });
};

export const useRequestAccess = (options?: { onSuccess?: (data: any) => void; onError?: (error: any) => void }) => {
  const { requestAccess } = commonFunction();
  return useMutation({
    mutationFn: requestAccess,
    onSuccess: (response: any) => {
      console.log("requestAccess success:", response);
      toast.success("Access request sent successfully");
      if (options?.onSuccess) {
        options.onSuccess(response);
      }
    },
    onError: (error: any) => {
      console.error("requestAccess mutation error:", error);
      toast.error(error.response?.data?.email?.[0] || "Failed to send access request");
      if (options?.onError) {
        options.onError(error);
      }
    },
  });
};
