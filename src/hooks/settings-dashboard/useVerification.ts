import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const commonFunction = () => {
  const uploadIdVerification = async (formData: FormData) => {
    const response = await axios.post("/id-verifications/", formData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  };

  const fetchIdVerifications = async () => {
    const response = await axios.get("/id-verifications/", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const deleteIdVerification = async (id: string) => {
    const response = await axios.delete(`/id-verifications/${id}/`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  return { uploadIdVerification, fetchIdVerifications, deleteIdVerification };
};

export const useUploadIdVerification = () => {
  const { uploadIdVerification } = commonFunction();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: uploadIdVerification,
    onSuccess: () => {
      toast.success("ID Verification uploaded successfully");
      queryClient.invalidateQueries({ queryKey: ["id-verifications"] });
    },
    onError: () => {
      toast.error("Failed to upload ID Verification");
    },
  });
};

export const useFetchIdVerifications = () => {
  const { fetchIdVerifications } = commonFunction();
  return useQuery({
    queryKey: ["id-verifications"],
    queryFn: fetchIdVerifications,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export const useDeleteIdVerification = () => {
  const { deleteIdVerification } = commonFunction();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteIdVerification,
    onSuccess: () => {
      toast.success("ID Verification deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["id-verifications"] });
    },
    onError: () => {
      toast.error("Failed to delete ID Verification");
    },
  });
};
