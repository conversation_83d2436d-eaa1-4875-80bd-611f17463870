import axios from "@/services/api-client";
import { useQuery } from "@tanstack/react-query";

const generateQRCode = async () => {
  const response = await axios.get("/generate-token/", {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("rT")}`,
    },
  });
  return response.data;
};

export const useGenerateQRCode = () => {
  const { data, isLoading, isError } = useQuery({
    queryKey: ["generate-qr-code"],
    queryFn: generateQRCode,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  return { data, isLoading, isError };
};
