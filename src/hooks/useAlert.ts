"use client";
import axios from "@/services/api-client";
import { useQuery } from "@tanstack/react-query";

const useAlerts = () => {
  return useQuery({
    queryKey: ["alerts"],
    queryFn: async () => {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get<TabAlert[]>("/content/admin/tab-alerts/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
  });
};

export default useAlerts;
