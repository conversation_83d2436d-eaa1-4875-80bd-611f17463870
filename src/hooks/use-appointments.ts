"use client";
import axios from "@/services/api-client";
import { useStore } from "@/store/store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

// Type for the appointment availability response
export interface AppointmentAvailability {
  id?: string;
  doctor?: string;
  clinic?: string | null;
  enterprise?: string | null;
  title: string;
  meeting_link?: string | null;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  is_active: boolean;
  recurrence_type: "none" | "weekly" | "monthly";
  recurrence_interval?: number;
  recurrence_days?: string;
  recurrence_month_day?: number | null;
  recurrence_end_date?: string | null;
  recurrence_count?: number | null;
  status?: "pending" | "confirmed" | "canceled";
}

// Type for available slots response
export interface AvailableSlotResponse {
  date: string;
  day_of_week: string;
  total_slots: number;
  booked_slots: number;
  available_slots: number;
  slots: AvailableSlot[];
}

// Type for available days overview response
export interface AvailableDaysResponse {
  available_days: string[];
  unavailable_days: string[];
}

export interface AvailableSlot {
  start_time: string;
  end_time: string;
  availability_id: string;
  title: string;
  clinic_id: string | null;
  enterprise_id: string | null;
  recurrence_type: string;
  status: string;
  booking_info: any;
}

// Type for appointment data
export interface Appointment {
  id: string;
  appointment_type: string;
  start_time: string;
  end_time: string;
  title: string;
  notes?: string;
  location?: string;
  is_all_day: boolean;
  status?: string;
  mode?: string;
  meeting_link?: string | null;
  creator?: string;
  patient?: string;
  doctor?: string;
  qr_code?: string | null;
  google_event_id?: string | null;
  cancelled_by?: string | null;
  cancellation_reason?: string;
}

export type DoctorContactInfo = {
  id?: string | undefined;
  primary_email: string;
  secondary_email: string;
  is_primary_email_active?: boolean;
  is_secondary_email_active?: boolean;
};

// Type for the form data that will be transformed
export interface WorkingHoursForm {
  applyToAll: boolean;
  globalFrom: string;
  globalTo: string;
  days: {
    [key: string]: {
      id?: string;
      enabled: boolean;
      times: Array<{
        id?: string;
        from: string;
        to: string;
      }>;
    };
  };
  slotDuration: string;
  breakTime: {
    from: string;
    to: string;
  };
}

// Day mapping from form days to API expected format
const DAY_MAPPING: Record<string, string> = {
  MON: "monday",
  TUE: "tuesday",
  WED: "wednesday",
  THU: "thursday",
  FRI: "friday",
  SAT: "saturday",
  SUN: "sunday",
};

// Ensure time is in the correct format (hh:mm:ss)
const formatTimeForApi = (time: string): string => {
  if (!time) return "00:00:00";

  // If time already has seconds, return as is
  if (time.split(":").length === 3) return time;

  // Otherwise, add seconds
  return `${time}:00`;
};

// Convert form data to API-compatible format
const transformFormToApiFormat = (formData: WorkingHoursForm, userId: string): AppointmentAvailability[] => {
  const availabilities: AppointmentAvailability[] = [];
  const today = new Date();

  Object.entries(formData.days).forEach(([day, dayData]) => {
    if (dayData.enabled) {
      // Create time slots array based on the day's times
      // Only include times that have both from and to values
      dayData.times
        .filter((time) => time.from && time.to)
        .forEach((time) => {
          // Calculate the next occurrence of this day
          const dayNum = getDayNumber(DAY_MAPPING[day].toLowerCase());
          const currentDayNum = today.getDay();
          const daysToAdd = (dayNum - currentDayNum + 7) % 7;
          const nextDate = new Date(today);
          nextDate.setDate(today.getDate() + daysToAdd);

          const dateStr = nextDate.toISOString().split("T")[0];

          availabilities.push({
            id: dayData.id,
            doctor: userId,
            clinic: null,
            enterprise: null,
            title: "Availability",
            start_date: dateStr,
            end_date: dateStr,
            start_time: formatTimeForApi(time.from),
            end_time: formatTimeForApi(time.to),
            is_active: true,
            recurrence_type: "weekly",
            recurrence_interval: 1,
            recurrence_days: DAY_MAPPING[day].toLowerCase(),
          });
        });
    }
  });

  return availabilities;
};

// Helper function to get day number
function getDayNumber(day: string): number {
  const dayMap: Record<string, number> = {
    sunday: 0,
    monday: 1,
    tuesday: 2,
    wednesday: 3,
    thursday: 4,
    friday: 5,
    saturday: 6,
  };
  return dayMap[day] || 0;
}

// Type for my available slots response
export interface MyAvailableSlotsResponse {
  start_date: string;
  end_date: string;
  view_type: string;
  timezone: string;
  total_days: number;
  total_slots: number;
  total_available_slots: number;
  total_booked_slots: number;
  schedule: {
    date: string;
    day_of_week: string;
    slots: {
      start_time: string;
      end_time: string;
      availability_id: string;
      title: string;
      clinic_id: string | null;
      enterprise_id: string | null;
      recurrence_type: string;
      status: string;
      booking_info: {
        id: string;
        start_time: string;
        end_time: string;
        status: string;
        patient_id: string;
        patient_name: string;
        title: string;
        clinic_id: string | null;
        enterprise_id: string | null;
        appointment_type: string;
        mode: string;
      } | null;
    }[];
    total_slots: number;
    available_slots: number;
    booked_slots: number;
  }[];
}

// Put all query functions here later u refactor

const fetchAvailableDays = async (doctorId: string, startDate: string, endDate: string): Promise<AvailableSlotResponse> => {
  const response = await axios.get<AvailableSlotResponse>(
    `/appointments/availabilities/available_slots/?doctor_id=${doctorId}&start_date=${startDate}&end_date=${endDate}`,
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    }
  );
  return response.data;
};

export const useAppointmentAvailability = () => {
  const queryClient = useQueryClient();
  const { user } = useStore();

  // Function to fetch all doctor availabilities
  const fetchAvailabilities = async (): Promise<AppointmentAvailability[]> => {
    const response = await axios.get<AppointmentAvailability[]>("/appointments/availabilities/", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Function to fetch appointments within a date range
  const fetchAppointments = async (startDate: string, endDate: string) => {
    const response = await axios.get<Appointment[]>(`/appointments/?start_date=${startDate}&end_date=${endDate}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const createDoctorContactInfo = async (data: DoctorContactInfo) => {
    const response = await axios.post("/appointments/doctor-contact-info/", data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const fetchDoctorContactInfo = async () => {
    const response = await axios.get("/appointments/doctor-contact-info/", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const updateDoctorContactInfo = async (data: DoctorContactInfo) => {
    const response = await axios.put(`/appointments/doctor-contact-info/${data.id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };
  // Function to create or update availabilities
  const createAvailability = async (data: WorkingHoursForm | AppointmentAvailability | AppointmentAvailability[]) => {
    // If it's an array of availabilities
    if (Array.isArray(data)) {
      const response = await axios.post("/appointments/availabilities/bulk_save/", data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
          "Content-Type": "application/json",
        },
      });
      return response.data;
    }

    // If it's the new format (has start_date property)
    if ("start_date" in data) {
      const response = await axios.post("/appointments/availabilities/bulk_save/", [data], {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
          "Content-Type": "application/json",
        },
      });
      return response.data;
    }

    // Old format - transform and send
    const transformedData = transformFormToApiFormat(data as WorkingHoursForm, user.user_id);

    const response = await axios.post("/appointments/availabilities/bulk_save/", transformedData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
        "Content-Type": "application/json",
      },
    });
    return response.data;
  };

  const createEvent = async (data: any) => {
    const response = await axios.post("/appointments/", data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const { mutate: createAppointment, isPending: isCreatingAppointment } = useMutation({
    mutationKey: ["create-appointment"],
    mutationFn: createEvent,
    onSuccess: () => {
      toast.success("Your appointment request has been submitted");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
  });

  // Query to get all availabilities
  const {
    data: availabilities,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["appointment-availabilities"],
    queryFn: fetchAvailabilities,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });

  // Query to fetch appointments within a date range
  const {
    data: appointments,
    isLoading: isLoadingAppointments,
    refetch: refetchAppointments,
  } = useQuery({
    queryKey: ["appointments"],
    queryFn: () => fetchAppointments("2023-03-01", "2026-03-31"),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  // Mutation to create availability
  const { mutate: createAppointmentAvailability, isPending: isCreating } = useMutation({
    mutationKey: ["create-appointment-availability"],
    mutationFn: createAvailability,
    onSuccess: (data, variables) => {
      // Dismiss loading toast if it exists
      toast.dismiss("creating-availabilities");

      // Show appropriate message based on whether we created one or multiple availabilities
      if (Array.isArray(variables) && variables.length > 1) {
        toast.success(`${variables.length} availabilities created successfully`);
      } else {
        toast.success("Availability updated successfully");
      }
      queryClient.invalidateQueries({ queryKey: ["appointment-availabilities"] });
    },
    onError: (error: any) => {
      // Dismiss loading toast if it exists
      toast.dismiss("creating-availabilities");

      console.error("API Error:", error.response?.data || error.message);
      toast.error(error.response?.data?.detail || error.message || "Failed to update availability");
    },
  });

  const { mutate: createDoctorContactInfoMutation } = useMutation({
    mutationKey: ["create-doctor-contact-info"],
    mutationFn: createDoctorContactInfo,
  });

  const { mutate: updateDoctorContactInfoMutation } = useMutation({
    mutationKey: ["update-doctor-contact-info"],
    mutationFn: updateDoctorContactInfo,
  });

  const { data: doctorContactInfo } = useQuery({
    queryKey: ["doctor-contact-info"],
    queryFn: fetchDoctorContactInfo,
  });

  // Function to fetch available slots for a doctor on a specific date
  const fetchAvailableSlots = async (doctorId: string, date: string): Promise<AvailableSlotResponse> => {
    console.log("Making API call for slots:", { doctorId, date });
    const response = await axios.get<AvailableSlotResponse>(`/appointments/availabilities/available_slots/?doctor_id=${doctorId}&date=${date}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    console.log("API Response:", response.data);
    return response.data;
  };

  // Query to fetch available slots
  const fetchSlots = (doctorId: string, date: string) => {
    return useQuery({
      queryKey: ["available-slots", doctorId, date],
      queryFn: () => fetchAvailableSlots(doctorId, date),
      enabled: !!doctorId && !!date,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    });
  };

  // Function to fetch available days for a doctor within a date range
  const fetchAvailableDays = async (doctorId: string, startDate: string, endDate: string): Promise<AvailableDaysResponse> => {
    const response = await axios.get<AvailableDaysResponse>(
      `/appointments/availabilities/available_days/?doctor_id=${doctorId}&start_date=${startDate}&end_date=${endDate}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
        },
      }
    );
    return response.data;
  };

  // Query to fetch available days for a month
  const fetchDoctorAvailableDays = (doctorId: string, year: number, month: number) => {
    // Calculate first and last day of the month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    const startDate = firstDay.toISOString().split("T")[0];
    const endDate = lastDay.toISOString().split("T")[0];

    return useQuery({
      queryKey: ["doctor-available-days", doctorId, year, month],
      queryFn: () => fetchAvailableDays(doctorId, startDate, endDate),
      enabled: !!doctorId && year > 0 && month >= 0,
    });
  };

  const deleteAvailability = async (id: string) => {
    const response = await axios.delete(`/appointments/availabilities/${id}/`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const updateSingleAvailability = async (id: string, data: AppointmentAvailability) => {
    const response = await axios.put(`/appointments/availabilities/${id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Add function to update appointment status
  const updateAppointmentStatus = async (id: string, status: "confirmed" | "canceled", cancellation_reason?: string) => {
    const data = status === "canceled" ? { status, cancellation_reason } : { status };
    const response = await axios.put(`/appointments/${id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Add mutation for updating appointment status
  const { mutate: updateStatus, isPending: isUpdatingStatus } = useMutation({
    mutationKey: ["update-appointment-status"],
    mutationFn: ({ id, status, cancellation_reason }: { id: string; status: "confirmed" | "canceled"; cancellation_reason?: string }) =>
      updateAppointmentStatus(id, status, cancellation_reason),
    onSuccess: () => {
      toast.success("Appointment status updated successfully");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || "Failed to update appointment status");
    },
  });

  // Add new mutations for delete and update
  const { mutate: deleteAppointmentAvailability, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-appointment-availability"],
    mutationFn: deleteAvailability,
    onSuccess: () => {
      toast.success("Availability deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["appointment-availabilities"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || error.message || "Failed to delete availability");
    },
  });

  const { mutate: updateSingleAppointmentAvailability, isPending: isUpdating } = useMutation({
    mutationKey: ["update-single-appointment-availability"],
    mutationFn: ({ id, data }: { id: string; data: AppointmentAvailability }) => updateSingleAvailability(id, data),
    onSuccess: () => {
      toast.success("Availability updated successfully");
      queryClient.invalidateQueries({ queryKey: ["appointment-availabilities"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || error.message || "Failed to update availability");
    },
  });

  // Update appointment mutation
  const updateAppointment = async (data: any) => {
    const response = await axios.put(`/appointments/${data.id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const { mutate: updateAppointmentMutation, isPending: isUpdatingAppointment } = useMutation({
    mutationFn: updateAppointment,
    onSuccess: () => {
      toast.success("Appointment updated successfully");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || "Failed to update appointment");
    },
  });

  // Delete appointment mutation
  const deleteAppointment = async (id: string) => {
    const response = await axios.delete(`/appointments/${id}/`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const { mutate: deleteAppointmentMutation, isPending: isDeletingAppointment } = useMutation({
    mutationFn: deleteAppointment,
    onSuccess: () => {
      toast.success("Appointment deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || "Failed to delete appointment");
    },
  });

  // Function to fetch my available slots within a date range
  const fetchMyAvailableSlots = async (startDate: string, endDate: string): Promise<MyAvailableSlotsResponse> => {
    const response = await axios.get<MyAvailableSlotsResponse>(`/appointments/availabilities/my_available_slots/?start_date=${startDate}&end_date=${endDate}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Get current date and format it as YYYY-MM-DD
  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };

  // Get a future date (2026) formatted as YYYY-MM-DD
  const getFutureDate = () => {
    const futureDate = new Date();
    // Add 90 days to the current date
    futureDate.setDate(futureDate.getDate() + 90);
    return futureDate.toISOString().split("T")[0];
  };

  // Query to fetch my available slots
  const { data: myAvailableSlots, isLoading: isLoadingMySlots } = useQuery({
    queryKey: ["my-available-slots"],
    queryFn: () => fetchMyAvailableSlots(getCurrentDate(), getFutureDate()),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  return {
    availabilities,
    appointments,
    isLoadingAppointments,
    refetchAppointments,
    createAppointment,
    isCreatingAppointment,
    isLoading,
    isError,
    createAppointmentAvailability,
    isCreating,
    refetch,
    createDoctorContactInfoMutation,
    doctorContactInfo,
    updateDoctorContactInfoMutation,
    fetchSlots,
    fetchDoctorAvailableDays,
    deleteAppointmentAvailability,
    updateSingleAppointmentAvailability,
    isDeleting,
    isUpdating,
    updateStatus,
    isUpdatingStatus,
    updateAppointmentMutation,
    isUpdatingAppointment,
    deleteAppointmentMutation,
    isDeletingAppointment,
    myAvailableSlots,
    isLoadingMySlots,
  };
};

// export separate custom hook

export const useFetchAvailableDays = (doctorId: string, startDate: string, endDate: string) => {
  return useQuery({
    queryKey: ["fetch-available-days", doctorId, startDate, endDate],
    queryFn: () => fetchAvailableDays(doctorId, startDate, endDate),
  });
};
