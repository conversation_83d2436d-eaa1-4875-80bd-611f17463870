import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const card = async (data: any) => {
  const response = await axios.post("/business-card/upsert/", data, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("rT")}`,
    },
  });
  return response.data;
};

const getCard = async (id: string) => {
  const response = await axios.get(`/business-card/?user_id=${id}`, {});

  return response.data;
};

export const useCreateEditCard = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["card"],
    mutationFn: card,
    onSuccess: (data) => {
      const isUpdate = data?.id;
      toast.success(isUpdate ? "R-Name-Card updated successfully" : "R-Name-Card created successfully");
      queryClient.invalidateQueries({ queryKey: ["card"] });
    },
    onError: (error) => {
      toast.error(error.message || "Card operation failed");
    },
  });
};

export const useGetCard = (id: string) => {
  return useQuery({
    queryKey: ["card"],
    queryFn: () => getCard(id),
    retry: false,
  });
};
