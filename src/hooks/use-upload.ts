import axios from "@/services/api-client";
import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

type UploadDocumentProps = {
  type?: string;
  data?: FormData;
};

export const useUploadDocument = () => {
  return useMutation({
    mutationFn: async ({ type, data }: UploadDocumentProps) => {
      if (!data) {
        throw new Error("No data provided for upload");
      }

      const response = await axios.post("/supporting-docs/", data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
          "Content-Type": "multipart/form-data",
        },
      });

      console.log("Upload response:", response.data);
      return response.data;
    },
    onError: (error: any) => {
      console.error("Upload error:", error);
      const errorMessage = error.response?.data?.detail || "Failed to upload document";
      toast.error(errorMessage);
    },
  });
};
