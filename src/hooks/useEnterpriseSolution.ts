"use client";
import { AdminFormType, EnterpriseFormType } from "@/lib/utils/validations";
import axios from "@/services/api-client";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

export const useEnterpriseSolution = () => {
  const queryClient = useQueryClient();

  const createEnterprise = async () => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const redirect_url = window.location.origin + "/onboarding/payment-success";
      const response = await axios.post(
        "enterprise/solutions/upgrade_to_enterprise/",
        { solution_id: "0196b5ac-5bfc-7926-ae06-f33e58d74ef2", redirect_url },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to create enterprise");
    }
  };

  const getEnterpriseProfile = async (showToast = true) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get("enterprise/profile/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (showToast) {
        toast.promise(Promise.resolve(response), {
          loading: "Fetching enterprise profile...",
          success: "Enterprise profile created successfully",
          error: "Failed to create enterprise profile",
        });
      }

      queryClient.invalidateQueries({ queryKey: ["user"] });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to fetch enterprise profile");
    }
  };

  const uploadEnterpriseImages = async (formData: FormData) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.post("enterprise/profile/", formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to upload enterprise images");
    }
  };

  const removeEnterpriseImage = async () => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.put(
        "enterprise/remove-logo/",
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to remove enterprise image");
    }
  };

  const updateEnterpriseProfile = async (data: EnterpriseFormType) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.put("enterprise/profile/", data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to update enterprise profile");
    }
  };

  const getEnterpriseAdmin = async () => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get("enterprise/admin/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to fetch enterprise admins");
    }
  };

  const updateEnterpriseAdmin = async (data: AdminFormType) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const transformedData = data.admins.map((admin) => {
        if (!admin.id) {
          const { id, ...adminWithoutId } = admin;
          return adminWithoutId;
        }
        return admin;
      });

      const response = await axios.post("enterprise/admin/", transformedData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to update enterprise admin");
    }
  };

  const deleteEnterpriseAdmin = async (id: string) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.delete(`enterprise/admin/?id=${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to delete enterprise admin");
    }
  };

  const addEnterpriseUser = async (enterprise_id: string, email: string) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.post(
        `enterprise/${enterprise_id}/invite/`,
        { email: email },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to add enterprise user");
    }
  };

  const acceptEnterpriseInvite = async (token: string) => {
    try {
      const response = await axios.post(`enterprise/invite/accept/`, { token: token });

      queryClient.invalidateQueries({ queryKey: ["user"] });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to accept enterprise invite");
    }
  };

  const listEnterpriseInvites = async (enterprise_id: string) => {
    try {
      const response = await axios.get(`/enterprise/${enterprise_id}/users/`);
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to list enterprise invites");
    }
  };
  return {
    createEnterprise,
    getEnterpriseProfile,
    updateEnterpriseProfile,
    getEnterpriseAdmin,
    updateEnterpriseAdmin,
    deleteEnterpriseAdmin,
    uploadEnterpriseImages,
    removeEnterpriseImage,
    addEnterpriseUser,
    acceptEnterpriseInvite,
    listEnterpriseInvites,
    queryClient,
  };
};

export const useCreateEnterprise = () => {
  const { createEnterprise } = useEnterpriseSolution();

  return useMutation({
    mutationFn: createEnterprise,
    onSuccess: (data) => {
      if (data?.checkout_url) {
        window.location.href = data.checkout_url;
      } else {
        toast.error("Failed to go to checkout");
      }
    },
    onError: () => {
      toast.error("Failed to go to checkout");
    },
  });
};

export const useGetEnterpriseProfile = (showToast = true) => {
  const { getEnterpriseProfile } = useEnterpriseSolution();

  return useQuery({
    queryKey: ["enterprise-profile"],
    queryFn: () => getEnterpriseProfile(showToast),
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export const useUpdateEnterpriseProfile = () => {
  const { updateEnterpriseProfile, queryClient } = useEnterpriseSolution();

  return useMutation({
    mutationFn: updateEnterpriseProfile,
    onSuccess: () => {
      // Invalidate and refetch the enterprise profile query
      queryClient.invalidateQueries({ queryKey: ["enterprise-profile"] });
    },
  });
};

export const useDeleteEnterpriseAdmin = () => {
  const { deleteEnterpriseAdmin, queryClient } = useEnterpriseSolution();

  return useMutation({
    mutationFn: deleteEnterpriseAdmin,
    onSuccess: () => {
      // Invalidate and refetch the admin list after successful deletion
      queryClient.invalidateQueries({ queryKey: ["enterprise-admin"] });
    },
    onError: () => {
      toast.error("Failed to delete admin");
    },
  });
};

export const useGetEnterpriseAdmin = () => {
  const { getEnterpriseAdmin } = useEnterpriseSolution();

  return useQuery({
    queryKey: ["enterprise-admin"],
    queryFn: getEnterpriseAdmin,
  });
};

export const useUpdateEnterpriseAdmin = () => {
  const { updateEnterpriseAdmin, queryClient } = useEnterpriseSolution();

  return useMutation({
    mutationFn: updateEnterpriseAdmin,
    onSuccess: () => {
      // Add this to invalidate and refetch the admin list after successful update
      queryClient.invalidateQueries({ queryKey: ["enterprise-admin"] });
    },
  });
};

export const useUploadEnterpriseImages = () => {
  const { uploadEnterpriseImages, queryClient } = useEnterpriseSolution();

  return useMutation({
    mutationFn: uploadEnterpriseImages,
    onSuccess: () => {
      // Invalidate and refetch relevant queries after successful upload
      queryClient.invalidateQueries({ queryKey: ["enterprise-profile"] });
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("Images uploaded successfully");
    },
    onError: () => {
      toast.error("Failed to upload images");
    },
  });
};

export const useRemoveEnterpriseImage = () => {
  const { removeEnterpriseImage, queryClient } = useEnterpriseSolution();

  return useMutation({
    mutationFn: removeEnterpriseImage,
    onSuccess: () => {
      // Invalidate and refetch relevant queries after successful removal
      queryClient.invalidateQueries({ queryKey: ["enterprise-profile"] });
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("Logo removed successfully");
    },
    onError: () => {
      toast.error("Failed to remove logo");
    },
  });
};

export const useAddEnterpriseUser = () => {
  const { addEnterpriseUser, queryClient } = useEnterpriseSolution();

  return useMutation({
    mutationFn: (data: { enterprise_id: string; email: string }) => addEnterpriseUser(data.enterprise_id, data.email),
    onSuccess: () => {
      toast.success("Invite sent successfully");
      queryClient.invalidateQueries({ queryKey: ["enterprise-invites"] });
    },
    onError: () => {
      toast.error("Failed to send invite");
    },
  });
};

export const useAcceptEnterpriseInvite = () => {
  const { acceptEnterpriseInvite } = useEnterpriseSolution();

  return useMutation({
    mutationFn: acceptEnterpriseInvite,
  });
};

export const useListEnterpriseInvites = (enterprise_id: string) => {
  const { listEnterpriseInvites } = useEnterpriseSolution();

  return useQuery({
    queryKey: ["enterprise-invites"],
    queryFn: () => listEnterpriseInvites(enterprise_id),
  });
};
