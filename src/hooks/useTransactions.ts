import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export interface SubscriptionPlan {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  features: string[];
  price: number;
  billing_cycle: "MONTHLY" | "YEARLY";
  order: number;
  is_active?: boolean;
  // Optional stripe properties for future integration
  stripe_price_id?: string;
  short_name?: string;
  button_text?: string;
}

const useTransactions = () => {
  const queryClient = useQueryClient();

  const getBillings = async () => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get("/payments/billing-history/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to fetch billing history");
    }
  };

  const getSubscriptionPlans = async () => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get("/content/admin/subscription-plans/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to fetch subscription plans");
    }
  };

  const createPaymentLink = async ({ price_id, redirect_url }: { price_id: string; redirect_url?: string }) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.post(
        "/payments/create-checkout-session/",
        {
          price_id,
          redirect_url,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error creating payment link:", error);
      throw new Error(error instanceof Error ? error.message : "Failed to create payment link");
    }
  };

  return { getBillings, getSubscriptionPlans, createPaymentLink, queryClient };
};

export const useGetBillings = () => {
  const { getBillings } = useTransactions();

  return useQuery({
    queryKey: ["billings"],
    queryFn: getBillings,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};

export const useGetSubscriptionPlans = () => {
  const { getSubscriptionPlans } = useTransactions();

  return useQuery({
    queryKey: ["subscription-plans"],
    queryFn: getSubscriptionPlans,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};

export const useCreatePaymentLink = () => {
  const { createPaymentLink } = useTransactions();

  return useMutation({
    mutationFn: createPaymentLink,
    onError: (error: Error) => {
      console.error("Payment link creation failed:", error);
    },
    onSuccess: (data) => {
      const url = (data as any)?.url;
      if (typeof url === "string") {
        window.location.href = url;
      }
    },
  });
};
