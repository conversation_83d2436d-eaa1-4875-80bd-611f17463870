"use client";
import axios from "@/services/api-client";
import { useQuery, useMutation } from "@tanstack/react-query";

interface ServiceResponse {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  storage_size: string;
  features: string[];
  price: string;
  is_active: boolean;
  service_type: string;
  order: number;
  button_text: string;
  discounted_price: string;
  active_promotions: PromotionFormValues[];
}

interface CheckoutDnaServicesRequest {
  services: {
    service_id: string;
    genes?: string[];
    depth?: string;
    promo_code?: string;
    dna_code?: string;
  }[];
  input_files: (string | number)[];
  redirect_url: string;
}

interface CheckoutDnaServicesResponse {
  url: string;
  session_id: string;
}

interface VerifyPromoCodeRequest {
  code: string;
  service_ids: string[];
}

export const useServices = () => {
  return useQuery({
    queryKey: ["services"],
    queryFn: async () => {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get<ServiceResponse[]>("/content/admin/services/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
  });
};

export const useCheckoutDnaServices = () => {
  return useMutation({
    mutationFn: async (data: CheckoutDnaServicesRequest) => {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      console.log("data", data);
      const response = await axios.post<CheckoutDnaServicesResponse>("/billing/checkout_dna_services/", data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
  });
};

export const useCheckPromoCode = () => {
  return useQuery({
    queryKey: ["promo-code"],
    queryFn: async () => {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get("content/admin/promotions/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
  });
};

export const useVerifyPromoCode = () => {
  return useMutation({
    mutationFn: async (data: VerifyPromoCodeRequest) => {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.post("content/public/verify-promo-code/", data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
  });
};

export default useServices;
