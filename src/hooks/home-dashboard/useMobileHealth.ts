"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";

const useMobileHealth = () => {
  const queryClient = useQueryClient();

  const getFitbitProfile = async (beforeDate: string) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}health-watch/fitbit_user/get_profile/?before_date=${beforeDate}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.data.profile) {
        return response.data.profile;
      } else {
        return null;
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to fetch fitbit profile");
    }
  };

  const handleFitbitAuth = async () => {
    try {
      window.location.href = `${process.env.NEXT_PUBLIC_API_URL}health-watch/get_authorization_url/`;
    } catch (error) {
      console.error("Error connecting to Fitbit:", error);
      throw new Error("Failed to connect to Fitbit");
    }
  };

  return {
    getFitbitProfile,
    handleFitbitAuth,
  };
};

export const useGetFitbitProfile = () => {
  const { getFitbitProfile } = useMobileHealth();

  return useQuery({
    queryKey: ["fitbit-profile"],
    queryFn: () => getFitbitProfile(new Date().toISOString().split("T")[0]),
  });
};

export const useHandleFitbitAuth = () => {
  const { handleFitbitAuth } = useMobileHealth();

  return useMutation({
    mutationFn: handleFitbitAuth,
  });
};
