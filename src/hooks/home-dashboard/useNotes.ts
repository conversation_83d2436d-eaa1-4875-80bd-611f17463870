import axios from "@/services/api-client";
import { QueryClient, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const commonFunctions = () => {
  const queryClient = useQueryClient();

  const createNote = async (noteData: Notes[]) => {
    const response = await axios.post(
      "/notes/",
      noteData,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
        },
      }
    );
    return response.data;
  };

  const updateNote = async (noteData: Notes[]) => {
    const response = await axios.post(
      `/notes/`,
      noteData,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
        },
      }
    );
    return response.data;
  };

  const getNotes = async () => {
    const response = await axios.get("/notes/", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const deleteNote = async (id: string) => {
    const response = await axios.delete(`/notes?id=${id}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  return {
    createNote,
    getNotes,
    deleteNote,
    updateNote,
    queryClient,
  };
};

export const useCreateNote = () => {
  const { createNote, queryClient } = commonFunctions();
  return useMutation({
    mutationFn: createNote,
    onSuccess: () => {
      toast.success("Note created successfully");
      queryClient.invalidateQueries({ queryKey: ["notes"] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.detail);
    },
  });
};

export const useGetNotes = () => {
  const { getNotes } = commonFunctions();
  const { data, isLoading, error } = useQuery({
    queryKey: ["notes"],
    queryFn: getNotes,
  });

  return {
    notes: data,
    isLoading: isLoading,
    error: error,
  };
};

export const useDeleteNote = () => {
  const { deleteNote, queryClient } = commonFunctions();
  return useMutation({
    mutationFn: deleteNote,
    onSuccess: () => {
      toast.success("Note deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["notes"] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.detail);
    },
  });
};

export const useUpdateNote = () => {
  const { updateNote, queryClient } = commonFunctions();
  return useMutation({
    mutationFn: updateNote,
    onSuccess: () => {
      toast.success("Note updated successfully");
      queryClient.invalidateQueries({ queryKey: ["notes"] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.detail);
    },
  });
};
