import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

type GetDiagnosisProps = {
  page?: number;
  pageSize?: number;
  type?: string;
  data?: any;
  id?: string;
};

const commonFunction = ({ page, pageSize }: GetDiagnosisProps) => {
  const queryClient = useQueryClient();
  const seggregate = (data: any) => {
    const diagnosis = data?.filter((item: any) => item.type === "diagnosis");
    const prescriptions = data?.filter((item: any) => item.type === "prescription");
    return { diagnosis, prescriptions };
  };
  const getDiagnosis = async () => {
    const response = await axios.get(`/supporting-docs/?page=${page}&page_size=${pageSize}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const UploadDocument = async ({ type, data }: GetDiagnosisProps) => {
    data.append("type", type);
    const response = await axios.post("/supporting-docs/", data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  };

  const DeleteDocument = async (id: string) => {
    const response = await axios.delete(`/supporting-docs?id=${id}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  return { getDiagnosis, seggregate, UploadDocument, DeleteDocument, queryClient };
};

export const useGetDiagnosis = () => {
  const { getDiagnosis, seggregate } = commonFunction({ page: 1, pageSize: 10 });
  const { data, isLoading, error } = useQuery({
    queryKey: ["diagnosis"],
    queryFn: getDiagnosis,
  });

  const { diagnosis, prescriptions } = seggregate(data);

  return { diagnosis, prescriptions, isLoading, error };
};

export const useUploadDocument = () => {
  const { UploadDocument, queryClient } = commonFunction({ type: "diagnosis", data: {} });
  return useMutation({
    mutationFn: UploadDocument,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["diagnosis"] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.detail);
    },
  });
};

export const useDeleteDocument = () => {
  const { DeleteDocument, queryClient } = commonFunction({ type: "diagnosis", data: {} });
  return useMutation({
    mutationFn: DeleteDocument,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["diagnosis"] });
      toast.success("Document deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error.response.data.detail);
    },
  });
};
