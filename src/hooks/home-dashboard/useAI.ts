import axios from "@/services/api-client";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import toast from "react-hot-toast";

type StartChatProps = {
  message: string;
  model: string;
  enable_search?: boolean;
};

type SendChatMessageProps = {
  message: string;
  model: string;
  chat_id: string;
  enable_search?: boolean;
};

type SendUnifiedMessageProps = {
  message: string;
  chat_id: string;
  files?: File[];
  processing_method?: "multimodal" | "text_extraction";
};

type AnalyzeFilesProps = {
  ids: string[];
  model: string;
  type?: string;
};

type ChatHistoryResponse = {
  id: string;
  title: string;
  model: string;
  timestamp: string;
};

type ChatMessage = {
  role: "user" | "assistant";
  content: string;
};

const commonFunction = () => {
  const queryClient = useQueryClient();

  const startChat = async ({ message, model, enable_search }: StartChatProps) => {
    const response = await axios.post(
      "/ai-analysis/analyze-diagnosis/start_chat/",
      { message, model, enable_search },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
        },
      }
    );
    return response.data;
  };

  const deleteChat = async ({ chat_ids }: { chat_ids: string[] }) => {
    const response = await axios.delete(`/ai-analysis/analyze-diagnosis/delete_chats/`, {
      headers: { Authorization: `Bearer ${localStorage.getItem("rT")}` },
      data: { chat_ids },
    });
    return response.data;
  };

  const saveDiagnosisAsPDF = async ({ output_id }: { output_id: string }) => {
    const response = await axios.post(
      "/ai-analysis/analyze-diagnosis/save_to_pdf/",
      { output_id },
      { headers: { Authorization: `Bearer ${localStorage.getItem("rT")}` } }
    );
    return response.data;
  };

  const analyzeFiles = async ({ ids, model, type }: AnalyzeFilesProps) => {
    model = model || "gemini";

    const response = await axios.post(
      "/ai-analysis/analyze-diagnosis/start_chat_with_files/",
      { ids, model, type },
      { headers: { Authorization: `Bearer ${localStorage.getItem("rT")}` } }
    );

    return response.data;
  };

  const sendChatMessage = async ({ message, model, chat_id, enable_search }: SendChatMessageProps) => {
    const response = await axios.post(
      "/ai-analysis/analyze-diagnosis/message/",
      { message, model, chat_id, enable_search },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
        },
      }
    );
    return response.data;
  };

  const sendUnifiedMessage = async ({ message, chat_id, files, processing_method }: SendUnifiedMessageProps) => {
    const formData = new FormData();
    formData.append("message", message);
    formData.append("chat_id", chat_id);

    if (processing_method) {
      formData.append("processing_method", processing_method);
    }

    if (files && files.length > 0) {
      files.forEach((file) => {
        formData.append("files", file);
      });
    }

    const response = await axios.post("/ai-analysis/analyze-diagnosis/message_unified/", formData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  };

  const getChatHistory = async () => {
    const startDate = new Date();
    startDate.setFullYear(startDate.getFullYear() - 4);
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 1);

    const response = await axios.get(`/ai-analysis/analyze-diagnosis/chat_history/?has_files=false`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const getChatById = async ({ id }: { id: string }) => {
    const response = await axios.get(`/ai-analysis/analyze-diagnosis/chat_history/?chat_id=${id}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  return { startChat, sendChatMessage, sendUnifiedMessage, analyzeFiles, saveDiagnosisAsPDF, getChatHistory, getChatById, deleteChat, queryClient };
};

export const useStartChat = () => {
  const { startChat } = commonFunction();
  return useMutation({
    mutationFn: startChat,
  });
};

export const useSendChatMessage = () => {
  const { sendChatMessage } = commonFunction();
  return useMutation({
    mutationFn: sendChatMessage,
  });
};

export const useSendUnifiedMessage = () => {
  const { sendUnifiedMessage } = commonFunction();
  return useMutation({
    mutationFn: sendUnifiedMessage,
  });
};

export const useAnalyzeFiles = () => {
  const { analyzeFiles } = commonFunction();
  return useMutation({
    mutationFn: analyzeFiles,
  });
};

export const useSaveDiagnosisAsPDF = () => {
  const { saveDiagnosisAsPDF, queryClient } = commonFunction();
  return useMutation({
    mutationFn: saveDiagnosisAsPDF,
    onError: (error: any) => {
      toast.error(error.response.data.detail);
    },
    onSuccess: (data: any) => {
      toast.success(data.message);
      queryClient.invalidateQueries({ queryKey: ["diagnosis"] });
    },
  });
};

export const useChatHistory = (enabled: boolean = false) => {
  const { getChatHistory } = commonFunction();
  return useQuery({
    queryKey: ["chatHistory"],
    queryFn: getChatHistory,
    enabled: enabled,
  });
};

export const useGetChatById = () => {
  const { getChatById } = commonFunction();
  return useMutation({
    mutationFn: getChatById,
  });
};

export const useDeleteChat = () => {
  const { deleteChat, queryClient } = commonFunction();
  return useMutation({
    mutationFn: deleteChat,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chatHistory"] });
      toast.success("Chat deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || "Failed to delete chat");
    },
  });
};
