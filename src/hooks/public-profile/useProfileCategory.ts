import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const fetchCategories = async () => {
  const token = localStorage.getItem("rT");
  const response = await axios.get("/profile-categories/", {
    headers: { Authorization: `Bearer ${token}` },
  });
  return response.data;
};

const fetchProfileById = async (userId: string) => {
  try {
    const response = await axios.get(`/roles/profile/${userId}`);
    if (response.status === 200) {
      return response.data;
    }
  } catch (error) {
    console.error("Error fetching profile by ID:", error);
    throw error;
  }
};

const fetchCategoriesById = async (userId: string) => {
  try {
    // Fetch categories for specific user without authorization
    const categoriesResponse = await axios.get(`/roles/profile/${userId}/categories`);
    const categories = categoriesResponse.data;
    return categories;
  } catch (error) {
    console.error("Error fetching categories by ID:", error);
    throw error;
  }
};

const saveCategoriesNew = async (categories: any[]) => {
  const token = localStorage.getItem("rT");
  const response = await axios.put("/profile-categories/all/", categories, {
    headers: { Authorization: `Bearer ${token}` },
  });
  return response.data;
};

const sendMessage = async (subject: string, content: string, userId: string) => {
  const token = localStorage.getItem("rT");
  const response = await axios.post("/appointments/doctor-messages/", { subject, content, doctor: userId }, { headers: { Authorization: `Bearer ${token}` } });
  return response.data;
};

const getMessages = async (userId: string) => {
  const token = localStorage.getItem("rT");
  const response = await axios.get(`/appointments/doctor-messages/`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const markMessageAsRead = async (messageId: string) => {
  const token = localStorage.getItem("rT");
  const response = await axios.put(
    `/appointments/doctor-messages/${messageId}/`,
    { is_read: true },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

export const useProfileCategory = () => {
  return useQuery({
    queryKey: ["categories"],
    queryFn: fetchCategories,
    // staleTime: 1000 * 60, // 1 minute
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const useProfileById = (id: string) => {
  return useQuery({
    queryKey: ["profile", id],
    queryFn: () => fetchProfileById(id),
  });
};

// may remove later
export const useGetCategoriesById = (id: string) => {
  return useQuery({
    queryKey: ["categories", id],
    queryFn: () => fetchCategoriesById(id),
  });
};

export const useSaveProfileCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: saveCategoriesNew,
    onSuccess: () => {
      toast.success("Categories saved successfully");
      queryClient.invalidateQueries({ queryKey: ["categories"] });
    },
    onError: (error) => {
      toast.error("Failed to save categories");
    },
  });
};

export const useMessageMe = () => {
  return useMutation({
    mutationFn: ({ subject, content, userId }: { subject: string; content: string; userId: string }) => sendMessage(subject, content, userId),
    onSuccess: (data) => {
      console.log("data", data);
      toast.success("Message sent successfully");
    },
    onError: (error) => {
      toast.error("Failed to send message");
    },
  });
};

export const useGetMessages = (userId: string) => {
  return useQuery({
    queryKey: ["messages", userId],
    queryFn: () => getMessages(userId),
  });
};

export const useMarkMessageAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (messageId: string) => markMessageAsRead(messageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["messages"] });
      toast.success("Message marked as read");
    },
    onError: () => {
      toast.error("Failed to mark message as read");
    },
  });
};
