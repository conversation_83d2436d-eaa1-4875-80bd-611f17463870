import { useState, useEffect } from "react";

export const useDnaServicesState = (services: Service[] | undefined) => {
  const [selectedFiles, setSelectedFiles] = useState<Array<{ id: string | number; fileName: string }>>([]);
  const [isPromoModalOpen, setIsPromoModalOpen] = useState(false);
  const [isCancerModalOpen, setIsCancerModalOpen] = useState(false);

  // Initialize selected files from sessionStorage
  useEffect(() => {
    const storedFiles = sessionStorage.getItem("selectedDNAFiles");
    if (storedFiles) {
      try {
        const parsedFiles = JSON.parse(storedFiles);
        // Handle both old format (string[]) and new format (object[])
        if (Array.isArray(parsedFiles)) {
          if (parsedFiles.length > 0 && typeof parsedFiles[0] === "string") {
            // Old format - convert to new format
            const fileObjects = parsedFiles.map((fileName, index) => ({
              id: index,
              fileName: fileName,
            }));
            setSelectedFiles(fileObjects);
          } else {
            // New format
            setSelectedFiles(parsedFiles);
          }
        }
      } catch (error) {
        console.error("Error parsing selectedDNAFiles from sessionStorage:", error);
        sessionStorage.removeItem("selectedDNAFiles");
      }
    }
  }, []);

  return {
    selectedFiles,
    setSelectedFiles,
    isPromoModalOpen,
    setIsPromoModalOpen,
    isCancerModalOpen,
    setIsCancerModalOpen,
  };
};
