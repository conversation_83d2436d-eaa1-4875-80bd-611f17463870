// Helper function to check if a file is a FASTQ file
const isFastqFile = (fileName: string): boolean => {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith(".fastq") || lowerName.endsWith(".fq") || lowerName.endsWith(".fq.gz");
};

// Helper function to check if two FASTQ files form a valid R1/R2 pair
const isValidFastqPair = (fastqFiles: string[]): boolean => {
  if (fastqFiles.length !== 2) return false;

  const lowerFiles = fastqFiles.map((name) => name.toLowerCase());

  // Check for _r1/_r2 pattern
  const hasR1 = lowerFiles.some((name) => name.includes("_r1"));
  const hasR2 = lowerFiles.some((name) => name.includes("_r2"));

  if (hasR1 && hasR2) return true;

  // Check for _1/_2 pattern
  const has1 = lowerFiles.some((name) => name.includes("_1"));
  const has2 = lowerFiles.some((name) => name.includes("_2"));

  if (has1 && has2) return true;

  // Check for .1/.2 pattern (less common but possible)
  const hasDot1 = lowerFiles.some((name) => name.includes(".1"));
  const hasDot2 = lowerFiles.some((name) => name.includes(".2"));

  return hasDot1 && hasDot2;
};

export const useFileValidation = (selectedFiles: Array<{ id: string | number; fileName: string }>) => {
  const fileNames = selectedFiles.map((file) => file.fileName);

  const hasRequiredFastqFiles = (files: string[]) => {
    const fastqFiles = files.filter((file) => isFastqFile(file));
    return fastqFiles.length >= 2 && isValidFastqPair(fastqFiles);
  };
  const hasValidFiles = (files: string[]) => hasRequiredFastqFiles(files) || files.some((file) => file.endsWith(".bam") || file.endsWith(".vcf"));
  const hasVcfFile = (files: string[]) => files.some((file) => file.endsWith(".vcf"));
  const hasBamFile = (files: string[]) => files.some((file) => file.endsWith(".bam"));
  // Add helper to check if basic analysis should be locked
  const shouldLockBasicAnalysis = (files: string[]) => hasRequiredFastqFiles(files) || hasBamFile(files);

  return {
    hasValidFiles: hasValidFiles(fileNames),
    hasVcfFile: hasVcfFile(fileNames),
    hasBamFile: hasBamFile(fileNames),
    shouldLockBasicAnalysis: shouldLockBasicAnalysis(fileNames),
  };
};
