"use client";
import axios from "@/services/api-client";
import type { AxiosError } from "axios";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRef } from "react";

// Custom error class for upload cancellation
class UploadCancelledError extends Error {
  constructor() {
    super("Upload was cancelled");
    this.name = "UploadCancelledError";
  }
}

interface UploadResponse {
  message: string;
  file_id: string;
  status: string;
}

interface UploadRequest {
  file: File;
  fileType: "FASTQ" | "BAM" | "VCF";
  onProgress?: (progress: number) => void;
}

// Interfaces for 3-step upload flow
interface FileData {
  filename: string;
  file_type: string;
  file_size: number;
}

interface ErrorResponse {
  detail?: string;
  message?: string;
}

const API_ENDPOINTS = {
  GET_UPLOAD_URL: "/analysis/gs-file-manager/get-resumable-upload-url/",
  CONFIRM_UPLOAD: "/analysis/gs-file-manager/confirm-upload/",
};

export const useDnaFileUpload = () => {
  const queryClient = useQueryClient();
  const currentXHRRef = useRef<XMLHttpRequest | null>(null);
  const isCancelledRef = useRef<boolean>(false);

  // 3-step upload implementation
  const getUploadUrl = async (fileData: FileData) => {
    const token = localStorage.getItem("rT");
    if (!token) throw new Error("No authentication token found");

    try {
      const response = await axios.post<{ upload_url: string; file_path: string }>(
        API_ENDPOINTS.GET_UPLOAD_URL,
        fileData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw new Error(axiosError.response?.data?.detail || "Failed to get upload URL");
    }
  };

  const uploadToGCS = async (uploadUrl: string, file: File, onProgress?: (progress: number) => void) => {
    try {
      return new Promise<boolean>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        currentXHRRef.current = xhr;
        
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress && !isCancelledRef.current) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress(progress);
          }
        });
        
        xhr.addEventListener('load', () => {
          currentXHRRef.current = null;
          if (isCancelledRef.current) {
            reject(new UploadCancelledError());
          } else if (xhr.status === 200) {
            resolve(true);
          } else {
            // Create a more user-friendly error message
            const errorMessage = xhr.status === 400 && xhr.responseText.includes('MalformedSecurityHeader') 
              ? 'File upload failed due to invalid file type or format. Please try again.'
              : `Upload failed with status: ${xhr.status}`;
            reject(new Error(errorMessage));
          }
        });
        
        xhr.addEventListener('error', () => {
          currentXHRRef.current = null;
          reject(new Error('Upload failed due to network error'));
        });
        
        xhr.addEventListener('abort', () => {
          currentXHRRef.current = null;
          reject(new UploadCancelledError());
        });
        
        xhr.open('PUT', uploadUrl);
        // Always set Content-Type to application/octet-stream for consistency
        xhr.setRequestHeader('Content-Type', 'application/octet-stream');
        xhr.send(file);
      });
    } catch (error) {
      currentXHRRef.current = null;
      if (isCancelledRef.current || error instanceof UploadCancelledError) {
        throw new UploadCancelledError();
      }
      throw error;
    }
  };

  const confirmUpload = async (filePath: string, fileData: FileData) => {
    const token = localStorage.getItem("rT");
    if (!token) throw new Error("No authentication token found");

    try {
      const response = await axios.post<UploadResponse>(
        API_ENDPOINTS.CONFIRM_UPLOAD,
        {
          file_path: filePath,
          filename: fileData.filename,
          file_type: fileData.file_type,
          file_size: fileData.file_size,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw new Error(axiosError.response?.data?.detail || "Failed to confirm upload");
    }
  };

  const threeStepUpload = async ({ file, fileType, onProgress }: UploadRequest) => {
    if (!file) throw new Error("No file selected");

    // Reset cancellation flag at start of upload
    isCancelledRef.current = false;

    try {
      if (isCancelledRef.current) throw new UploadCancelledError();

      const fileData: FileData = {
        filename: file.name,
        file_type: "application/octet-stream", // Always use application/octet-stream
        file_size: file.size,
      };

      const { upload_url, file_path } = await getUploadUrl(fileData);
      if (isCancelledRef.current) throw new UploadCancelledError();

      if (!upload_url || !file_path) {
        throw new Error("Invalid upload URL response from server");
      }

      const uploadSuccess = await uploadToGCS(upload_url, file, onProgress);
      if (isCancelledRef.current) throw new UploadCancelledError();

      if (!uploadSuccess) {
        throw new Error("Upload to GCS failed");
      }

      const result = await confirmUpload(file_path, fileData);
      if (isCancelledRef.current) throw new UploadCancelledError();

      return result;
    } catch (error) {
      if (isCancelledRef.current || error instanceof UploadCancelledError) {
        throw new UploadCancelledError();
      }
      throw error;
    }
  };

  const cancelUpload = () => {
    isCancelledRef.current = true;
    if (currentXHRRef.current) {
      currentXHRRef.current.abort();
      currentXHRRef.current = null;
    }
  };

  const mutation = useMutation({
    mutationFn: threeStepUpload,
    onSuccess: () => {
      if (!isCancelledRef.current) {
        currentXHRRef.current = null;
        isCancelledRef.current = false;
        queryClient.invalidateQueries({ queryKey: ["my-files"] });
      }
    },
    onError: () => {
      currentXHRRef.current = null;
    },
  });

  return {
    ...mutation,
    cancelUpload,
    isCancelled: isCancelledRef.current,
    mutateAsync: async (...args: Parameters<typeof mutation.mutateAsync>) => {
      try {
        const result = await mutation.mutateAsync(...args);
        if (isCancelledRef.current) {
          throw new UploadCancelledError();
        }
        return result;
      } catch (error) {
        if (isCancelledRef.current || error instanceof UploadCancelledError) {
          throw new UploadCancelledError();
        }
        throw error;
      }
    }
  };
};

export default useDnaFileUpload;
