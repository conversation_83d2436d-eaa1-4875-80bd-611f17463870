import { useState } from "react";
import { useFileValidation } from "./useFileValidation";
import { CANCER_VARIANT_SERVICE_NAME } from "@/constants/dna-dashboard";

export const useServiceSelection = (services: Service[] | undefined, initialFiles: Array<{ id: string | number; fileName: string }>) => {
  const [selectedServiceIds, setSelectedServiceIds] = useState<string[]>([]);
  const [cancerServiceDetails, setCancerServiceDetails] = useState<CancerServiceDetails>({
    serviceId: null,
    genes: [],
    depth: "100",
  });

  // Service selection logic
  const handleServiceSelection = (serviceId: string, title: string) => {
    if (title === CANCER_VARIANT_SERVICE_NAME) {
      return {
        type: "CANCER_SERVICE",
        serviceId,
      };
    }

    // Check if this is the basic analysis service (first service) and if it should be locked
    const { shouldLockBasicAnalysis } = useFileValidation(initialFiles);
    const isBasicAnalysisService = services && services[0]?.id === serviceId;

    if (isBasicAnalysisService && shouldLockBasicAnalysis) {
      // Don't allow deselection of basic analysis when it should be locked
      return { type: "LOCKED_SERVICE" };
    }

    setSelectedServiceIds((current) => (current.includes(serviceId) ? current.filter((id) => id !== serviceId) : [...current, serviceId]));

    return { type: "REGULAR_SERVICE" };
  };

  return {
    selectedServiceIds,
    cancerServiceDetails,
    handleServiceSelection,
    setCancerServiceDetails,
    setSelectedServiceIds,
  };
};
