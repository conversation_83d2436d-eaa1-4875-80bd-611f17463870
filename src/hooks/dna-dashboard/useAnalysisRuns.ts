import { useQuery } from "@tanstack/react-query";
import axios from "@/services/api-client";

export interface InputFile {
  id: string;
  signed_url: string;
  filename: string;
  file_size: number;
  status: string;
  is_result: boolean;
}

export interface AnalysisRun {
  id: string;
  user: string;
  status: string;
  created_at: string;
  updated_at: string;
  total_steps: number;
  current_step: number;
  input_files: InputFile[];
  service_payments: any[];
}

interface AnalysisRunsResponse {
  analysis_runs: AnalysisRun[];
}

const fetchAnalysisRuns = async (): Promise<AnalysisRun[]> => {
  const token = localStorage.getItem("rT");
  if (!token) throw new Error("No authentication token found");

  const response = await axios.get<AnalysisRunsResponse>("/analysis/analysis-runs/", {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data.analysis_runs;
};

export const useAnalysisRuns = () => {
  const query = useQuery({
    queryKey: ["analysisRuns"],
    queryFn: fetchAnalysisRuns,
    staleTime: 5 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return {
    analysisRuns: query.data || [],
    loading: query.isLoading,
    error: query.error?.message || null,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
    isFetching: query.isFetching,
  };
};
