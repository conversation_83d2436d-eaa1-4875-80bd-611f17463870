import { useMemo } from "react";
import { staticDNAFiles } from "@/config/constants";
import { ApiFile, FileData } from "./file-manager-types";

export const useFileProcessing = (files: ApiFile[] | undefined, currentPage: number, itemsPerPage: number = 10) => {
  const totalPages = useMemo(() => {
    const totalFiles = files && files.length > 0 ? files.length : staticDNAFiles.length;
    return Math.ceil(totalFiles / itemsPerPage);
  }, [files, itemsPerPage]);

  const getCurrentPageFiles = useMemo((): FileData[] => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    // If there are no files, use staticDNAFiles
    if (!files || files.length === 0) {
      return staticDNAFiles.slice(startIndex, endIndex).map((file) => ({
        id: file.id,
        fileName: file.fileName,
        date: file.date,
        size: file.size,
        ai: file.ai,
      }));
    }

    // Transform fetched files to match the display structure
    return files.slice(startIndex, endIndex).map((file) => ({
      id: file.id,
      fileName: file.uploaded_file.filename,
      date: new Date(file.created_at).toLocaleString(),
      size: `${(file.uploaded_file.file_size / (1024 * 1024)).toFixed(2)}MB`,
      ai: file.file_type?.toLowerCase().includes("ai") || false,
      uploaded_file: file.uploaded_file,
    }));
  }, [files, currentPage, itemsPerPage]);

  return {
    currentFiles: getCurrentPageFiles,
    totalPages,
  };
};
