import { Selection } from "@heroui/react";

export interface FileData {
  id: string | number;
  fileName: string;
  date: string;
  size: string;
  ai?: boolean;
  uploaded_file?: {
    file_url: string;
    filename: string;
    file_size: number;
    id: number;
    file_type: string;
    user: string;
    uploaded_at: string;
  };
}

export interface ApiFile {
  id: string;
  uploaded_file: {
    id: number;
    file_url: string;
    filename: string;
    file_size: number;
    uploaded_at: string;
    file_type: string;
    user: string;
  };
  created_at: string;
  updated_at: string;
  status: string;
  is_result: boolean;
  file_type?: string;
}

export interface FileManagerState {
  selectedKeys: Selection;
  currentPage: number;
  isUploadModalOpen: boolean;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export interface FileTableProps {
  files: FileData[];
  selectedKeys: Selection;
  onSelectionChange: (selection: Selection) => void;
  isLoading: boolean;
  error: Error | null;
}

export interface FileActionsProps {
  file: FileData;
}
