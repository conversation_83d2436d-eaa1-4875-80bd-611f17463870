import axios from "@/services/api-client";
import { useQuery } from "@tanstack/react-query";
import { ApiFile } from "./file-manager-types";

export const useMyFiles = () => {
  return useQuery({
    queryKey: ["my-files"],
    queryFn: async () => {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get<ApiFile[]>("/analysis/gs-file-manager/my-files/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data as ApiFile[];
    },
  });
};

export default useMyFiles;
