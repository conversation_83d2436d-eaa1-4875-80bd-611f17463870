import { useFileValidation } from "./useFileValidation";

export const useServiceClickHandler = (
  services: Service[] | undefined,
  selectedFiles: Array<{ id: string | number; fileName: string }>,
  selectedServiceIds: string[],
  handleServiceSelection: (serviceId: string, title: string) => { type: string; serviceId?: string },
  setCancerServiceDetails: (details: CancerServiceDetails) => void,
  setIsCancerModalOpen: (open: boolean) => void
) => {
  const handleServiceClick = (serviceId: string, title: string) => {
    // Skip if no files are selected - button should be disabled anyway
    if (selectedFiles.length === 0) return;

    // Skip if first service and no valid files - button should be disabled anyway
    if (services && services[0]?.id === serviceId && !useFileValidation(selectedFiles).hasValidFiles) return;

    // Check if this is a locked basic analysis service
    const { shouldLockBasicAnalysis } = useFileValidation(selectedFiles);
    const isBasicAnalysisService = services && services[0]?.id === serviceId;

    if (isBasicAnalysisService && shouldLockBasicAnalysis && selectedServiceIds.includes(serviceId)) {
      // Don't allow any action on locked basic analysis service that is already selected
      return;
    }

    const { type } = handleServiceSelection(serviceId, title);

    if (type === "CANCER_SERVICE") {
      setCancerServiceDetails({
        serviceId,
        genes: [],
        depth: "100",
      });
      setIsCancerModalOpen(true);
    }
  };

  return { handleServiceClick };
};
