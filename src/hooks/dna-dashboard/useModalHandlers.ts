import { toast } from "react-hot-toast";
import { useCheckoutDnaServices } from "@/hooks/useServices";
import { useGetUser } from "@/hooks/useUser";
import { TAB_IDS } from "@/components/Dashboards/DNADashboard";
import { useRouter } from "next/navigation";

export const useModalHandlers = (
  selectedServiceIds: string[],
  cancerServiceDetails: CancerServiceDetails,
  setCancerServiceDetails: (details: CancerServiceDetails) => void,
  setSelectedServiceIds: (ids: string[] | ((prev: string[]) => string[])) => void,
  setIsCancerModalOpen: (open: boolean) => void,
  setIsPromoModalOpen: (open: boolean) => void,
  services?: Service[]
) => {
  const { data: user } = useGetUser();
  const checkoutMutation = useCheckoutDnaServices();

  const handleCancerModalConfirm = (data: { genes: string[]; depth: string }) => {
    console.log("Cancer modal confirm data received:", data);

    // Validate genes are selected before proceeding
    if (data.genes.length === 0) {
      toast.error("Please select genes for Cancer Variant Customized Analysis before proceeding.");
      return;
    }

    if (cancerServiceDetails.serviceId) {
      setSelectedServiceIds((currentIds: string[]) => [...currentIds, cancerServiceDetails.serviceId as string]);
      const updatedDetails = {
        serviceId: cancerServiceDetails.serviceId,
        genes: data.genes,
        depth: data.depth,
      };
      console.log("Setting cancer service details:", updatedDetails);
      setCancerServiceDetails(updatedDetails);
    }
    setIsCancerModalOpen(false);
  };

  const handleCancerModalClose = () => {
    setIsCancerModalOpen(false);
    setCancerServiceDetails({
      serviceId: null,
      genes: [],
      depth: "100",
    });
  };

  const handleContinue = () => {
    setIsPromoModalOpen(true);
  };

  const shouldUseDepth30 = (serviceId: string): boolean => {
    if (!services) return false;

    const service = services.find((s) => s.id === serviceId);
    if (!service) return false;

    const serviceName = service.name.toLowerCase();

    return (
      serviceName.includes("fertility") || serviceName.includes("pharmacogenomic") || serviceName.includes("pharmacogenomics") || serviceName.includes("pgx")
    );
  };

  const handlePromoCodeConfirm = async (promoCodes: Record<string, string>) => {
    try {
      // Debug logging
      console.log("Cancer service details:", cancerServiceDetails);
      console.log("Selected service IDs:", selectedServiceIds);

      // Additional safety check: validate cancer service has genes selected
      if (cancerServiceDetails.serviceId && selectedServiceIds.includes(cancerServiceDetails.serviceId)) {
        if (!cancerServiceDetails.genes || cancerServiceDetails.genes.length === 0) {
          toast.error("Cancer Variant Customized Analysis requires gene selection. Please configure the service properly.");
          return;
        }
      }

      // Get selected file IDs from session storage
      const storedFiles = sessionStorage.getItem("selectedDNAFiles");
      let fileIds: (string | number)[] = [];

      if (storedFiles) {
        try {
          const parsedFiles = JSON.parse(storedFiles) as Array<{ id: string | number; fileName: string }>;
          fileIds = parsedFiles.map((file) => file.id);
        } catch (error) {
          console.error("Error parsing selectedDNAFiles from sessionStorage:", error);
          toast.error("Error retrieving selected files. Please reselect your files.");
          return;
        }
      }

      if (fileIds.length === 0) {
        toast.error("No files selected. Please select files before proceeding.");
        return;
      }

      const response = await checkoutMutation.mutateAsync({
        services: selectedServiceIds.map((id) => {
          const serviceData: any = {
            service_id: id,
            ...(promoCodes[id] && {
              promo_code: promoCodes[id],
            }),
          };

          // Debug logging for each service
          console.log(`Processing service ID: ${id}`);
          console.log(`Cancer service ID: ${cancerServiceDetails.serviceId}`);
          console.log(`Is cancer service: ${id === cancerServiceDetails.serviceId}`);

          if (id === cancerServiceDetails.serviceId && cancerServiceDetails.genes && cancerServiceDetails.genes.length > 0) {
            serviceData.dna_code = cancerServiceDetails.genes.join(",");
            serviceData.depth = cancerServiceDetails.depth;
            console.log(`Cancer service data:`, {
              dna_code: serviceData.dna_code,
              depth: serviceData.depth,
              genes: cancerServiceDetails.genes,
            });
          } else if (shouldUseDepth30(id)) {
            serviceData.depth = "30";
          } else {
            serviceData.depth = "100";
          }

          console.log(`Final service data for ${id}:`, serviceData);
          return serviceData;
        }),
        input_files: fileIds,
        redirect_url: `${window.location.origin}/my/dna/${user?.user_id}?dnaTab=${TAB_IDS.GS_ANALYSIS_MANAGER}`,
      });

      sessionStorage.removeItem("selectedDNAFiles");
      window.location.href = response.url;
    } catch (error) {
      toast.error("Failed to checkout services. Please try again.");
      console.error("Checkout error:", error);
    }
  };

  return {
    handleCancerModalConfirm,
    handleCancerModalClose,
    handleContinue,
    handlePromoCodeConfirm,
    isCheckoutLoading: checkoutMutation.isPending,
  };
};
