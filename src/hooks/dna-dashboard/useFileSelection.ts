import { useEffect, useState } from "react";
import { Selection } from "@heroui/react";
import { changeDNATab, TAB_IDS } from "@/components/Dashboards/DNADashboard";
import { FileData } from "./file-manager-types";

// Helper function to check if a file is a FASTQ file
const isFastqFile = (fileName: string): boolean => {
  const lowerName = fileName.toLowerCase();
  return (
    lowerName.endsWith(".fastq") || lowerName.endsWith(".fq") || lowerName.endsWith(".fq.gz") || lowerName.endsWith(".fastq.gz") || lowerName.endsWith(".gz")
  ); // Include .gz files as they could be compressed FASTQ files
};

// Helper function to check if two FASTQ files form a valid R1/R2 pair
const isValidFastqPair = (fastqFiles: string[]): boolean => {
  if (fastqFiles.length !== 2) return false;

  const lowerFiles = fastqFiles.map((name) => name.toLowerCase());

  // Check for _r1/_r2 pattern
  const hasR1 = lowerFiles.some((name) => name.includes("_r1"));
  const hasR2 = lowerFiles.some((name) => name.includes("_r2"));

  if (hasR1 && hasR2) return true;

  // Check for _1/_2 pattern
  const has1 = lowerFiles.some((name) => name.includes("_1"));
  const has2 = lowerFiles.some((name) => name.includes("_2"));

  if (has1 && has2) return true;

  // Check for .1/.2 pattern (less common but possible)
  const hasDot1 = lowerFiles.some((name) => name.includes(".1"));
  const hasDot2 = lowerFiles.some((name) => name.includes(".2"));

  return hasDot1 && hasDot2;
};

export const useFileSelection = (currentFiles: FileData[], currentPage: number) => {
  const [selectedKeys, setSelectedKeys] = useState<Selection>(new Set([]));

  // Initialize selected files from session storage
  useEffect(() => {
    const storedFiles = sessionStorage.getItem("selectedDNAFiles");
    if (storedFiles) {
      const fileData = JSON.parse(storedFiles) as Array<{ id: string | number; fileName: string }>;
      const selectedIds = new Set(fileData.map((file) => file.id.toString()));
      setSelectedKeys(selectedIds);
    }
  }, [currentPage, currentFiles]);

  const handleSelectionChange = (selection: Selection) => {
    setSelectedKeys(selection);

    let selectedFiles: FileData[] = [];

    if (selection === "all") {
      selectedFiles = currentFiles;
    } else if (selection instanceof Set) {
      selectedFiles = Array.from(selection)
        .map((id) => {
          const file = currentFiles.find((f) => f.id.toString() === id);
          return file;
        })
        .filter((file): file is FileData => file !== undefined);
    }

    if (selectedFiles.length > 0) {
      const filesToStore = selectedFiles.map((file) => ({
        id: file.id,
        fileName: file.fileName,
      }));
      sessionStorage.setItem("selectedDNAFiles", JSON.stringify(filesToStore));
    } else {
      sessionStorage.removeItem("selectedDNAFiles");
    }
  };

  const isValidSelection = (): boolean => {
    if (!(selectedKeys instanceof Set) || selectedKeys.size === 0) {
      return false;
    }

    const selectedFileNames = Array.from(selectedKeys)
      .map((id) => {
        const file = currentFiles.find((f) => f.id.toString() === id);
        return file?.fileName;
      })
      .filter((name): name is string => name !== undefined);

    // Check if any output files (HTML, PDF, JSON) are selected
    const hasOutputFiles = selectedFileNames.some(
      (name) => name.toLowerCase().endsWith(".html") || name.toLowerCase().endsWith(".pdf") || name.toLowerCase().endsWith(".json")
    );

    // If any output files are selected, disable analysis
    if (hasOutputFiles) {
      return false;
    }

    // Check if there are any FASTQ files in the selection (including .fq.gz)
    const fastqFiles = selectedFileNames.filter((name) => isFastqFile(name));
    const nonFastqFiles = selectedFileNames.filter((name) => !isFastqFile(name));

    // If there are any FASTQ files selected
    if (fastqFiles.length > 0) {
      // We must have exactly 2 FASTQ files and no other file types
      if (fastqFiles.length !== 2 || nonFastqFiles.length > 0) {
        return false;
      }

      // Check if they form a valid pair using the helper function
      return isValidFastqPair(fastqFiles);
    }

    // Check for BAM and VCF files
    const bamFiles = selectedFileNames.filter((name) => name.toLowerCase().endsWith(".bam"));
    const vcfFiles = selectedFileNames.filter((name) => name.toLowerCase().endsWith(".vcf"));

    // Prevent selection of both BAM and VCF files
    if (bamFiles.length > 0 && vcfFiles.length > 0) {
      return false;
    }

    // For non-FASTQ files, we need at least one valid file (BAM or VCF)
    return selectedFileNames.some((name) => name.toLowerCase().endsWith(".bam") || name.toLowerCase().endsWith(".vcf"));
  };

  const navigateToDNADashboard = () => {
    if (selectedKeys instanceof Set) {
      const selectedFilesData = Array.from(selectedKeys)
        .map((id) => {
          const file = currentFiles.find((f) => f.id.toString() === id);
          return file ? { id: file.id, fileName: file.fileName } : null;
        })
        .filter(Boolean);

      sessionStorage.setItem("selectedDNAFiles", JSON.stringify(selectedFilesData));
    }
    changeDNATab(TAB_IDS.SERVICES);
  };

  return {
    selectedKeys,
    handleSelectionChange,
    isValidSelection,
    navigateToDNADashboard,
  };
};
