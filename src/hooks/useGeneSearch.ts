import { useQuery } from "@tanstack/react-query";
import axios from "@/services/api-client";
import { useState, useEffect, useRef } from "react";

interface GeneSearchResult {
  Gene: string;
  CancerGenes: string;
}

export const useGeneSearch = (searchTerm: string) => {
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);
  const prevLength = useRef(searchTerm.length);

  useEffect(() => {
    // If it's the first character, update immediately
    if (searchTerm.length === 1 && prevLength.current === 0) {
      setDebouncedTerm(searchTerm);
    }
    // For subsequent changes, only update if length difference is 2 or more
    else if (Math.abs(searchTerm.length - prevLength.current) >= 2) {
      setDebouncedTerm(searchTerm);
    }
    // For any other case, debounce the update
    else {
      const timer = setTimeout(() => {
        setDebouncedTerm(searchTerm);
      }, 300);
      return () => clearTimeout(timer);
    }

    prevLength.current = searchTerm.length;
  }, [searchTerm]);

  return useQuery({
    queryKey: ["geneSearch", debouncedTerm],
    queryFn: async () => {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      try {
        console.log("Sending request with term:", debouncedTerm.toUpperCase());
        const response = await axios.get<string[]>(`/analysis/search-genes/?search_term=${debouncedTerm.toUpperCase()}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        console.log("Full API Response:", response);

        if (!response.data || response.data.length === 0) {
          console.log("No results found");
          return [];
        }

        // Transform the string array into GeneSearchResult objects
        const transformedData: GeneSearchResult[] = response.data.map((gene) => ({
          Gene: gene,
          CancerGenes: gene, // Using the same value for both since we only get the gene name
        }));

        return transformedData.slice(0, 10);
      } catch (error) {
        console.error("API Error:", error);
        throw error;
      }
    },
    enabled: Boolean(debouncedTerm) && debouncedTerm.length >= 1, // Changed to allow single character searches
  });
};

export default useGeneSearch;
