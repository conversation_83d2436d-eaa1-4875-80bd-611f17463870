"use client";
import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { DoctorContactInfo } from "./types";

export const useDoctorContactInfo = () => {
  const queryClient = useQueryClient();

  const createDoctorContactInfo = async (data: DoctorContactInfo) => {
    const response = await axios.post("/appointments/doctor-contact-info/", data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const fetchDoctorContactInfo = async () => {
    const response = await axios.get("/appointments/doctor-contact-info/", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const updateDoctorContactInfo = async (data: DoctorContactInfo) => {
    const response = await axios.put(`/appointments/doctor-contact-info/${data.id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const { mutate: createDoctorContactInfoMutation } = useMutation({
    mutationKey: ["create-doctor-contact-info"],
    mutationFn: createDoctorContactInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["doctor-contact-info"] });
    },
  });

  const { mutate: updateDoctorContactInfoMutation } = useMutation({
    mutationKey: ["update-doctor-contact-info"],
    mutationFn: updateDoctorContactInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["doctor-contact-info"] });
    },
  });

  const { data: doctorContactInfo } = useQuery({
    queryKey: ["doctor-contact-info"],
    queryFn: fetchDoctorContactInfo,
  });

  return {
    createDoctorContactInfoMutation,
    updateDoctorContactInfoMutation,
    doctorContactInfo,
  };
};
