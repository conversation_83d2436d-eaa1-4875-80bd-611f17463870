"use client";
import axios from "@/services/api-client";
import { useStore } from "@/store/store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { AppointmentAvailability, WorkingHoursForm } from "./types";
import { transformFormToApiFormat } from "./useUtils";

export const useAppointmentAvailability = () => {
  const queryClient = useQueryClient();
  const { user } = useStore();

  // Function to fetch all doctor availabilities
  const fetchAvailabilities = async (): Promise<AppointmentAvailability[]> => {
    const response = await axios.get<AppointmentAvailability[]>("/appointments/availabilities/", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Function to create or update availabilities
  const createAvailability = async (data: WorkingHoursForm | AppointmentAvailability | AppointmentAvailability[]) => {
    // If it's an array of availabilities
    if (Array.isArray(data)) {
      const response = await axios.post("/appointments/availabilities/bulk_save/", data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
          "Content-Type": "application/json",
        },
      });
      return response.data;
    }

    // If it's the new format (has start_date property)
    if ("start_date" in data) {
      const response = await axios.post("/appointments/availabilities/bulk_save/", [data], {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
          "Content-Type": "application/json",
        },
      });
      return response.data;
    }

    // Old format - transform and send
    const transformedData = transformFormToApiFormat(data as WorkingHoursForm, user.user_id);

    const response = await axios.post("/appointments/availabilities/bulk_save/", transformedData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
        "Content-Type": "application/json",
      },
    });
    return response.data;
  };

  // Delete availability
  const deleteAvailability = async (id: string) => {
    const response = await axios.delete(`/appointments/availabilities/${id}/`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Update a single availability
  const updateSingleAvailability = async (id: string, data: AppointmentAvailability) => {
    const response = await axios.put(`/appointments/availabilities/${id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Query to get all availabilities
  const { data: availabilities, isLoading, isError, refetch } = useQuery({
    queryKey: ["appointment-availabilities"],
    queryFn: fetchAvailabilities,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });

  // Mutation to create availability
  const { mutate: createAppointmentAvailability, isPending: isCreating } = useMutation({
    mutationKey: ["create-appointment-availability"],
    mutationFn: createAvailability,
    onSuccess: (data, variables) => {
      // Dismiss loading toast if it exists
      toast.dismiss("creating-availabilities");

      // Show appropriate message based on whether we created one or multiple availabilities
      if (Array.isArray(variables) && variables.length > 1) {
        toast.success(`${variables.length} availabilities created successfully`);
      } else {
        toast.success("Availability updated successfully");
      }
      queryClient.invalidateQueries({ queryKey: ["appointment-availabilities"] });
    },
    onError: (error: any) => {
      // Dismiss loading toast if it exists
      toast.dismiss("creating-availabilities");

      console.error("API Error:", error.response?.data || error.message);
      toast.error(error.response?.data?.detail || error.message || "Failed to update availability");
    },
  });

  // Add new mutations for delete and update
  const { mutate: deleteAppointmentAvailability, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-appointment-availability"],
    mutationFn: deleteAvailability,
    onSuccess: () => {
      toast.success("Availability deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["appointment-availabilities"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || error.message || "Failed to delete availability");
    },
  });

  const { mutate: updateSingleAppointmentAvailability, isPending: isUpdating } = useMutation({
    mutationKey: ["update-single-appointment-availability"],
    mutationFn: ({ id, data }: { id: string; data: AppointmentAvailability }) => updateSingleAvailability(id, data),
    onSuccess: () => {
      toast.success("Availability updated successfully");
      queryClient.invalidateQueries({ queryKey: ["appointment-availabilities"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || error.message || "Failed to update availability");
    },
  });

  return {
    availabilities,
    isLoading,
    isError,
    createAppointmentAvailability,
    isCreating,
    refetch,
    deleteAppointmentAvailability,
    updateSingleAppointmentAvailability,
    isDeleting,
    isUpdating,
  };
};
