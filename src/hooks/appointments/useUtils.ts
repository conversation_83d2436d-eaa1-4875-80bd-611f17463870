"use client";
import { WorkingHoursForm, AppointmentAvailability } from "./types";

// Day mapping from form days to API expected format
export const DAY_MAPPING: Record<string, string> = {
  MON: "monday",
  TUE: "tuesday",
  WED: "wednesday",
  THU: "thursday",
  FRI: "friday",
  SAT: "saturday",
  SUN: "sunday",
};

// Ensure time is in the correct format (hh:mm:ss)
export const formatTimeForApi = (time: string): string => {
  if (!time) return "00:00:00";

  // If time already has seconds, return as is
  if (time.split(":").length === 3) return time;

  // Otherwise, add seconds
  return `${time}:00`;
};

// Convert form data to API-compatible format
export const transformFormToApiFormat = (formData: WorkingHoursForm, userId: string): AppointmentAvailability[] => {
  const availabilities: AppointmentAvailability[] = [];
  const today = new Date();

  Object.entries(formData.days).forEach(([day, dayData]) => {
    if (dayData.enabled) {
      // Create time slots array based on the day's times
      // Only include times that have both from and to values
      dayData.times
        .filter((time) => time.from && time.to)
        .forEach((time) => {
          // Calculate the next occurrence of this day
          const dayNum = getDayNumber(DAY_MAPPING[day].toLowerCase());
          const currentDayNum = today.getDay();
          const daysToAdd = (dayNum - currentDayNum + 7) % 7;
          const nextDate = new Date(today);
          nextDate.setDate(today.getDate() + daysToAdd);

          const dateStr = nextDate.toISOString().split("T")[0];

          availabilities.push({
            id: dayData.id,
            doctor: userId,
            clinic: null,
            enterprise: null,
            title: "Availability",
            start_date: dateStr,
            end_date: dateStr,
            start_time: formatTimeForApi(time.from),
            end_time: formatTimeForApi(time.to),
            is_active: true,
            recurrence_type: "weekly",
            recurrence_interval: 1,
            recurrence_days: DAY_MAPPING[day].toLowerCase(),
          });
        });
    }
  });

  return availabilities;
};

// Helper function to get day number
export function getDayNumber(day: string): number {
  const dayMap: Record<string, number> = {
    sunday: 0,
    monday: 1,
    tuesday: 2,
    wednesday: 3,
    thursday: 4,
    friday: 5,
    saturday: 6,
  };
  return dayMap[day] || 0;
}

// Get current date and format it as YYYY-MM-DD
export const getCurrentDate = () => {
  const today = new Date();
  return today.toISOString().split("T")[0];
};

// Get a future date formatted as YYYY-MM-DD
export const getFutureDate = (daysAhead = 90) => {
  const futureDate = new Date();
  // Add specified days to the current date
  futureDate.setDate(futureDate.getDate() + daysAhead);
  return futureDate.toISOString().split("T")[0];
};
