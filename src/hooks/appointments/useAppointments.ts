"use client";
import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Appointment } from "./types";
import { getCurrentDate, getFutureDate } from "./useUtils";

export const useAppointments = () => {
  const queryClient = useQueryClient();

  // Function to fetch appointments within a date range
  const fetchAppointments = async (startDate: string = "2023-03-01", endDate: string = "2026-03-31") => {
    const response = await axios.get<Appointment[]>(`/appointments/?start_date=${startDate}&end_date=${endDate}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const createEvent = async (data: any) => {
    const response = await axios.post("/appointments/", data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Update appointment status
  const updateAppointmentStatus = async (id: string, status: "confirmed" | "canceled", cancellation_reason?: string) => {
    const data = status === "canceled" ? { status, cancellation_reason } : { status };
    const response = await axios.put(`/appointments/${id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Update appointment
  const updateAppointment = async (data: any) => {
    const response = await axios.put(`/appointments/${data.id}/`, data, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Delete appointment
  const deleteAppointment = async (id: string) => {
    const response = await axios.delete(`/appointments/${id}/`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  // Query to fetch appointments within a date range
  const { data: appointments, isLoading: isLoadingAppointments, refetch: refetchAppointments } = useQuery({
    queryKey: ["appointments"],
    queryFn: () => fetchAppointments(),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  // Mutation to create appointment
  const { mutate: createAppointment, isPending: isCreatingAppointment } = useMutation({
    mutationKey: ["create-appointment"],
    mutationFn: createEvent,
    onSuccess: () => {
      toast.success("Your appointment request has been submitted");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
  });

  // Add mutation for updating appointment status
  const { mutate: updateStatus, isPending: isUpdatingStatus } = useMutation({
    mutationKey: ["update-appointment-status"],
    mutationFn: ({ id, status, cancellation_reason }: { id: string; status: "confirmed" | "canceled"; cancellation_reason?: string }) =>
      updateAppointmentStatus(id, status, cancellation_reason),
    onSuccess: () => {
      toast.success("Appointment status updated successfully");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || "Failed to update appointment status");
    },
  });

  // Update appointment mutation
  const { mutate: updateAppointmentMutation, isPending: isUpdatingAppointment } = useMutation({
    mutationFn: updateAppointment,
    onSuccess: () => {
      toast.success("Appointment updated successfully");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || "Failed to update appointment");
    },
  });

  // Delete appointment mutation
  const { mutate: deleteAppointmentMutation, isPending: isDeletingAppointment } = useMutation({
    mutationFn: deleteAppointment,
    onSuccess: () => {
      toast.success("Appointment deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || "Failed to delete appointment");
    },
  });

  return {
    appointments,
    isLoadingAppointments,
    refetchAppointments,
    createAppointment,
    isCreatingAppointment,
    updateStatus,
    isUpdatingStatus,
    updateAppointmentMutation,
    isUpdatingAppointment,
    deleteAppointmentMutation,
    isDeletingAppointment,
  };
};
