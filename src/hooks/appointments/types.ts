// Type definitions for appointment hooks

// Type for the appointment availability response
export interface AppointmentAvailability {
  id?: string;
  doctor?: string;
  clinic?: string | null;
  enterprise?: string | null;
  title: string;
  meeting_link?: string | null;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  is_active: boolean;
  recurrence_type: "none" | "weekly" | "monthly";
  recurrence_interval?: number;
  recurrence_days?: string;
  recurrence_month_day?: number | null;
  recurrence_end_date?: string | null;
  recurrence_count?: number | null;
  status?: "pending" | "confirmed" | "canceled";
  mode?: string;
}

// Type for available slots response
export interface AvailableSlotResponse {
  date: string;
  day_of_week: string;
  total_slots: number;
  booked_slots: number;
  available_slots: number;
  slots: AvailableSlot[];
}

// Type for available days overview response
export interface AvailableDaysResponse {
  available_days: string[];
  unavailable_days: string[];
}

export interface AvailableSlot {
  start_time: string;
  end_time: string;
  availability_id: string;
  title: string;
  clinic_id: string | null;
  enterprise_id: string | null;
  recurrence_type: string;
  status: string;
  booking_info: any;
}

// Type for appointment data
export interface Appointment {
  id: string;
  appointment_type: string;
  start_time: string;
  end_time: string;
  title: string;
  notes?: string;
  location?: string;
  is_all_day: boolean;
  status?: string;
  mode?: string;
  meeting_link?: string | null;
  creator?: string;
  patient?: string;
  doctor?: string;
  qr_code?: string | null;
  google_event_id?: string | null;
  cancelled_by?: string | null;
  cancellation_reason?: string;
  attachments?: Array<{
    id: string;
    file: number;
    file_url: string;
    filename: string;
    description: string;
    created_at: string;
  }>;
}

export type DoctorContactInfo = {
  id?: string | undefined;
  primary_email: string;
  secondary_email: string;
  is_primary_email_active?: boolean;
  is_secondary_email_active?: boolean;
};

// Type for the form data that will be transformed
export interface WorkingHoursForm {
  applyToAll: boolean;
  globalFrom: string;
  globalTo: string;
  days: {
    [key: string]: {
      id?: string;
      enabled: boolean;
      times: Array<{
        id?: string;
        from: string;
        to: string;
      }>;
    };
  };
  slotDuration: string;
  breakTime: {
    from: string;
    to: string;
  };
}

// Type for my available slots response
export interface MyAvailableSlotsResponse {
  start_date: string;
  end_date: string;
  view_type: string;
  timezone: string;
  total_days: number;
  total_slots: number;
  total_available_slots: number;
  total_booked_slots: number;
  schedule: {
    date: string;
    day_of_week: string;
    slots: {
      start_time: string;
      end_time: string;
      availability_id: string;
      title: string;
      clinic_id: string | null;
      enterprise_id: string | null;
      recurrence_type: string;
      status: string;
      booking_info: {
        id: string;
        start_time: string;
        end_time: string;
        status: string;
        patient_id: string;
        patient_name: string;
        title: string;
        clinic_id: string | null;
        enterprise_id: string | null;
        appointment_type: string;
        mode: string;
      } | null;
    }[];
    total_slots: number;
    available_slots: number;
    booked_slots: number;
  }[];
}
