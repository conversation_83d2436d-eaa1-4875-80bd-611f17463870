"use client";
import axios from "@/services/api-client";
import { useQuery } from "@tanstack/react-query";
import { AvailableSlotResponse, AvailableDaysResponse, MyAvailableSlotsResponse } from "./types";
import { getCurrentDate, getFutureDate } from "./useUtils";

// Function to fetch available slots for a doctor on a specific date
const fetchAvailableSlots = async (doctorId: string, date: string): Promise<AvailableSlotResponse> => {
  console.log("Making API call for slots:", { doctorId, date });
  const response = await axios.get<AvailableSlotResponse>(`/appointments/availabilities/available_slots/?doctor_id=${doctorId}&date=${date}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("rT")}`,
    },
  });
  console.log("API Response:", response.data);
  return response.data;
};

// Function to fetch available days for a doctor within a date range
const fetchAvailableDays = async (doctorId: string, startDate: string, endDate: string): Promise<AvailableDaysResponse> => {
  const response = await axios.get<AvailableDaysResponse>(
    `/appointments/availabilities/available_slots/?doctor_id=${doctorId}&start_date=${startDate}&end_date=${endDate}`,
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    }
  );
  return response.data;
};

// Function to fetch my available slots within a date range
const fetchMyAvailableSlots = async (startDate: string, endDate: string): Promise<MyAvailableSlotsResponse> => {
  const response = await axios.get<MyAvailableSlotsResponse>(`/appointments/availabilities/my_available_slots/?start_date=${startDate}&end_date=${endDate}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("rT")}`,
    },
  });
  return response.data;
};

// Hook to fetch available slots
export const useAvailableSlots = (doctorId: string, date: string) => {
  return useQuery({
    queryKey: ["available-slots", doctorId, date],
    queryFn: () => fetchAvailableSlots(doctorId, date),
    enabled: !!doctorId && !!date,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });
};

// Hook to fetch available days for a month
export const useAvailableDays = (doctorId: string, year: number, month: number) => {
  // Calculate first and last day of the month
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  const startDate = firstDay.toISOString().split("T")[0];
  const endDate = lastDay.toISOString().split("T")[0];

  return useQuery({
    queryKey: ["doctor-available-days", doctorId, year, month],
    queryFn: () => fetchAvailableDays(doctorId, startDate, endDate),
    enabled: !!doctorId && year > 0 && month >= 0,
  });
};

// Hook to fetch available days for a date range
export const useFetchAvailableDays = (doctorId: string, startDate: string, endDate: string) => {
  return useQuery({
    queryKey: ["fetch-available-days", doctorId, startDate, endDate],
    queryFn: () => fetchAvailableDays(doctorId, startDate, endDate),
  });
};

// Hook to fetch my available slots
export const useMyAvailableSlots = () => {
  return useQuery({
    queryKey: ["my-available-slots"],
    queryFn: () => fetchMyAvailableSlots(getCurrentDate(), getFutureDate()),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });
};
