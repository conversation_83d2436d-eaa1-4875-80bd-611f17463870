// Re-export all appointment hooks
export * from "./types";
export * from "./useUtils";
export * from "./useAppointmentAvailability";
export * from "./useAppointments";
export * from "./useAvailableSlots";
export * from "./useDoctorContactInfo";
export * from "./compatibilityFunctions";

// Import all necessary hooks for the combined hook
import { useAppointmentAvailability } from "./useAppointmentAvailability";
import { useAppointments } from "./useAppointments";
import { useMyAvailableSlots } from "./useAvailableSlots";
import { useDoctorContactInfo } from "./useDoctorContactInfo";
import { fetchSlots, fetchDoctorAvailableDays } from "./compatibilityFunctions";

/**
 * Legacy combined hook that provides backward compatibility
 * with the original useAppointmentAvailability hook.
 *
 * New components should import specific hooks instead of this one.
 */
export const useAppointmentsLegacy = () => {
  const availability = useAppointmentAvailability();
  const appointments = useAppointments();
  const { data: myAvailableSlots, isLoading: isLoadingMySlots } = useMyAvailableSlots();
  const doctorInfo = useDoctorContactInfo();

  return {
    ...availability,
    ...appointments,
    ...doctorInfo,
    myAvailableSlots,
    isLoadingMySlots,
    // Re-export the compatibility functions
    fetchSlots,
    fetchDoctorAvailableDays,
  };
};

// Export legacy hook with its original name for backward compatibility
export { useAppointmentsLegacy as useAppointments };
