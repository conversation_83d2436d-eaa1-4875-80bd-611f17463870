"use client";
import { useAvailableSlots, useAvailableDays } from "./useAvailableSlots";

/**
 * Wrapper functions to maintain backward compatibility
 * with the original API. These functions allow direct invocation
 * in components.
 */

// Legacy version of fetchSlots as used in the original hook
export const fetchSlots = (doctorId: string, date: string) => {
  return useAvailableSlots(doctorId, date);
};

// Legacy version of fetchDoctorAvailableDays as used in the original hook
export const fetchDoctorAvailableDays = (doctorId: string, year: number, month: number) => {
  return useAvailableDays(doctorId, year, month);
};
