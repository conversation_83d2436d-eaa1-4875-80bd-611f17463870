"use client";
import { useState, useEffect } from "react";
import axios from "@/services/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { AdminFormType } from "@/lib/utils/validations";
export const useClinicSolution = () => {
  const queryClient = useQueryClient();

  const getClinicProfile = async (showToast = true) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get("clinic/profile/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (showToast) {
        toast.promise(Promise.resolve(response), {
          loading: "Fetching clinic profile...",
          success: "Clinic profile fetched successfully",
          error: "Failed to fetch clinic profile",
        });
      }
      queryClient.invalidateQueries({ queryKey: ["user"] });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to fetch clinic profile");
    }
  };

  const getClinicAdmin = async () => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.get("clinic/admin/", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to fetch enterprise admins");
    }
  };

  const updateClinicAdmin = async (data: AdminFormType) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const transformedData = data.admins.map((admin) => {
        if (!admin.id) {
          const { id, ...adminWithoutId } = admin;
          return adminWithoutId;
        }
        return admin;
      });

      const response = await axios.post("clinic/admin/", transformedData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to update clinic admin");
    }
  };

  const removeClinicAdmin = async (id: string) => {
    try {
      const token = localStorage.getItem("rT");
      if (!token) throw new Error("No authentication token found");

      const response = await axios.delete(`clinic/admin/?id=${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to delete enterprise admin");
    }
  };

  return {
    getClinicProfile,
    getClinicAdmin,
    updateClinicAdmin,
    removeClinicAdmin,
    queryClient,
  };
};

export const useGetClinicProfile = (showToast = true) => {
  const { getClinicProfile } = useClinicSolution();

  const { data, isLoading, error } = useQuery({
    queryKey: ["clinic-profile"],
    queryFn: () => getClinicProfile(showToast),
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  return { data, isLoading, error };
};

export const useGetClinicAdmin = () => {
  const { getClinicAdmin } = useClinicSolution();

  const { data, isLoading, error } = useQuery({
    queryKey: ["clinic-admin"],
    queryFn: getClinicAdmin,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  return { data, isLoading, error };
};

export const useUpdateClinicAdmin = () => {
  const { updateClinicAdmin, queryClient } = useClinicSolution();

  return useMutation({
    mutationFn: updateClinicAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clinic-admin"] });
    },
  });
};

export const useDeleteClinicAdmin = () => {
  const { removeClinicAdmin, queryClient } = useClinicSolution();

  return useMutation({
    mutationFn: removeClinicAdmin,
    onSuccess: () => {
      // Invalidate and refetch the admin list after successful deletion
      queryClient.invalidateQueries({ queryKey: ["clinic-admin"] });
    },
    onError: () => {
      toast.error("Failed to delete admin");
    },
  });
};
