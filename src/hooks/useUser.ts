"use client";
import axios from "@/services/api-client";
import { useStore } from "@/store/store";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

const useCommonFunctions = () => {
  const { setUser } = useStore();
  const router = useRouter();
  const queryClient = useQueryClient();

  const getUser = async () => {
    try {
      const response = await axios.get<User>("roles/profile");
      useStore.setState({ user: response.data });
      return response.data;
    } catch (error) {
      console.error("Fetching user failed:", error);
      throw error;
    }
  };

  const registerUser = async ({ email, password }: { email: string; password: string }) => {
    const response = await axios.post("/register/", {
      email,
      password,
      is_clinic_signup: false,
      is_enterprise_signup: false,
    });
    return response.data;
  };

  const loginUser = async ({ email, password }: { email: string; password: string }) => {
    queryClient.setQueryData(["user"], undefined);

    try {
      const response = await axios.post("/login/", {
        email,
        password,
      });

      if (response.data) {
        const { access_token, refresh_token, user_id } = response.data;
        localStorage.setItem("rT", access_token);
        localStorage.setItem("rrT", refresh_token);
        setUser(response.data);
        return response.data;
      }
    } catch (err) {
      toast.error("Login failed");
    }
  };

  const updateUser = async (data: any) => {
    const response = await axios.post("/roles/profile/", data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${localStorage.getItem("rT")}`,
      },
    });
    return response.data;
  };

  const updateProfileImages = async (data: any) => {
    let response: any;
    if (data instanceof FormData) {
      response = await axios.post("/roles/profile/", data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
          "Content-Type": "multipart/form-data",
        },
      });
    }
    return response.data;
  };

  const updateSecondaryEmail = async (email: string) => {
    const response = await axios.post(
      "/change-second-email/",
      { second_email: email },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
        },
      }
    );
    return response.data;
  };

  const logoutUser = async () => {
    const response = await axios.post(
      "/logout/",
      {},
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("rT")}`,
        },
      }
    );
    toast.promise(Promise.resolve(response), {
      loading: "Logging out...",
      success: "Logged out successfully",
      error: "Logout failed",
    });
    localStorage.removeItem("rT");
    localStorage.removeItem("rrT");
    localStorage.removeItem("store");
    localStorage.removeItem("default_user");
    localStorage.removeItem("user2_email");
    localStorage.removeItem("user2_name");
    localStorage.removeItem("shared_token");
    setUser(null);
    queryClient.clear();
  };

  return {
    getUser,
    registerUser,
    loginUser,
    updateUser,
    logoutUser,
    router,
    queryClient,
    setUser,
    updateProfileImages,
    updateSecondaryEmail,
  };
};

export const useRegister = () => {
  const { registerUser } = useCommonFunctions();

  return useMutation({
    mutationKey: ["register-user"],
    mutationFn: registerUser,
  });
};

export const useLogin = () => {
  const { loginUser, router, queryClient } = useCommonFunctions();

  return useMutation({
    mutationKey: ["login-user"],
    mutationFn: loginUser,
    onSuccess: async (data) => {
      toast.success("Logged in successfully");

      // Check for pending enterprise invite
      const pendingInviteToken = localStorage.getItem("pending_enterprise_invite");
      if (pendingInviteToken) {
        // Clear both tokens
        localStorage.removeItem("pending_enterprise_invite");
        localStorage.removeItem("processed_invite_token");
        // Redirect to the invite accept page with the token
        router.push(`/enterprise/invite/accept?token=${pendingInviteToken}`);
      } else {
        // Normal login flow
        router.push(`/my/${data.user_id}/agent`);
      }

      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
    onError: (error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : "Login failed";
      toast.error(errorMessage);
    },
  });
};

export const useGetUser = () => {
  const { getUser } = useCommonFunctions();

  const { data, isLoading, isError } = useQuery({
    queryKey: ["user"],
    queryFn: getUser,
    refetchOnMount: true,
    retry: false,
  });

  return { data, isLoading, isError };
};

export const useUpdateUser = () => {
  const { updateUser, queryClient } = useCommonFunctions();

  return useMutation({
    mutationKey: ["update-user"],
    mutationFn: updateUser,

    onSuccess: () => {
      toast.success("User updated successfully");
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
    onError: (error) => {
      toast.error(error.message || "User update failed");
    },
  });
};

export const useUpdateProfileImages = () => {
  const { updateProfileImages, updateSecondaryEmail, queryClient } = useCommonFunctions();

  return useMutation({
    mutationKey: ["update-profile-images"],
    mutationFn: updateProfileImages,
    onSuccess: () => {
      toast.success("Profile images updated successfully");
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
    onError: (error) => {
      toast.error(error.message || "Profile images update failed");
    },
  });
};

export const useUpdateSecondaryEmail = () => {
  const { updateSecondaryEmail } = useCommonFunctions();

  return useMutation({
    mutationKey: ["update-secondary-email"],
    mutationFn: updateSecondaryEmail,
    onSuccess: () => {
      toast.success("Secondary email updated successfully");
    },
    onError: (error) => {
      toast.error(error.message || "Secondary email update failed");
    },
  });
};

export const useLogout = () => {
  const { logoutUser, router, queryClient } = useCommonFunctions();

  return useMutation({
    mutationKey: ["logout"],
    mutationFn: logoutUser,
    onSuccess: () => {
      router.push("/signin");
      queryClient.invalidateQueries({ queryKey: ["user", "enterprise-profile"] });
    },
  });
};
