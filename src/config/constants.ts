export const bloodTypes = [
  { key: "A+", label: "A+" },
  { key: "A-", label: "A-" },
  { key: "B+", label: "B+" },
  { key: "B-", label: "B-" },
  { key: "AB+", label: "AB+" },
  { key: "AB-", label: "AB-" },
  { key: "O+", label: "O+" },
  { key: "O-", label: "O-" },
  { key: "Z", label: "Z" },
];
export const genderOptions = [
  { key: "male", label: "Male (xy)" },
  { key: "female", label: "Female (xx)" },
];

export const yesNoOptions = [
  { key: "true", label: "Yes" },
  { key: "false", label: "No" },
];

export const languages = [
  { key: "en", label: "English" },
  { key: "ar", label: "العربية" },
  { key: "vi", label: "Tiếng Việt" },
  { key: "th", label: "ไทย" },
  { key: "sw", label: "Swahili" },
  { key: "hi", label: "Hindi" },
];

export const insuranceTypes = [
  { key: "health", label: "Health Insurance" },
  { key: "dental", label: "Dental Insurance" },
  { key: "vision", label: "Vision Insurance" },
  { key: "other", label: "Other Insurance" },
];

export const dependentOptions = [
  { key: "self", label: "Self" },
  { key: "parents", label: "Parents" },
  { key: "spouse", label: "Spouse" },
  { key: "partner", label: "Partner" },
  { key: "group", label: "Group" },
];

export const geneticData = {
  totalVariants: 500,
  rareVariants: 121,
  pathogenicVariants: 45,
  topDiseases: ["Age related cataract", "Adult onset diabetes", "Wolfram Syndrome", "Wolfram Syndrome Type 1", "ADNSHL Type 6"],
};

export const predictiveData = [
  { name: "Heterozygous (Pathogenic)", value: 70 },
  { name: "Homozygous Alt (Pathogenic)", value: 25 },
];

export const communicationTypes = [
  {
    key: "sms",
    label: "Service expiration reminders via SMS",
    description: "By enabling, you consent to phone number use for sending reminders as per our Privacy Policy. You can withdraw consent anytime.",
  },
  {
    key: "whatsapp",
    label: "Service expiration reminders via WhatsApp",
    description: "By enabling, you consent to phone number use for sending reminders as per our Privacy Policy. You can withdraw consent anytime.",
  },
  {
    key: "email",
    label: "Account Updates & Special Offers via Email",
    description:
      "By enabling your account, you are hereby consenting to receive information about your Account and be the first to know about our latest features and special offers.",
  },
];

// language spoken
export const languageSpokenOptions = [
  { key: "en", label: "English" },
  { key: "ar", label: "العربية" },
  { key: "vi", label: "Tiếng Việt" },
  { key: "es", label: "Español" },
  { key: "th", label: "ไทย" },
];

export const DAYS = [
  { key: "MON", label: "MON" },
  { key: "TUE", label: "TUE" },
  { key: "WED", label: "WED" },
  { key: "THU", label: "THU" },
  { key: "FRI", label: "FRI" },
  { key: "SAT", label: "SAT" },
  { key: "SUN", label: "SUN" },
];

export const ads = [
  {
    title: "Explore Genomics",
    description: "From Advance Genetic Carrier Screening to Whole Exome Sequencing (WES) explore various Packages that best suit your needs",
    image: "/images/add.png",
    service: "Genomic Services",
    banner: {
      text: "Promoted Service",
      type: "service",
    },
  },
  {
    title: "Explore Multidisciplinary",
    description: "Explore Personalized Multidisciplinary Health Checkups to Comprehensive Kidney Health Assessments & more",
    image: "/images/addTwo.png",
    service: "Multidisciplinary",
    banner: {
      text: "Promoted Service",
      type: "service",
    },
  },
  {
    title: "Dr. John Doe",
    description: "Dr. John is a dedicated orthodontist with over 10 years of experience in creating confident, healthy smiles. Specializing in braces....",
    image: "/images/addThree.png",
    service: "Longevity & Wellness",
    banner: {
      text: "Promoted Profile",
      type: "profile",
    },
  },
];

export const upcomingAppointments = [
  {
    title: "Mom's Neurology Appointment",
    doctor: "Dr. Sarah Johnson",
    time: "Today at 10:00 AM",
  },
  {
    title: "Annual Physical Checkup",
    doctor: "Dr. Michael Chen",
    time: "Jan 25, 2024 at 2:30 PM",
  },
];

export const upcomingFeatures = [
  { title: "Calendar sync across devices" },
  { title: "SMS appointment reminders" },
  { title: "Family appointment management" },
  { title: "Telehealth integration" },
  { title: "Prescription reminders" },
  { title: "Health record sharing" },
];

export const qrDisclaimer =
  "Disclaimer: Information captured in this QR code is generated from the last server saved content that resides within the Personal Info, Emergency Info & Medical Team section of your R.A.V.I.D. account, in addition to your personalized UID number. Please exercise care & caution with whoever you wish to share your valuable information.";

export const yearlyPlans = [
  {
    id: "yearly-plan-1",
    name: "AI Agent Integration Yearly Subscription",
    description: "Full monthly access to AI Analysis. Applicable throughout the R.A.V.I.D. platform. Daily Token Limits for AI Agent",
    short_name: "basic-yearly",
    price: "xyz",
    features: ["Full monthly access to AI Analysis.", "Applicable throughout the R.A.V.I.D.", "Daily Token Limits for AI Agent"],
  },
  {
    id: "yearly-plan-2",
    name: "DNA Analysis",
    description: "All Basic features plus advanced analytics and tools.",
    short_name: "dna-pro-yearly",
    price: "xyz",
    features: ["Advanced genomic variant analysis", "GPU-powered processing", "Comprehensive DNA sequencing"],
  },
  {
    id: "yearly-plan-4",
    name: "Verification ",
    description: "Complete Genome analysis. Advanced variant calling. Premium support.",
    short_name: "premium-genomics-yearly",
    price: "xyz",
    features: ["Verified Mark on QR Code retrieval", "Enhanced credibility", "Identity verification"],
  },
];

export const services = [
  {
    title: "Whole Genome Sequencing Analysis (WGS)",
    benefits: ["Some Text goes here -->", "Some Text goes here -->", "Some Text goes here -->"],
    price: 400,
  },
  {
    title: "Cancer Screening Analysis (CSA)",
    benefits: ["Some Text goes here -->", "Some Text goes here -->", "Some Text goes here -->"],
    price: 400,
  },
  {
    title: "Pharmacogenomic Analysis (PGxA)",
    benefits: ["Some Text goes here -->", "Some Text goes here -->", "Some Text goes here -->"],
    price: 400,
  },
];

// mobile health
// Sample data for sleep quality
export const sleepData = [
  { month: "Mon", sleep: 2 },
  { month: "Tue", sleep: 4 },
  { month: "Wed", sleep: 3 },
  { month: "Thu", sleep: 6 },
  { month: "Fri", sleep: 5 },
  { month: "Sat", sleep: 4 },
  { month: "Sun", sleep: 7 },
];

// Add sample data for new graphs
export const activeHoursData = [
  { month: "Mon", value: 5 },
  { month: "Tue", value: 5.2 },
  { month: "Wed", value: 5 },
  { month: "Thu", value: 5.5 },
  { month: "Fri", value: 6.3 },
  { month: "Sat", value: 4 },
];

export const stressData = [
  { time: "04:00", value: 35 },
  { time: "08:00", value: 40 },
  { time: "12:00", value: 30 },
  { time: "16:00", value: 35 },
  { time: "20:00", value: 34 },
  { time: "24:00", value: 38 },
  { time: "03:59", value: 32 },
];

// medical team
export const defaultPractitioners = [
  {
    id: undefined,
    name: "",
    email: "",
    contact_number: "",
    affiliation: "",
    role: "Primary Physician",
  },
  {
    id: undefined,
    name: "",
    email: "",
    contact_number: "",
    affiliation: "",
    role: "Geneticist",
  },
  {
    id: undefined,
    name: "",
    email: "",
    contact_number: "",
    affiliation: "",
    role: "Dentist",
  },
  {
    id: undefined,
    name: "",
    email: "",
    contact_number: "",
    affiliation: "",
    role: "Ophthalmologist",
  },
];

// Define a type for the user object to make it more type-safe
export type UserData = {
  user_id?: string;
  cuid?: string;
  euid?: string;
  [key: string]: any;
};

// New dashboard and quick links
export const getDashboardLinks = (user: UserData | null | undefined, t: (key: string) => string) => [
  ...(user?.cuid
    ? [
        { href: `/my/cs/${user.cuid}/edit`, text: t("ravid.sideBar.myCLinicProfile") },
        { href: `/my/cs/${user.cuid}`, text: t("ravid.sideBar.myClinicSettings") },
      ]
    : []),
  ...(user?.euid
    ? [
        { href: `/my/es/${user.euid}/`, text: t("ravid.sideBar.companyDashboard") },
        { href: `/my/es/${user.euid}/edit`, text: t("ravid.sideBar.companySettings") },
        { href: `/my/es/${user.euid}/subscription`, text: t("ravid.sideBar.companySubscription") },
      ]
    : []),
  { href: `/my/${user?.user_id}/home`, text: t("ravid.sideBar.myRavidDashboard") },
  { href: `/my/${user?.user_id}/edit`, text: t("ravid.sideBar.myRavidSettings") },
  { href: `/my/${user?.user_id}/subscriptions`, text: t("ravid.sideBar.mySubscriptions") },
  { href: `/my/${user?.user_id}`, text: t("ravid.sideBar.myPublicProfile") },
  { href: `/my/dna/${user?.user_id}`, text: t("ravid.sideBar.myDna") },
  { href: `/my/permissions`, text: t("ravid.sideBar.permissions") },
];

export const getQuickLinks = (t: (key: string) => string) => [
  { href: "/about", text: t("ravid.nav.about") },
  { href: "/collaborate", text: t("ravid.nav.collaborate") },
  { href: "/communities", text: t("ravid.nav.communities") },
  { href: "/solutions", text: t("ravid.nav.solutions") },
];

// Staff users for the ManageUsersTab
export const staffUsers = [
  {
    id: "1",
    name: "Dr. Sarah Johnson",
    specialty: "Cardiologist",
    verified: true,
    image: "/images/avatar-placeholder.png",
  },
];

// Sidebar Items Constants
export const SIDEBAR_ITEMS = {
  MY_CLINIC_SOLUTIONS: "ravid.sideBar.myClinicSolutions",
  MY_CLINIC_SETTINGS: "ravid.sideBar.myClinicSettings",
  MY_CLINIC_PROFILE: "ravid.sideBar.myCLinicProfile",
  ENTERPRISE_SOLUTIONS: "ravid.sideBar.enterpriseSolutions",
  COMPANY_DASHBOARD: "ravid.sideBar.companyDashboard",
  COMPANY_SETTINGS: "ravid.sideBar.companySettings",
  COMPANY_SUBSCRIPTION: "ravid.sideBar.companySubscription",
  GENOMICS_DEPARTMENT: "ravid.sideBar.genomicsDepartment",
  MY_DASHBOARD: "ravid.sideBar.myDashboard",
  MY_SETTINGS: "ravid.sideBar.mySettings",
  MY_SUBSCRIPTIONS: "ravid.sideBar.mySubscriptions",
  MY_PUBLIC_PROFILE: "ravid.sideBar.myPublicProfile",
  EDIT_PUBLIC_PROFILE: "ravid.publicProfile.published.editPublicProfile",
  MESSAGES: "ravid.publicProfile.messages",
  NOTIFICATIONS: "ravid.publicProfile.notifications",
  PAYMENTS: "ravid.publicProfile.payments",
  APPOINTMENT_MANAGER: "ravid.publicProfile.appointments",
  ADMINISTRATION_MANAGER: "ravid.publicProfile.administrationManager",
  PAYMENT_MANAGER: "ravid.publicProfile.paymentManager",
  TELEMEDICINE: "ravid.publicProfile.telemedicine",
  DONATE: "ravid.publicProfile.donate",
  MY_PERMISSIONS: "ravid.sideBar.myPermissions",
  MY_DNA: "ravid.sideBar.myDna",
  SIGN_OUT: "ravid.sideBar.signOut",
  MY_AI_AGENT: "ravid.sideBar.myAiAgent",
};

// Navigation Types
export type NavItem = {
  id: string;
  label: string;
  href?: string;
  icon?: React.ElementType;
  color?: string;
  separator?: boolean;
  showInFooter?: boolean;
  disabled?: boolean;
};

export type AccordionSection = {
  id: string;
  title: string;
  icon?: React.ElementType;
  showInFooter?: boolean;
  items: NavItem[];
};

// Centralized Navigation Structure
export const getNavigationStructure = (user: UserData | null | undefined, t: (key: string) => string) => {
  // Accordion Sections first (as shown in image)
  const accordionSections: AccordionSection[] = [
    ...(user?.cuid
      ? [
          {
            id: "clinic-solutions",
            title: t(SIDEBAR_ITEMS.MY_CLINIC_SOLUTIONS),
            showInFooter: true,
            items: [
              {
                id: "clinic-settings",
                label: t(SIDEBAR_ITEMS.MY_CLINIC_SETTINGS),
                href: `/my/cs/${user?.cuid}/edit`,
                showInFooter: true,
              },
              {
                id: "clinic-profile",
                label: t(SIDEBAR_ITEMS.MY_CLINIC_PROFILE),
                href: `/my/cs/${user?.cuid}`,
                showInFooter: true,
              },
            ],
          },
        ]
      : []),
    ...(user?.euid && user?.enterprise_member_role === "owner"
      ? [
          {
            id: "enterprise-solutions",
            title: t(SIDEBAR_ITEMS.ENTERPRISE_SOLUTIONS),
            showInFooter: true,
            items: [
              {
                id: "company-dashboard",
                label: t(SIDEBAR_ITEMS.COMPANY_DASHBOARD),
                href: `/my/es/${user?.euid}`,
                showInFooter: true,
              },
              {
                id: "company-settings",
                label: t(SIDEBAR_ITEMS.COMPANY_SETTINGS),
                href: `/my/es/${user?.euid}/edit`,
                showInFooter: true,
              },
              {
                id: "company-subscription",
                label: t(SIDEBAR_ITEMS.COMPANY_SUBSCRIPTION),
                href: `/my/es/${user?.euid}/subscription`,
                showInFooter: true,
              },
              {
                id: "genomics-department",
                label: t(SIDEBAR_ITEMS.GENOMICS_DEPARTMENT),
                href: `/my/es/${user?.euid}/genomics`,
                showInFooter: true,
              },
            ],
          },
        ]
      : []),
  ];

  // Main navigation items in correct order
  const mainNavItems: NavItem[] = [
    {
      id: "agent",
      label: t(SIDEBAR_ITEMS.MY_AI_AGENT),
      href: `/my/${user?.user_id}/agent`,
      showInFooter: true,
    },
    {
      id: "dna",
      label: t(SIDEBAR_ITEMS.MY_DNA),
      href: `/my/dna/${user?.user_id}`,
      showInFooter: true,
    },
    {
      id: "dashboard",
      label: t(SIDEBAR_ITEMS.MY_DASHBOARD),
      href: `/my/${user?.user_id}/home`,
      showInFooter: true,
    },
    {
      id: "edit-profile",
      label: t(SIDEBAR_ITEMS.MY_PUBLIC_PROFILE),
      href: `/my/${user?.user_id}`,
      showInFooter: true,
      separator: true,
    },
  ];

  // Public Profile Accordion Section
  // const publicProfileSection: AccordionSection = {
  //   id: "public-profile",
  //   title: t(SIDEBAR_ITEMS.MY_PUBLIC_PROFILE),
  //   showInFooter: true,
  //   items: [
  //     {
  //       id: "edit-profile",
  //       label: t(SIDEBAR_ITEMS.EDIT_PUBLIC_PROFILE),
  //       href: `/my/${user?.user_id}`,
  //       showInFooter: true,
  //       separator: true,
  //     },
  //     {
  //       id: "appointments",
  //       label: t(SIDEBAR_ITEMS.APPOINTMENT_MANAGER),
  //       href: `/my/${user?.user_id}/appointments?tab=appointmentSchedule`,
  //       showInFooter: true,
  //     },
  //     {
  //       id: "donate",
  //       label: t(SIDEBAR_ITEMS.DONATE),
  //       // href: `/my/${user?.user_id}/appointments?tab=appointmentSchedule`,
  //       showInFooter: true,
  //     },
  //     {
  //       id: "messages",
  //       label: t(SIDEBAR_ITEMS.MESSAGES),
  //       href: `/my/${user?.user_id}/message`,
  //       showInFooter: true,
  //     },
  //     {
  //       id: "notifications",
  //       label: t(SIDEBAR_ITEMS.NOTIFICATIONS),
  //       href: `/my/${user?.user_id}/notifications`,
  //       showInFooter: true,
  //     },
  //     {
  //       id: "payments",
  //       label: t(SIDEBAR_ITEMS.PAYMENTS),
  //       href: `/my/${user?.user_id}/payments`,
  //       showInFooter: true,
  //     },
  //     {
  //       id: "telemedicine",
  //       label: t(SIDEBAR_ITEMS.TELEMEDICINE),
  //       href: `/my/${user?.user_id}/telemedicine`,
  //       showInFooter: true,
  //     },
  //   ],
  // };

  // Additional navigation items (Permissions and Settings)
  const additionalNavItems: NavItem[] = [
    {
      id: "permissions",
      label: t(SIDEBAR_ITEMS.MY_PERMISSIONS),
      href: "/my/permissions",
      separator: true,
      showInFooter: true,
    },
    {
      id: "settings",
      label: t(SIDEBAR_ITEMS.MY_SETTINGS),
      href: `/my/${user?.user_id}/edit`,
      showInFooter: true,
    },
  ];

  const quickLinks: NavItem[] = [
    { id: "about", label: t("ravid.nav.about"), href: "/about", showInFooter: true },
    { id: "collaborate", label: t("ravid.nav.collaborate"), href: "/collaborate", showInFooter: true },
    { id: "communities", label: t("ravid.nav.communities"), href: "/communities", showInFooter: true },
    { id: "solutions", label: t("ravid.nav.solutions"), href: "/solutions", showInFooter: true },
  ];

  // Combine all accordion sections in the correct order
  const allAccordionSections = [...accordionSections];

  return {
    mainNavItems,
    accordionSections: allAccordionSections,
    additionalNavItems,
    quickLinks,
  };
};

export const staticDNAFiles = [
  {
    id: 1,
    fileName: "john_doe_234657_r1.fastq",
    date: "2025-04-10, 11:09 AM",
    size: "40GB",
    selected: false,
    ai: false,
  },
  {
    id: 2,
    fileName: "john_doe_234657_r2.fastq",
    date: "2025-04-10, 11:09 AM",
    size: "40GB",
    selected: false,
    ai: false,
  },
  {
    id: 9,
    fileName: "sample_wgs_001_r1.fq.gz",
    date: "2025-04-14, 2:15 PM",
    size: "35GB",
    selected: false,
    ai: false,
  },
  {
    id: 10,
    fileName: "sample_wgs_001_r2.fq.gz",
    date: "2025-04-14, 2:15 PM",
    size: "35GB",
    selected: false,
    ai: false,
  },
  {
    id: 11,
    fileName: "test_sample_r1.fq",
    date: "2025-04-14, 3:30 PM",
    size: "42GB",
    selected: false,
    ai: false,
  },
  {
    id: 12,
    fileName: "test_sample_r2.fq",
    date: "2025-04-14, 3:30 PM",
    size: "42GB",
    selected: false,
    ai: false,
  },
  {
    id: 3,
    fileName: "john_doe_234657.bam",
    date: "2025-04-10, 11:09 AM",
    size: "80GB",
    selected: false,
    ai: false,
  },
  {
    id: 4,
    fileName: "john_doe_234657.vcf",
    date: "2025-04-12, 11:09 PM",
    size: "5GB",
    selected: false,
    ai: false,
  },
  {
    id: 6,
    fileName: "R.A.V.I.D. Generated_36328.json",
    date: "2025-04-13, 12:19 PM",
    size: "689KB",
    selected: false,
    ai: true,
  },
  {
    id: 7,
    fileName: "R.A.V.I.D. Generated_363568.json",
    date: "2025-04-13, 12:35 PM",
    size: "20KB",
    selected: false,
    ai: true,
  },
  {
    id: 8,
    fileName: "R.A.V.I.D. Generated_363568.pdf",
    date: "2025-04-13, 12:37 PM",
    size: "6MB",
    selected: false,
    ai: true,
  },
];
