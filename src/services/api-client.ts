import axios, { Axios<PERSON>rror, InternalAxiosRequestConfig } from "axios";
import { useStore } from "@/store/store";

// Define a custom interface that extends InternalAxiosRequestConfig
interface RetryableRequest extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

// Only add token-related interceptors on the client side
if (typeof window !== "undefined") {
  // Request interceptor to add auth token to requests
  instance.interceptors.request.use(
    (config) => {
      const store = useStore.getState();
      const shared_token = localStorage.getItem("shared_token");
      if (shared_token && store.isShared) {
        // Initialize params object if it doesn't exist
        config.params = config.params || {};

        // Add shared_token to the query parameters
        config.params.shared_token = shared_token;
      } else {
        // Only add Authorization header if not in user2 mode
        const token = localStorage.getItem("rT");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor to handle token refresh
  instance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as RetryableRequest | undefined;

      // If error is 401 and we haven't tried refreshing yet
      if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          // Try to refresh the token
          const refreshToken = localStorage.getItem("rrT");
          const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}token/refresh/`, { refresh: refreshToken });

          const newAccessToken = response.data.access;

          // Save the new token
          localStorage.setItem("rT", newAccessToken);

          // Update the original request with new token
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
          }

          // Retry the original request
          return instance(originalRequest);
        } catch (refreshError) {
          // If refresh fails, clear tokens and reject
          localStorage.removeItem("rT");
          localStorage.removeItem("rrT");
          return Promise.reject(refreshError);
        }
      }

      return Promise.reject(error);
    }
  );
}

export default instance;
