import { create } from "zustand";

// Define types for chart data
export interface ChartDataItem {
  name: string;
  value: number;
  [key: string]: any; // Allow additional properties
}

export interface ChartDataRegistry {
  [chartId: string]: {
    data: ChartDataItem[];
    title: string;
    type: string;
  };
}

interface ChartDataStore {
  chartDataStore: any;
  aiChatOpen: boolean;
  setChartDataStore: (chartData: any[]) => void;
  openAiChat: (chartData: any[]) => void;
  closeAiChat: () => void;
}

export const useChartDataStore = create<ChartDataStore>((set, get) => ({
  chartDataStore: [],
  aiChatOpen: false,
  setChartDataStore: (chartData: any[]) => {
    set({ chartDataStore: chartData });
  },
  openAiChat: (chartData: any[]) => {
    set({ aiChatOpen: true });
    set({ chartDataStore: chartData });
  },
  closeAiChat: () => {
    set({ aiChatOpen: false });
    set({ chartDataStore: [] });
  },
}));
