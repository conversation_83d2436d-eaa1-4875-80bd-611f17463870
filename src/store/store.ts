import { create } from "zustand";
import { persist } from "zustand/middleware";

type StoreState = {
  user: any;
  user2: any;
  isShared: boolean;
  setUser: (user: any) => void;
  setUser2: (user2: any) => void;
  setIsShared: (isShared: boolean) => void;
  language: string;
  setLanguage: (language: string) => void;
  showVerifyModal: boolean;
  setShowVerifyModal: (showVerifyModal: boolean) => void;
};

export const useStore = create<StoreState>()(
  persist(
    (set) => ({
      user: null,
      setUser: (user: any) => set({ user }),
      user2: null,
      setUser2: (user2: any) => set({ user2 }),
      isShared: false,
      setIsShared: (isShared: boolean) => set({ isShared }),
      language: typeof window !== "undefined" ? localStorage.getItem("ln") || "en" : "en",
      setLanguage: (language: string) => {
        set({ language });
        if (typeof window !== "undefined") {
          localStorage.setItem("ln", language);
        }
      },
      showVerifyModal: false,
      setShowVerifyModal: (showVerifyModal: boolean) => set({ showVerifyModal }),
    }),
    { name: "store" }
  )
);
