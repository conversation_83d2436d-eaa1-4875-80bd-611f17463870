/**
 * Converts an image source (URL or data URI) to a base64 string
 * @param imageSrc - The image source (URL or data URI)
 * @returns Promise that resolves to a base64 string (without data URI prefix)
 */
export const convertImageToBase64 = async (imageSrc: string): Promise<string> => {
  try {
    // If it's already a data URI, extract the base64 part
    if (imageSrc.startsWith("data:")) {
      const base64Data = imageSrc.split(",")[1];
      return base64Data || "";
    }

    // If it's a URL, fetch and convert to base64
    const response = await fetch(imageSrc);
    const blob = await response.blob();

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        // Extract just the base64 part (after the comma)
        const base64Data = result.split(",")[1];
        resolve(base64Data || "");
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error("Error converting image to base64:", error);
    return "";
  }
};
