import { nullable, z } from "zod";

export const RegisterFormValidation = z
  .object({
    email: z.string().email(),
    password: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    confirmPassword: z.string(),
    is_clinic_signup: z.boolean().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"], // path of error
  });

// Example usage:
export type RegisterFormType = z.infer<typeof RegisterFormValidation>;

export const LoginFormValidation = z.object({
  email: z.string().email(),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters.",
  }),
});

export type LoginFormType = z.infer<typeof LoginFormValidation>;

export const PersonalFormValidation = z.object({
  first_name: z.string().optional(),
  email: z
    .string()
    .email()
    .optional(),
  middle_name: z.string().optional(),
  last_name: z.string().optional(),
  dob: z.string().optional(),
  blood_group: z.string().optional(),
  gender: z.string().optional(),
  genome_tested: z.boolean().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  zipcode: z.string().optional(),
  mobile: z.string().optional(),
  language: z.string().optional(),
  private_profile_picture: z.string().optional(),
});

export type PersonalFormType = z.infer<typeof PersonalFormValidation>;

export const InsuranceFormValidation = z.object({
  id: z.string().optional(),
  provider: z.string().optional(),
  policy_number: z.string().optional(),
  type: z.string().optional(),
  group_number: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  policy_holder_name: z.string().optional(),
  dependent_information: z.string().optional(),
  deductable_amount: z.string().optional(),
  copay_amount: z.string().optional(),
});

export type InsuranceFormType = z.infer<typeof InsuranceFormValidation>;

export const NoteFormValidation = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
});

export type NoteFormType = z.infer<typeof NoteFormValidation>;

export const EnterpriseFormValidation = z.object({
  id: z.number().optional(),
  unique_identifier: z.string().optional(),
  identification_number: z.string().optional(),
  admin_email: z
    .string()
    .email()
    .nullable()
    .optional(),
  name: z
    .string()
    .nullable()
    .optional(),
  about: z
    .string()
    .nullable()
    .optional(),
  contact_number: z
    .string()
    .nullable()
    .optional(),
  // additional_email: z.string().email().nullable().optional().or(z.literal("")),
  language_spoken: z
    .string()
    .nullable()
    .optional(),
  insurance_accepted: z.boolean().optional(),
  logo: z
    .string()
    .nullable()
    .optional(),
  logo_file: z
    .string()
    .nullable()
    .optional(),
  location: z
    .object({
      id: z
        .number()
        .nullable()
        .optional(),
      address: z
        .string()
        .nullable()
        .optional(),
      city: z
        .string()
        .nullable()
        .optional(),
      state: z
        .string()
        .nullable()
        .optional(),
      zip_code: z
        .string()
        .nullable()
        .optional(),
      country: z
        .string()
        .nullable()
        .optional(),
    })
    .nullable()
    .optional(),
  services: z.union([z.tuple([]), z.null()]).optional(),
  doctors: z.union([z.tuple([]), z.null()]).optional(),
  operating_hours: z.union([z.tuple([]), z.null()]).optional(),
  departments: z.union([z.tuple([]), z.null()]).optional(),
  enterprise_department_doctors: z.union([z.tuple([]), z.null()]).optional(),
  enterprise_images: z.union([z.tuple([]), z.null()]).optional(),
  is_enterprise_public: z.boolean().optional(),
  date_of_incorporation: z
    .string()
    .nullable()
    .optional(),
  state_of_incorporation: z.string().optional(),
  website: z
    .string()
    .nullable()
    .optional(),
});

export type EnterpriseFormType = z.infer<typeof EnterpriseFormValidation>;

export const AdminFormValidation = z.object({
  admins: z.array(
    z.object({
      id: z.string().optional(),
      name: z.string().optional(),
      email: z.string().optional(),
      phone_number: z.string().optional(),
      position: z.string().optional(),
    })
  ),
});

export type AdminFormType = z.infer<typeof AdminFormValidation>;

// medical team form validation
export const practionerValidation = z.object({
  name: z.string().optional(),
  email: z.string().optional(),
  contact_number: z.string().optional(),
  affiliation: z.string().optional(),
  role: z.string().optional(),
  id: z.string().optional(),
});
export type PractitionerFormValues = z.infer<typeof practionerValidation>;

// New schema for role-based medical team structure
export const roleMedicalTeamSchema = z.record(
  z.string(), // Role as key (e.g., "Primary Physician", "Ophthalmologist")
  practionerValidation
);
export type RoleMedicalTeamFormValues = z.infer<typeof roleMedicalTeamSchema>;

// clinic form validation
export const ClinicFormValidation = z.object({
  name: z.string().optional(),
  license_number: z.string().optional(),
  language_spoken: z.string().optional(),
  date_of_incorporation: z.string().optional(),
  additional_email: z.string().optional(),
  insurance_accepted: z.boolean().optional(),
  contact_number: z.string().optional(),
  email: z.string().optional(),
  location: z.object({
    city: z.string().optional(),
    zip_code: z.string().optional(),
    address: z.string().optional(),
    country: z.string().optional(),
  }),
  logo: z
    .string()
    .optional()
    .nullable(),
  about: z.string().optional(),
});

export type ClinicFormType = z.infer<typeof ClinicFormValidation>;

export const AppointmentFormValidation = z.object({
  title: z.string().min(1, "Title is required"),
  notes: z.string().optional(),
  startTime: z.string().min(1, "Start time is required"),
  endTime: z.string().min(1, "End time is required"),
});

export type AppointmentFormType = z.infer<typeof AppointmentFormValidation>;
