{"ravid": {"buttons": {"save": "<PERSON><PERSON><PERSON>", "saving": "Inahifadhi...", "saveChanges": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "add": "Ongeza", "close": "Funga", "back": "<PERSON><PERSON>"}, "mySubscriptionDashboard": {"tabs": {"billings": "<PERSON><PERSON>", "services": "<PERSON><PERSON><PERSON>", "subscriptions": "<PERSON><PERSON><PERSON>"}}, "publicProfile": {"liveVersion": "<PERSON><PERSON>", "unPublish": "<PERSON><PERSON><PERSON>", "verify": "<PERSON><PERSON><PERSON><PERSON>", "getVerifiedProfile": "<PERSON><PERSON> <PERSON><PERSON>", "publishProfile": "<PERSON><PERSON><PERSON>", "editProfile": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON>", "middleName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "professionalNameAndTitle": "<PERSON><PERSON> na <PERSON> cha <PERSON>", "speciality": "U<PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON><PERSON><PERSON>", "messageMe": "<PERSON><PERSON><PERSON>", "makeAppointment": "<PERSON><PERSON><PERSON>", "telemedicine": "Tiba Mtandao", "donate": "Changia", "publicDomainName": "<PERSON><PERSON> cha <PERSON>", "startAPostOn": "<PERSON><PERSON>", "createYourOwnPersonalURL": "Tengeneza URL yako <PERSON>", "published": {"manageProfile": "<PERSON><PERSON><PERSON>", "editPublicProfile": "<PERSON><PERSON>", "unPublishProfile": "Ondoa Chapisho la Wasifu"}}, "sideBar": {"myClinicSolutions": "Suluhisho za Kliniki Yangu", "myClinicSettings": "<PERSON><PERSON><PERSON><PERSON> ya Kliniki <PERSON>", "myCLinicProfile": "<PERSON><PERSON><PERSON> wa Kliniki Yangu", "enterpriseSolutions": "<PERSON><PERSON><PERSON><PERSON> za B<PERSON>", "companyDashboard": "<PERSON><PERSON><PERSON><PERSON> ya Kampuni", "companySettings": "<PERSON><PERSON><PERSON><PERSON>", "companySubscription": "<PERSON><PERSON><PERSON> wa <PERSON>", "genomicsDepartment": "Vipengele vya Genetiki", "myDashboard": "<PERSON><PERSON><PERSON><PERSON>", "mySettings": "<PERSON><PERSON><PERSON><PERSON>", "mySubscription": "<PERSON><PERSON><PERSON>", "myPublicProfile": "<PERSON><PERSON><PERSON> wa <PERSON>", "myPermissions": "<PERSON><PERSON><PERSON>", "myDna": "<PERSON>", "signOut": "To<PERSON>", "myAiAgent": "<PERSON><PERSON>"}, "nav": {"about": "<PERSON><PERSON><PERSON>", "collaborate": "<PERSON><PERSON><PERSON>", "communities": "<PERSON><PERSON>", "solutions": "<PERSON><PERSON><PERSON><PERSON>"}, "ravidSettings": {"tabs": {"personal": "<PERSON><PERSON><PERSON>", "emergency": "<PERSON><PERSON><PERSON>", "medical": "<PERSON><PERSON>", "insurance": "Bima", "qrCode": "Msimbo wa QR", "accessSecurity": "<PERSON><PERSON><PERSON> wa Upatikanaji", "verification": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "accountSharing": "<PERSON><PERSON><PERSON><PERSON>", "communications": "<PERSON><PERSON><PERSON><PERSON>", "storage": "<PERSON><PERSON><PERSON><PERSON>", "billings": "<PERSON><PERSON>"}, "personal": {"personalInformation": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON>", "middleName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON> ya <PERSON>zaliwa", "language": "<PERSON><PERSON><PERSON>", "bloodType": "<PERSON><PERSON>", "gender": "Jinsia", "genomeTested": "Genome Imepimwa?", "personalAddress": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON>", "state": "Jimbo", "zipCode": "<PERSON><PERSON><PERSON> wa <PERSON>", "country": "<PERSON>chi", "mobilePhone": "<PERSON><PERSON>", "personalProfilePicture": "<PERSON><PERSON> <PERSON><PERSON>", "additionalInformation": "Chaguo za Kuingiza Data za Ziada Zinakuja Hivi Karibuni"}, "emergency": {"emergencyContactInformation": "<PERSON><PERSON><PERSON> za Mawas<PERSON> ya <PERSON>", "primaryEmergencyContact": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON> ya <PERSON>", "contactName": "<PERSON><PERSON>", "contactPhoneNumber": "<PERSON><PERSON> ya <PERSON>mu", "contactEmail": "<PERSON><PERSON>", "relationship": "<PERSON><PERSON><PERSON>", "additionalEmergencyContact": "<PERSON><PERSON><PERSON><PERSON> ya Dharura ya Ziada", "additionalEmergencyInformation": "Taarifa za Dharura za Ziada", "nextOfKinContact": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON> wa <PERSON>bu", "allergies": "Mzio", "emergencyMedications": "Dawa za Dharura", "critialInformation": "<PERSON><PERSON><PERSON>", "critialPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ingiza hali zote ambazo zingekuwa muhimu kwa utunzaji wako katika <PERSON>", "pastMedicalSurgeries": "Historia ya Matibabu/Upasuaji", "enterPastMedicalSurgeries": "Ingiza Historia ya Matibabu/Upasuaji", "bloodType": "<PERSON><PERSON>", "pastAdmissionsSurgeries": "Historia ya Kulazwa/Upasuaji", "disclaimer": "Angalizo: Ta<PERSON><PERSON>li kumbuka kuwa taarifa zote za mawasiliano ya dharura na matibabu unazoweka na kuhifadhiwa kwa mafanikio kutoka ukurasa huu kwenye hifadhidata pia zinahifadhiwa kwenye msimbo wako wa QR. Unaweza kutumia taarifa hizi kubadilishana taarifa zako za siri wakati wa dharura au kama ilivyoamuliwa na wewe mwenyewe au mlezi wako halali/mtoa huduma. Kwa kutumia msimbo wa QR unachukua dhima yote ya kufichua taarifa zako za siri.", "saving": "Inahifadhi...", "save": "<PERSON><PERSON><PERSON>"}, "medical": {"medicalTeam": "<PERSON><PERSON>", "primaryPhysician": "<PERSON><PERSON><PERSON> wa Ms<PERSON>i", "geneticist": "<PERSON><PERSON><PERSON><PERSON> wa <PERSON>", "dentist": "<PERSON><PERSON><PERSON> wa Meno", "ophthalmologist": "<PERSON><PERSON><PERSON> wa <PERSON>", "additionalPractitioners": "<PERSON><PERSON><PERSON><PERSON> wa Z<PERSON>", "name": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "contact": "<PERSON><PERSON>", "affiliation": "<PERSON><PERSON><PERSON>", "addPractitioner": "Ongeza Mtaalamu", "saveChanges": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "disclaimer": "Angalizo: <PERSON><PERSON><PERSON><PERSON> kumbuka kuwa taarifa zote za timu ya matibabu unazoweka na kuhifadhiwa kwa mafanikio kutoka ukurasa huu kwenye hifadhidata pia zinahifadhiwa kwenye msimbo wako wa QR. Unaweza kutumia taarifa hizi kubadilishana taarifa zako za siri wakati wa dharura au kama ilivyoamuliwa na wewe mwenyewe au mlezi wako halali/mtoa huduma. Kwa kutumia msimbo wa QR unachukua dhima yote ya kufichua taarifa zako za siri.", "title": "<PERSON><PERSON>"}, "insurance": {"insuranceInformation": "Taarifa za Bima", "addNewInsurance": "Ongeza Bima Mpya", "insuranceProvider": "<PERSON><PERSON> wa <PERSON>", "insurancePolicyNumber": "<PERSON><PERSON> ya <PERSON>a", "policyHolderName": "<PERSON><PERSON>", "insuranceType": "<PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON> ya Kuanza", "endDate": "<PERSON><PERSON><PERSON> ya <PERSON>a", "dependentInformation": "Taarifa za Wategemezi", "groupNumber": "Nam<PERSON> ya Kikundi", "copayAmount": "<PERSON><PERSON> cha <PERSON> ya <PERSON>", "deductibleAmount": "<PERSON><PERSON> ch<PERSON>", "editInsurance": "<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>"}, "qrCode": {"myEmergencyQRCode": "<PERSON><PERSON><PERSON> wa QR wa <PERSON>"}}, "homeDashboard": {"tabs": {"ai": "AI", "appointments": "<PERSON><PERSON>", "diagnosis": "Utambuzi", "mobileHealth": "<PERSON><PERSON><PERSON>", "notes": "<PERSON><PERSON><PERSON>", "prescriptions": "<PERSON><PERSON><PERSON>", "preventiveServices": "<PERSON><PERSON><PERSON> za <PERSON>", "telemedicine": "Tiba Mtandao", "medicalRecords": "Rekodi za Matibabu"}}}}