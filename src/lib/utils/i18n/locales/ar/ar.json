{"ravid": {"buttons": {"save": "<PERSON><PERSON><PERSON>", "saving": "يتم حفظ...", "saveChanges": "حفظ التغييرات", "cancel": "إلغاء", "delete": "<PERSON><PERSON><PERSON>", "edit": "تعديل", "add": "إضافة", "close": "إغلاق", "back": "رجوع"}, "nav": {"about": "حو<PERSON>", "collaborate": "تعاون", "communities": "مجتمعات", "solutions": "حلو<PERSON>"}, "mySubscriptionDashboard": {"tabs": {"billings": "الفواتير", "services": "الخدمات", "subscriptions": "الاشتراكات"}}, "publicProfile": {"liveVersion": "النسخة المباشرة", "unPublish": "إلغاء النشر", "verify": "تحقق", "getVerifiedProfile": "الحصول على ملف شخصي موثق", "publishProfile": "نشر الملف الشخصي", "editProfile": "تعديل الملف الشخصي", "firstName": "الاسم الأول", "middleName": "الاسم الأوسط", "lastName": "اسم العائلة", "professionalNameAndTitle": "الاسم المهني واللقب", "speciality": "التخصص", "location": "الموقع", "summary": "الملخص", "features": "الميزات", "messageMe": "راسلني", "makeAppointment": "<PERSON><PERSON><PERSON> موعد", "telemedicine": "الطب عن بعد", "donate": "تبرع", "publicDomainName": "اسم النطاق العام", "startAPostOn": "بدء منشور على", "createYourOwnPersonalURL": "إنشاء عنوان URL شخصي خاص بك", "published": {"manageProfile": "إدارة الملف الشخصي", "editPublicProfile": "تعديل الملف الشخصي العام", "unPublishProfile": "إلغاء نشر الملف الشخصي"}}, "sideBar": {"myClinicSolutions": "حلول العيادات الخاصة", "myClinicSettings": "إعدادات العيادات الخاصة", "myCLinicProfile": "الملف الشخصي للعيادات الخاصة", "enterpriseSolutions": "حلول المؤسسات", "companyDashboard": "لوحة التحكم للشركات", "companySettings": "إعدادات الشركات", "companySubscription": "الاشتراكات الشركاتية", "myRavidDashboard": "لوحة التحكم لـ R.A.V.I.D.", "myRavidSettings": "إعدادات لـ R.A.V.I.D.", "mySubscriptions": "الاشتراكات الشخصية", "myPublicProfile": "الملف الشخصي العام", "permissions": "الصلاحيات", "myDna": "DNA الخاصة بي", "signOut": "تسجيل الخروج"}, "ravidSettings": {"tabs": {"personal": "شخصي", "emergency": "طوارئ", "medical": "الفريق الطبي", "insurance": "تأمين", "qrCode": "رمز QR", "accessSecurity": "<PERSON><PERSON><PERSON> الوصول", "verification": "تحقق", "accountSharing": "مشاركة الحساب", "communications": "اتصالات"}, "personal": {"personalInformation": "معلومات شخصية", "firstName": "الاسم الأول", "middleName": "الاسم الأوسط", "lastName": "اسم العائلة", "dateOfBirth": "تاريخ الميلاد", "language": "اللغة", "bloodType": "فصيلة الدم", "gender": "الجنس", "genomeTested": "هل تم اختبار الجينوم؟", "personalAddress": "العنوان الشخصي", "address": "العنوان", "city": "المدينة", "state": "الولاية", "zipCode": "الر<PERSON>ز البريدي", "country": "البلد", "mobilePhone": "الهات<PERSON> المحمول", "personalProfilePicture": "الصورة الشخصية", "additionalInformation": "خيارات إدخال البيانات الإضافية قريباً"}, "emergency": {"emergencyContactInformation": "معلومات الاتصال في حالات الطوارئ", "primaryEmergencyContact": "جهة اتصال الطوارئ الرئيسية", "contactName": "اسم جهة الاتصال", "contactPhoneNumber": "رقم الهاتف", "contactEmail": "الب<PERSON>يد الإلكتروني", "relationship": "العلاقة", "additionalEmergencyContact": "جهة اتصال طوارئ إضافية", "nextOfKinContact": "الاتصال بأقرب الأقارب", "allergies": "الحساسية", "emergencyMedications": "أدوية الطوارئ", "critialInformation": "معلومات حرجة", "critialPlaceholder": "ير<PERSON>ى إدخال جميع الحالات التي تكون حرجة لرعايتك في حالات الطوارئ", "pastMedicalSurgeries": "العمليات الجراحية/الطبية السابقة", "enterPastMedicalSurgeries": "أدخل العمليات الجراحية/الطبية السابقة", "bloodType": "فصيلة الدم"}, "medical": {"medicalTeam": "الفريق الطبي", "primaryPhysician": "الطبيب الرئيسي", "geneticist": "أخصائي الوراثة", "dentist": "طبيب الأسنان", "ophthalmologist": "طبيب العيون", "additionalPractitioners": "الممارسون الإضافيون", "name": "الاسم", "email": "الب<PERSON>يد الإلكتروني", "contact": "رقم الاتصال", "affiliation": "الانتماء", "title": "الفريق الطبي"}, "insurance": {"insuranceInformation": "معلومات التأمين", "addNewInsurance": "إضافة تأمين جديد", "insuranceProvider": "شركة التأمين", "insurancePolicyNumber": "رقم البوليصة", "policyHolderName": "اسم حامل البوليصة", "insuranceType": "نوع التأمين", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "dependentInformation": "معلومات المعالين", "groupNumber": "رقم المجموعة", "copayAmount": "مبلغ المشاركة في الدفع", "deductibleAmount": "م<PERSON><PERSON><PERSON> التحمل"}, "qrCode": {"myEmergencyQRCode": "رمز QR الطوارئ الشخصي"}}, "homeDashboard": {"tabs": {"ai": "الذكاء الاصطناعي", "appointments": "المواعيد", "diagnosis": "التشخيص", "mobileHealth": "الصحة المتنقلة", "notes": "الملاحظات", "prescriptions": "الوصفات الطبية", "preventiveServices": "الخدمات الوقائية", "telemedicine": "الطب عن بعد", "medicalRecords": "السجلات الطبية", "storage": "التخزين"}}}}