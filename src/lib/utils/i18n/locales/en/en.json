{"ravid": {"buttons": {"save": "Save", "saving": "Saving...", "saveChanges": "Save Changes", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "back": "Back"}, "mySubscriptionDashboard": {"tabs": {"billings": "<PERSON><PERSON>", "services": "Services", "subscriptions": "Subscriptions"}}, "publicProfile": {"liveVersion": "Live Version", "paymentGateway": "Payment Gateway", "payDonateManager": "Pay / Donate Manager", "unPublish": "Unpublish", "verify": "Verify", "getVerifiedProfile": "Get Verified Profile", "publishProfile": "Publish Profile", "editProfile": "Edit Profile", "firstName": "First Name", "middleName": "Middle Name", "lastName": "Last Name", "professionalNameAndTitle": "Professional Name & Title", "speciality": "Speciality", "location": "Location", "summary": "Summary", "features": "Features", "messageMe": "Message Me", "message": "Message", "messages": "Messages", "notifications": "Notifications", "administrationManager": "Administration Manager", "administration": "Administration", "paymentManager": "Payment Manager", "appointmentRequests": "Appointment Requests", "appointments": "Appointments", "notificationsSettings": "Notifications Settings", "appointmentSchedule": "Appointment Schedule", "appointment": "Appointment", "makeAppointment": "Make Appointment", "telemedicine": "Telemedicine", "payments": "Payments", "pay&donate": "Pay / Donate", "donate": "Donate", "publicDomainName": "Public Domain Name", "startAPostOn": "Start a Post on", "createYourOwnPersonalURL": "Create your own personal URL", "pendingVerification": "Pending Verification", "published": {"manageProfile": "Manage Profile", "editPublicProfile": "Edit Public Profile", "unPublishProfile": "Unpublish Profile"}}, "sideBar": {"myClinicSolutions": "My Clinic Solutions", "myClinicSettings": "My Clinic Settings", "myCLinicProfile": "My Clinic Profile", "enterpriseSolutions": "Enterprise Solutions", "companyDashboard": "Company Dashboard", "companySettings": "Company Settings", "companySubscription": "Company Subscription", "genomicsDepartment": "Genomics Department", "myDashboard": "My Dashboard", "mySettings": "My Settings", "mySubscriptions": "My Subscriptions", "myPublicProfile": "My Public Profile", "myPermissions": "My Permissions", "myDna": "My DNA", "signOut": "Sign Out", "myAiAgent": "My AI Agent"}, "footer": {"shortcuts": "Shortcuts", "quickLinks": "Quick Links", "about": "About", "collaborate": "Collaborate", "communities": "Communities", "solutions": "Solutions", "myClinicProfile": "My Clinic Profile", "myEnterpriseProfile": "My Enterprise Profile", "myRavidProfile": "My Ravid Profile", "mySubscription": "My Subscription", "myDna": "My DNA", "signOut": "Sign Out", "myClinicInfo": "My Clinic Info", "companyDashboard": "Company Dashboard", "companySettings": "Company Settings", "companySubscription": "Company Subscription", "myRavidDashboard": "My <PERSON>d <PERSON>", "myRavidSettings": "My <PERSON><PERSON>", "myPublicProfile": "My Public Profile", "myDNA": "My DNA", "myPermissions": "My Permissions"}, "nav": {"about": "About", "collaborate": "Collaborate", "communities": "Communities", "solutions": "Solutions"}, "ravidSettings": {"tabs": {"personal": "Personal", "emergency": "Emergency", "medical": "Medical Team", "insurance": "Insurance", "qrCode": "QR Code", "accessSecurity": "Access Security", "verification": "Verification", "accountSharing": "Account Sharing", "communications": "Communications", "storage": "Storage", "billings": "<PERSON><PERSON>"}, "personal": {"personalInformation": "Personal Information", "firstName": "First Name", "middleName": "Middle Name", "lastName": "Last Name", "dateOfBirth": "Date of Birth", "language": "Language", "bloodType": "Blood Type", "gender": "Gender", "genomeTested": "Genome Tested?", "personalAddress": "Personal Address", "address": "Address", "city": "City", "state": "State", "zipCode": "Zip Code", "country": "Country", "mobilePhone": "Mobile Phone", "personalProfilePicture": "Personal Profile Picture", "additionalInformation": "Additional data input options Coming Soon"}, "emergency": {"emergencyContactInformation": "Emergency Contact Information", "primaryEmergencyContact": "Primary Emergency Contact", "contactName": "Contact Name", "contactPhoneNumber": "Phone Number", "contactEmail": "Email", "relationship": "Relationship", "additionalEmergencyContact": "Additional Emergency Contact", "additionalEmergencyInformation": "Additional Emergency Information", "nextOfKinContact": "Next of Kin Contact", "allergies": "Allergies", "emergencyMedications": "Emergency Medications", "critialInformation": "Critical Information", "critialPlaceholder": "Please enter all conditions that would be critical for your care in an Emergency", "pastMedicalSurgeries": "Past Medical/Surgeries", "enterPastMedicalSurgeries": "Enter Past Medical/Surgeries", "bloodType": "Blood Type", "pastAdmissionsSurgeries": "Past Admissions/Surgeries", "disclaimer": "Disclaimer: Please note that all the emergency contact & medical information you input & is successfully saved from this page to the database is also saved in your QR code. You can use this information to seamlessly exchange your confidential information in times of emergency or as determined by yourself or your authorized legal guardian/ care provider. By deploying the QR code you assume all liability of disclosing your confidential information.", "saving": "Saving...", "save": "Save"}, "medical": {"medicalTeam": "Medical Team", "primaryPhysician": "Primary Physician", "geneticist": "Geneticist", "dentist": "Dentist", "ophthalmologist": "Ophthalmologist", "additionalPractitioners": "Additional Practitioners", "name": "Name", "email": "Email", "contact": "Contact Number", "affiliation": "Affiliation", "addPractitioner": "Add Practitioner", "saveChanges": "Save Changes", "save": "Save", "disclaimer": "Disclaimer: Please note that all the medical team information you input & is successfully saved from this page to the database is also saved in your QR code. You can this information to seamlessly exchange your confidential information in times of emergency or as determined by yourself or your authorized legal guardian/ care provider. By deploying the QR code you assume all liability of disclosing your confidential information.", "title": "Medical Team"}, "insurance": {"insuranceInformation": "Insurance Information", "addNewInsurance": "Add New Insurance", "insuranceProvider": "Insurance Provider", "insurancePolicyNumber": "Policy Number", "policyHolderName": "Policy Holder Name", "insuranceType": "Insurance Type", "startDate": "Start Date", "endDate": "End Date", "dependentInformation": "Dependent Information", "groupNumber": "Group Number", "copayAmount": "<PERSON><PERSON>", "save": "Save", "deductibleAmount": "Deductible Amount", "editInsurance": "Edit Insurance Information"}, "qrCode": {"myEmergencyQRCode": "My Emergency QR Code"}}, "homeDashboard": {"tabs": {"ai": "AI", "appointments": "Appointments", "diagnosis": "Diagnosis", "mobileHealth": "Mobile Health", "notes": "Notes", "prescriptions": "Prescriptions", "preventiveServices": "Preventive Services", "telemedicine": "Telemedicine", "medicalRecords": "Medical Records"}}}}