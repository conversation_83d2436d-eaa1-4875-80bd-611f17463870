import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import enLang from "./i18n/locales/en/en.json";
import arLang from "./i18n/locales/ar/ar.json";
import viLang from "./i18n/locales/vi/vi.json";
import swLang from "./i18n/locales/sw/sw.json";
import thLang from "./i18n/locales/th/th.json";
import hiLang from "./i18n/locales/hi/hi.json";

const resources = {
  en: {
    translation: enLang,
  },
  ar: {
    translation: arLang,
  },
  vi: {
    translation: viLang,
  },
  sw: {
    translation: swLang,
  },
  th: {
    translation: thLang,
  },
  hi: {
    translation: hiLang,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: "en",
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
});

export const changeLanguage = (lang: string) => {
  i18n.changeLanguage(lang);
  document.documentElement.lang = lang;
  document.documentElement.dir = "ltr";
};

export default i18n;
