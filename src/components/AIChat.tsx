"use client";
import { useAnalyzeFiles, useSaveDiagnosisAsPDF, useSendChatMessage, useSendUnifiedMessage, useStartChat } from "@/hooks/home-dashboard/useAI";
import { cn } from "@/lib/utils";
import { Textarea } from "@heroui/input";
import { Button, Card, Select, SelectItem } from "@heroui/react";
import { ArrowUp, CloudUpload, Image as ImageIcon, Mic, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import CopyComponent from "./Copy";
import DownloadAsPdf from "./DownloadAsPdf";
import { RsIcon } from "./icons";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  documentIds?: string[];
  chartData?: Array<{ name: string; value: number }>;
  type?: "prescription" | "diagnosis" | "dna";
};

type Message = {
  role: "user" | "assistant";
  content?: string;
  isStreaming?: boolean;
  displayContent?: string;
};

export const sanitizeMessage = (message: string) => {
  return message
    .trim()
    .replace(/^```json\n/, "")
    .replace(/```$/, "")
    .replace(/\\n/g, "<br/>")
    .replace(/\*\*(\d+\.)\s*([^:*]+):\*\*/g, '<strong class="block mt-2">$1 $2:</strong>')
    .replace(/\*\*([^*]+?):\*\*/g, "<strong>$1:</strong>")
    .replace(/\*\*([^*]+?)\*\*/g, "<strong>$1</strong>")
    .replace(/\n/g, "<br/>")
    .replace(/_([^_]+)_/g, "<em>$1</em>")
    .replace(/`([^`]+)`/g, "<code class='bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-xs'>$1</code>")
    .replace(/\*\s*(.*?)(?:<br\/>|$)/g, '<li class="ml-4">$1</li>')
    .replace(/(<li.*?>.*?<\/li>)+/g, '<ul class="list-disc space-y-1 mt-2">$&</ul>');
};

// Add TypeScript definitions for Speech Recognition
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
  error: any;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  isFinal: boolean;
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  onresult: (event: SpeechRecognitionEvent) => void;
  onerror: (event: SpeechRecognitionEvent) => void;
  onend: () => void;
  onstart: () => void;
  start(): void;
  stop(): void;
  abort(): void;
  new (): SpeechRecognition;
}

declare global {
  interface Window {
    SpeechRecognition: {
      new (): SpeechRecognition;
      prototype: SpeechRecognition;
    };
    webkitSpeechRecognition: {
      new (): SpeechRecognition;
      prototype: SpeechRecognition;
    };
  }
}

const AIChat = ({ isOpen, onClose, documentIds, type, chartData }: Props) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [chatId, setChatId] = useState<string | null>(null);
  const [messageId, setMessageId] = useState<string | null>(null);
  const [isRavidSearchActive, setIsRavidSearchActive] = useState(false);
  const [isListening, setIsListening] = useState<boolean>(false);
  const [hasRecognitionSupport, setHasRecognitionSupport] = useState<boolean>(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const isBrave = useRef<boolean>(false);
  const streamingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [selectedModel, setSelectedModel] = useState("gemini");
  const { mutate: analyzeFiles, isPending: isAnalyzingFiles } = useAnalyzeFiles();
  const { mutate: sendMessage, isPending: isSendingMessage } = useSendChatMessage();
  const { mutate: saveDiagnosisAsPDF, isPending: isSavingDiagnosisAsPDF } = useSaveDiagnosisAsPDF();
  const { mutate: startChat, isPending: isStartingChat } = useStartChat();
  const { mutate: sendUnifiedMessage } = useSendUnifiedMessage();

  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [previewFileName, setPreviewFileName] = useState<string>("");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [processingMethod, setProcessingMethod] = useState<"multimodal" | "text_extraction">("multimodal");
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      if ((type === "prescription" || type === "diagnosis") && documentIds) {
        setMessages([{ role: "user" as const, content: "Please analyze the selected medical documents and provide an assessment." }]);
        analyzeFiles(
          {
            ids: documentIds,
            model: "gemini",
            type,
          },
          {
            onSuccess: (response) => {
              try {
                const jsonMatch = response.match(/^\s*({.*?})/);
                if (jsonMatch) {
                  const jsonString = jsonMatch[1];
                  const jsonResponse = JSON.parse(jsonString);

                  if (jsonResponse.chat_id && jsonResponse.message_id) {
                    setChatId(jsonResponse.chat_id);
                    setMessageId(jsonResponse.message_id);
                    const sanitizedContent = sanitizeMessage(response.replace(jsonString, "").trim());
                    setMessages((prev) => {
                      const newMessages = [
                        ...prev,
                        {
                          role: "assistant" as const,
                          content: sanitizedContent,
                          isStreaming: true,
                          displayContent: "",
                        },
                      ];
                      simulateStreaming(newMessages.length - 1, sanitizedContent);
                      return newMessages;
                    });
                  }
                }
              } catch (error) {
                console.error("Error processing response:", error);
              }
            },
          }
        );
      } else if (type === "dna" && chartData) {
        // For chart data, create a proper chat session with chart context including values
        const chartSummary = chartData.map((d) => `${d.name}: ${d.value}`).join(", ");
        const chartContext = `Please analyze this ${type} chart data: ${chartSummary}. Provide insights about the distribution, patterns, and what these values might indicate.`;
        setMessages([
          {
            role: "user",
            content: chartContext,
          },
        ]);

        // Create a proper chat session for chart analysis using startChat
        startChat(
          {
            message: chartContext,
            model: "gemini",
            enable_search: isRavidSearchActive,
          },
          {
            onSuccess: (response) => {
              try {
                const jsonMatch = response.match(/^\s*({.*?})/);
                if (jsonMatch) {
                  const jsonString = jsonMatch[1];
                  const jsonResponse = JSON.parse(jsonString);

                  if (jsonResponse.chat_id) {
                    setChatId(jsonResponse.chat_id);
                    setMessages((prev) => [
                      ...prev,
                      {
                        role: "assistant",
                        content: sanitizeMessage(response.replace(jsonString, "").trim()),
                      },
                    ]);
                  }
                }
              } catch (error) {
                console.error("Error processing response:", error);
              }
            },
          }
        );
      }
    }
  }, [isOpen, documentIds, chartData, analyzeFiles, type, startChat]);

  // Check browser compatibility for speech recognition
  useEffect(() => {
    isBrave.current = "brave" in navigator;
    if ("SpeechRecognition" in window || "webkitSpeechRecognition" in window) {
      setHasRecognitionSupport(true);
    }
  }, []);

  const startListening = () => {
    if (!hasRecognitionSupport) {
      toast.error("Your browser doesn't support speech recognition.");
      return;
    }

    if (isBrave.current) {
      toast.error("Speech recognition doesn't work in Brave browser due to privacy restrictions. Please use Chrome or Edge.");
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognitionRef.current = new SpeechRecognition();

    if (recognitionRef.current) {
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = "en-US";

      recognitionRef.current.onstart = () => {
        setIsListening(true);
      };

      recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = Array.from(event.results)
          .map((result) => result[0])
          .map((result) => result.transcript)
          .join("");

        setInputMessage(transcript);
      };

      recognitionRef.current.onerror = (event: SpeechRecognitionEvent) => {
        switch (event.error) {
          case "network":
            toast.error("Network error. Please check your internet connection.");
            break;
          case "not-allowed":
          case "permission-denied":
            toast.error("Microphone access denied. Please allow microphone access in your browser settings.");
            break;
          case "no-speech":
            toast.error("No speech detected. Please try speaking more clearly.");
            break;
          case "audio-capture":
            toast.error("No microphone detected. Please check your microphone connection.");
            break;
          default:
            toast.error(`Recognition error: ${event.error}`);
        }
        setIsListening(false);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };

      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error("Failed to start speech recognition:", error);
        toast.error("Failed to start speech recognition. Please try again.");
        setIsListening(false);
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error("Error stopping recognition:", error);
      }
      setIsListening(false);
    }
  };

  const activateRavidSearch = () => {
    setIsRavidSearchActive(!isRavidSearchActive);
  };

  const handleSendMessage = () => {
    if (!inputMessage.trim() || !chatId) return;

    const userMessage: Message = {
      role: "user" as const,
      content: inputMessage + (selectedFiles.length > 0 ? ` (${selectedFiles.length} file(s) attached)` : ""),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");

    if (selectedFiles.length > 0) {
      // Use unified message API when files are attached
      sendUnifiedMessage(
        {
          message: inputMessage,
          chat_id: chatId,
          files: selectedFiles,
          processing_method: processingMethod,
        },
        {
          onSuccess: (data) => {
            try {
              // Split response into JSON and text content
              const responseText = data.toString();
              const parts = responseText.split("\n");

              // First line should be JSON
              const jsonLine = parts[0];
              const jsonResponse = JSON.parse(jsonLine);

              // Rest is the text content
              const textContent = parts
                .slice(1)
                .join("\n")
                .trim();

              if (jsonResponse?.chat_id) {
                setChatId(jsonResponse.chat_id);
                setMessageId(jsonResponse.message_id);
                const sanitizedContent = sanitizeMessage(textContent);
                setMessages((prev) => {
                  const newMessages = [
                    ...prev,
                    {
                      role: "assistant" as const,
                      content: sanitizedContent,
                      isStreaming: true,
                      displayContent: "",
                    },
                  ];
                  simulateStreaming(newMessages.length - 1, sanitizedContent);
                  return newMessages;
                });
              }
            } catch (error) {
              console.error("Failed to parse JSON:", error);
              toast.error("Failed to process the response");
            }
            // Clear selected files after sending
            setSelectedFiles([]);
          },
          onError: (error) => {
            console.error("Error sending message:", error);
            toast.error("Failed to send message");
            setSelectedFiles([]);
          },
        }
      );
    } else {
      // Use regular chat message API when no files
      sendMessage(
        { message: inputMessage, model: selectedModel, chat_id: chatId, enable_search: isRavidSearchActive },
        {
          onSuccess: (data) => {
            try {
              const jsonMatch = data.match(/^\s*({.*?})/);
              if (jsonMatch) {
                const jsonString = jsonMatch[1];
                const jsonResponse = JSON.parse(jsonString);
                if (jsonResponse.chat_id) {
                  setChatId(jsonResponse.chat_id);
                  const sanitizedContent = sanitizeMessage(data.replace(jsonString, "").trim());
                  setMessages((prev) => {
                    const newMessages = [
                      ...prev,
                      {
                        role: "assistant" as const,
                        content: sanitizedContent,
                        isStreaming: true,
                        displayContent: "",
                      },
                    ];
                    simulateStreaming(newMessages.length - 1, sanitizedContent);
                    return newMessages;
                  });
                }
              }
            } catch (error) {
              console.error("Error processing response:", error);
            }
          },
        }
      );
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSelectedFiles(files);
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSaveDiagnosisAsPDF = () => {
    if (!messageId) return;
    saveDiagnosisAsPDF({ output_id: messageId });
  };

  const handlePreview = (fileUrl: string, fileName: string) => {
    setPreviewUrl(fileUrl);
    setPreviewFileName(fileName);
    setIsPreviewOpen(true);
  };

  // Add streaming simulation function
  const simulateStreaming = (messageIndex: number, fullContent: string) => {
    let currentIndex = 0;

    const streamInterval = setInterval(() => {
      if (currentIndex <= fullContent.length) {
        setMessages((prevMessages) => {
          const newMessages = [...prevMessages];
          if (newMessages[messageIndex]) {
            newMessages[messageIndex] = {
              ...newMessages[messageIndex],
              displayContent: fullContent.slice(0, currentIndex),
              isStreaming: currentIndex < fullContent.length,
            };
          }
          return newMessages;
        });
        currentIndex += 3; // Adjust speed by changing this number
      } else {
        if (streamingIntervalRef.current) {
          clearInterval(streamingIntervalRef.current);
          streamingIntervalRef.current = null;
        }
      }
    }, 10); // Adjust interval for smoother/faster streaming

    streamingIntervalRef.current = streamInterval;
  };

  // Clean up streaming interval on unmount
  useEffect(() => {
    return () => {
      if (streamingIntervalRef.current) {
        clearInterval(streamingIntervalRef.current);
      }
    };
  }, []);

  // Fix for dangerouslySetInnerHTML type error
  const getHtmlContent = (message: Message) => {
    const content = message.displayContent || message.content || "";
    return { __html: content };
  };

  if (!isOpen) return null;

  console.group({
    messages,
    chatId,
    messageId,
  });

  return (
    <div className="fixed bottom-4 right-0 sm:right-4 z-50">
      <Card className="sm:w-[500px] w-full shadow-lg flex flex-col h-[600px] bg-white dark:bg-[#1a1b26] text-gray-900 dark:text-white border-none rounded-2xl overflow-hidden">
        <div className="flex items-center p-4 bg-gray-50 dark:bg-gradient-to-r from-[#231b4c] via-[#0b1b37] to-[#231c4d] border-b border-gray-200 dark:border-gray-700">
          <div className="w-8 h-8 rounded-full bg-gray-100 dark:bg-[#2a2b36] flex items-center justify-center mr-3">
            <img src="/images/ravid.png" alt="RAVID" className="w-6 h-6" />
          </div>
          <h3 className="font-normal text-sm">Chat with R.A.V.I.D. powered by {selectedModel === "gemini" ? "Gemini" : "Claude"}</h3>
          <Button variant="ghost" size="sm" onPress={onClose} className="p-1 ml-auto hover:bg-gray-100 dark:hover:bg-[#2a2b36]">
            <X size={18} className="text-gray-700 dark:text-white" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-white dark:bg-[#1a1b26]">
          <div className="text-amber-600 dark:text-[#ffd700] flex justify-end mb-4 text-xs">We have initiated the AI Agent with the following prompt:</div>

          {messages.map((message, index) => (
            <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
              {message.role === "user" && (
                <div className="bg-gray-100 dark:bg-[#2a2b36] p-2 rounded-lg text-gray-900 dark:text-white text-xs max-w-[90%] ml-auto">{message.content}</div>
              )}
              {message.role === "assistant" && (
                <div className="relative bg-gray-100 dark:bg-[#2a2b36] p-3 rounded-lg text-gray-900 dark:text-white text-xs max-w-[90%] mr-auto">
                  <div className="absolute top-2 right-2 flex gap-1">
                    <CopyComponent text={message.content || ""} />
                    <DownloadAsPdf text={message.content || ""} className="p-1 h-6 w-6 hover:bg-gray-200 dark:hover:bg-[#363746] border-none rounded" />
                    <button onClick={handleSaveDiagnosisAsPDF} className="rounded-md p-1 hover:bg-gray-200 dark:hover:bg-[#363746]" title="Save as PDF">
                      {!chartData && (
                        <CloudUpload className="h-4 w-4 transition-all hover:text-gray-700 dark:hover:text-gray-200 text-gray-500 dark:text-gray-400" />
                      )}
                    </button>
                  </div>
                  <div className="px-3 text-xs pr-24">
                    <div dangerouslySetInnerHTML={getHtmlContent(message)} />
                    {message.isStreaming && <span className="inline-block w-1 h-4 ml-1 bg-gray-500 animate-pulse"></span>}
                  </div>
                </div>
              )}
            </div>
          ))}
          {(isAnalyzingFiles || isSendingMessage || isSavingDiagnosisAsPDF || isStartingChat) && (
            <div className="flex justify-start">
              <div className="bg-gray-100 dark:bg-[#2a2b36] p-3 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-gray-600 dark:bg-white rounded-full animate-bounce"></div>
                  <div className="w-1 h-1 bg-gray-600 dark:bg-white rounded-full animate-bounce delay-75"></div>
                  <div className="w-1 h-1 bg-gray-600 dark:bg-white rounded-full animate-bounce delay-150"></div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="p-2 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1a1b26]">
          <div className="flex flex-col gap-3">
            <div className="flex gap-2">
              <Select
                classNames={{
                  trigger: "bg-gray-100 dark:bg-[#2a2b36] text-gray-900 dark:text-white border-gray-200 dark:border-gray-700",
                  value: "text-gray-900 dark:text-white",
                }}
                aria-label="Select Model"
                selectedKeys={[selectedModel]}
                className="w-[150px]"
                onSelectionChange={(keys) => {
                  const selectedValue = Array.from(keys)[0];
                  setSelectedModel(selectedValue as string);
                }}
                size="sm"
              >
                <SelectItem key="gemini" className="text-gray-900 dark:text-white">
                  Gemini
                </SelectItem>
                <SelectItem key="anthropic" className="text-gray-900 dark:text-white">
                  Claude-sonnet
                </SelectItem>
              </Select>

              {selectedFiles.length > 0 && (
                <Select
                  classNames={{
                    trigger: "bg-gray-100 dark:bg-[#2a2b36] text-gray-900 dark:text-white border-gray-200 dark:border-gray-700",
                    value: "text-gray-900 dark:text-white",
                  }}
                  aria-label="Processing Method"
                  selectedKeys={[processingMethod]}
                  className="w-[150px]"
                  onSelectionChange={(keys) => {
                    const selectedValue = Array.from(keys)[0];
                    setProcessingMethod(selectedValue as "multimodal" | "text_extraction");
                  }}
                  size="sm"
                >
                  <SelectItem key="multimodal" className="text-gray-900 dark:text-white">
                    Image Analysis
                  </SelectItem>
                  <SelectItem key="text_extraction" className="text-gray-900 dark:text-white">
                    Text Extraction
                  </SelectItem>
                </Select>
              )}

              <button
                className={cn(
                  "text-xs px-3 rounded-3xl font-medium transition-all duration-200 flex items-center gap-1.5 border",
                  isRavidSearchActive
                    ? "dark:bg-[#1f1729] bg-[#b790ca] text-white border-[#7d5191]"
                    : "border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-slate-700"
                )}
                onClick={activateRavidSearch}
              >
                <RsIcon className="w-4 h-4" />
                R.A.V.I.D. Search
              </button>
            </div>

            {selectedFiles.length > 0 && (
              <div className="flex flex-wrap gap-2 p-2 bg-gray-100 dark:bg-[#2a2b36] rounded-lg">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center gap-2 bg-white dark:bg-[#363746] p-1 rounded">
                    <span className="text-xs truncate max-w-[100px]">{file.name}</span>
                    <button onClick={() => handleRemoveFile(index)} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )}

            <div className="relative">
              <Textarea
                maxRows={2}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="Type a message..."
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                disabled={isSendingMessage || !chatId}
                className="w-full min-h-[50px] bg-gray-100 dark:bg-[#2a2b36] text-gray-900 dark:text-white border-gray-200 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 resize-none rounded-lg focus:ring-1 focus:ring-gray-500 focus:border-gray-500"
              />
              <div className="absolute right-2 top-2 flex items-center gap-2">
                <input type="file" ref={fileInputRef} onChange={handleFileSelect} className="hidden" multiple accept="image/*,.pdf,.doc,.docx,.txt" />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="h-6 w-6 rounded-full p-1 transition-all duration-300 cursor-pointer text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700"
                >
                  <ImageIcon className="h-full w-full" />
                </button>
                <button
                  onClick={isListening ? stopListening : startListening}
                  className={cn(
                    "h-6 w-6 rounded-full p-1 transition-all duration-300 cursor-pointer hover:scale-125",
                    isListening ? "text-red-500 hover:text-red-600" : "text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700"
                  )}
                >
                  <Mic className="h-full w-full" />
                </button>
                <Button
                  onPress={handleSendMessage}
                  disabled={isSendingMessage || !chatId || !inputMessage.trim()}
                  size="sm"
                  className="bg-[#DE5C08] text-white hover:bg-[#c24e06] disabled:opacity-50 disabled:cursor-not-allowed rounded-lg flex items-center justify-center"
                >
                  <ArrowUp className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="text-xs text-gray-500 p-2 text-left bg-white dark:bg-[#1a1b26] border-t border-gray-200 dark:border-gray-700">
          By proceeding further, you have acknowledged that you have read the Important Notice & Disclaimers and have agreed to all our Terms & Conditions.
        </div>
      </Card>
    </div>
  );
};

export default AIChat;
