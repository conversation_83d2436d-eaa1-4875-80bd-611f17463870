import React, { useState, useEffect } from "react";
import { Button } from "@heroui/react";
import { motion } from "framer-motion";

interface Step {
  target: string; // CSS selector for the target element
  title: string;
  description: string;
  position?: "top" | "bottom" | "left" | "right";
}

const steps: Step[] = [
  {
    target: '[data-wizard="3"]',
    title: "Fill Your Basic Information",
    description: "Start by entering your name, title, and other basic details.",
    position: "right",
  },
  {
    target: '[data-wizard="category-type"]',
    title: "Choose a Category",
    description: "Select a category type to showcase your professional information.",
    position: "left",
  },
  {
    target: '[data-wizard="save-your-profile"]',
    title: "Save All Changes",
    description: "Save all your profile changes by clicking the Save Your Profile button.",
    position: "bottom",
  },
  {
    target: '[data-wizard="personal-url"]',
    title: "Create Your Personal URL",
    description: "Choose a unique URL for your profile and click Save.",
    position: "bottom",
  },
  {
    target: '[data-wizard="publish-profile-btn"]',
    title: "Start Publishing",
    description: "Click the purple 'Publish Profile' button at the top to start the publishing process.",
    position: "left",
  },
];

interface ProfileGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProfileGuide: React.FC<ProfileGuideProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<DOMRect | null>(null);

  useEffect(() => {
    if (isOpen) {
      updateTargetElement();
      // Add scroll event listener
      window.addEventListener("scroll", updateTargetElement, true);
      // Add resize event listener to handle window resizing
      window.addEventListener("resize", updateTargetElement);

      return () => {
        // Clean up event listeners
        window.removeEventListener("scroll", updateTargetElement, true);
        window.removeEventListener("resize", updateTargetElement);
      };
    }
  }, [currentStep, isOpen]);

  const updateTargetElement = () => {
    const element = document.querySelector(steps[currentStep].target);
    if (element) {
      // Get updated position including scroll
      const rect = element.getBoundingClientRect();
      setTargetElement(rect);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isOpen || !targetElement) return null;

  const step = steps[currentStep];

  // Calculate tooltip position based on target element with viewport boundary checks
  const getTooltipPosition = () => {
    if (!targetElement) return {};

    const padding = 20;
    const tooltipWidth = 300;
    const tooltipHeight = 150; // Approximate height of tooltip
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let position = { top: 0, left: 0 };

    switch (step.position) {
      case "top":
        position = {
          top: Math.max(padding, targetElement.top - padding - tooltipHeight),
          left: targetElement.left + targetElement.width / 2 - tooltipWidth / 2,
        };
        break;
      case "bottom":
        position = {
          top: Math.min(viewportHeight - tooltipHeight - padding, targetElement.bottom + padding),
          left: targetElement.left + targetElement.width / 2 - tooltipWidth / 2,
        };
        break;
      case "left":
        position = {
          top: targetElement.top + targetElement.height / 2 - tooltipHeight / 2,
          left: Math.max(padding, targetElement.left - padding - tooltipWidth),
        };
        break;
      case "right":
        position = {
          top: targetElement.top + targetElement.height / 2 - tooltipHeight / 2,
          left: Math.min(viewportWidth - tooltipWidth - padding, targetElement.right + padding),
        };
        break;
      default:
        position = {
          top: Math.min(viewportHeight - tooltipHeight - padding, targetElement.bottom + padding),
          left: targetElement.left + targetElement.width / 2 - tooltipWidth / 2,
        };
    }

    // Ensure tooltip stays within viewport bounds
    position.left = Math.max(padding, Math.min(viewportWidth - tooltipWidth - padding, position.left));
    position.top = Math.max(padding, Math.min(viewportHeight - tooltipHeight - padding, position.top));

    return position;
  };

  return (
    <div className="fixed inset-0 z-[100] pointer-events-none">
      {/* Overlay with spotlight effect */}
      <div className="absolute inset-0 bg-black/30">
        {" "}
        {/* Reduced opacity from 50 to 30 */}
        <svg width="100%" height="100%">
          <defs>
            <mask id="spotlight">
              <rect width="100%" height="100%" fill="white" />
              <rect
                x={targetElement.left - 4}
                y={targetElement.top - 4}
                width={targetElement.width + 8}
                height={targetElement.height + 8}
                fill="black"
                rx="4"
              />
            </mask>
          </defs>
          <rect width="100%" height="100%" fill="rgba(0,0,0,0.45)" mask="url(#spotlight)" /> {/* Reduced opacity from 0.75 to 0.45 */}
        </svg>
      </div>

      {/* Target element highlight */}
      <div
        className="absolute border-2 border-blue-500 rounded-lg pointer-events-none transition-all duration-200"
        style={{
          top: targetElement.top - 4,
          left: targetElement.left - 4,
          width: targetElement.width + 8,
          height: targetElement.height + 8,
        }}
      />

      {/* Tooltip */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="absolute bg-white dark:bg-slate-900 rounded-lg shadow-lg p-4 w-[300px] pointer-events-auto"
        style={{
          ...getTooltipPosition(),
          zIndex: 9999,
        }}
      >
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-sm font-medium">{step.title}</h3>
          <Button
            isIconOnly
            size="sm"
            variant="light"
            onPress={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </Button>
        </div>
        <p className="text-xs text-gray-600 dark:text-gray-300 mb-4">{step.description}</p>

        <div className="flex justify-between items-center mt-4">
          <div className="text-xs text-gray-500">
            Step {currentStep + 1} of {steps.length}
          </div>
          <div className="flex gap-2">
            <Button variant="bordered" size="sm" onPress={handleBack} isDisabled={currentStep === 0} className="text-xs">
              Back
            </Button>
            <Button color="primary" size="sm" onPress={handleNext} className="text-xs">
              {currentStep === steps.length - 1 ? "Finish" : "Next"}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ProfileGuide;
