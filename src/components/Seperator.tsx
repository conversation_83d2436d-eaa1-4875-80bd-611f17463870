import React from "react";

interface SeparatorProps {
  width?: string | number;
  color?: string;
  className?: string;
}

export const Separator: React.FC<SeparatorProps> = ({
  width = "100%",
  color = "currentColor",
  className = "",
}) => {
  return (
    <div
      className={`h-[1px] bg-opacity-20 ${className}`}
      style={{
        width: typeof width === "number" ? `${width}px` : width,
        backgroundColor: color,
      }}
    />
  );
};

export default Separator;
