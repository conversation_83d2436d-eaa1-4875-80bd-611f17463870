"use client";
import { Co<PERSON>, <PERSON>py<PERSON><PERSON><PERSON> } from "lucide-react";
import React, { useState } from "react";

type Props = {
  text: string;
};

const CopyComponent = ({ text }: Props) => {
  const [copied, setCopied] = useState(false);
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 1000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <button onClick={copyToClipboard} className="rounded-md p-1 hover:bg-gray-200 dark:hover:bg-gray-600" title="Copy to clipboard">
      {copied ? (
        <CopyCheck className="h-4 w-4 text-green-500 transition-all" />
      ) : (
        <Copy className="h-4 w-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-all" />
      )}
    </button>
  );
};

export default CopyComponent;
