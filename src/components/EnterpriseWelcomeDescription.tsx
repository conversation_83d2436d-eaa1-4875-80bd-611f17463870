const EnterpriseWelcomeDescription = () => {
  return (
    <div className="p-4 rounded-lg space-y-4">
      <h1>Streamline Your Workflow with R.A.V.I.D.</h1>

      <h2>Tailored solutions for enterprises and clinics to optimize efficiency and patient outcomes.</h2>

      <h2>
        Our Enterprise Solutions leverage cutting-edge AI analysis to enhance your operations. We offer a cost-efficient, per-seat licensing model for the
        R.A.V.I.D. premium subscription, designed to boost loyalty among employees and stakeholders. Key features include :
      </h2>

      <p className=" list-disc pl-4 space-y-2">
        <li>AI-Powered Features: Utilize advanced AI analysis tools to streamline workflows.</li>
        <li>Cost-Efficient Licensing: Access all R.A.V.I.D. premium features with a per-seat economic model.</li>
        <li>Branding Integration: Add your logo across all licensed users for a cohesive experience.</li>
        <li>User-Friendly Dashboard: Easily manage users and subscriptions with an admin dashboard to add or delete access as needed.</li>
      </p>

      <h2>**A user needs to be a R.A.V.I.D. member in order to create/use Enterprise Solutions**</h2>
    </div>
  );
};

export default EnterpriseWelcomeDescription;
