// QR code generateor

"use client";
import React, { useState, useEffect } from "react";
import QRCodeStyling from "qr-code-styling";
import { toast } from "react-hot-toast";
import { useGetUser } from "@/hooks/useUser";
import { useGenerateQRCode } from "@/hooks/settings-dashboard/useQRCode";
import { <PERSON><PERSON>, Spinner } from "@heroui/react";
import { getQRCodeOptions } from "@/styles/qrCodeStyles";

interface QRCodeGeneratorProps {
  qrRef: React.RefObject<HTMLDivElement | null>;
  onError?: (error: boolean) => void;
  isModalOpen?: boolean;
  onTokenGenerated?: (token: string) => void;
  url?: string;
}

const QRCodeGenerator = ({ url, qrRef, onError, isModalOpen, onTokenGenerated }: QRCodeGeneratorProps) => {
  const { data: user } = useGetUser();
  const { data: qrCodeData, isLoading: isLoadingData } = useGenerateQRCode();
  const [qrCode, setQrCode] = useState<QRCodeStyling>();
  const [qrError, setQrError] = useState<boolean>(false);
  const [token, setToken] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const clearQrCode = () => {
    if (qrRef.current) {
      qrRef.current.innerHTML = "";
    }
    setToken(null);
    setQrError(false);
  };

  const generateQrCode = async () => {
    try {
      setIsGenerating(true);
      clearQrCode();
      setQrError(false);

      if (!qrCodeData) {
        throw new Error("Failed to generate token");
      }

      if (!user?.user_id) {
        throw new Error("User ID not available");
      }

      const newToken = qrCodeData.token;
      setToken(newToken);

      // Pass token back to parent component
      if (onTokenGenerated) {
        onTokenGenerated(newToken);
      }

      const qrUrl = url ? `${url}` : `${process.env.NEXT_PUBLIC_APP_URL}/share/${user.user_id}?token=${newToken}`;

      // Get QR options based on user's verification status
      const qrOptions = getQRCodeOptions(user?.paid_for_verification || false);
      const newOptions = {
        ...qrOptions,
        data: qrUrl,
      };

      const newQRCode = new QRCodeStyling(newOptions);
      setQrCode(newQRCode);

      // Make sure the DOM element is ready
      if (qrRef.current) {
        qrRef.current.innerHTML = "";

        // Use a small delay to ensure the DOM is ready
        setTimeout(() => {
          if (qrRef.current) {
            try {
              newQRCode.append(qrRef.current);
              console.log("QR code appended to DOM element");
            } catch (error) {
              console.error("Error appending QR code:", error);
              setQrError(true);
              onError?.(true);
            }
          } else {
            console.error("QR code container reference is null");
            setQrError(true);
            onError?.(true);
          }
          setIsGenerating(false);
        }, 500);
      } else {
        console.error("QR code container reference is null");
        setQrError(true);
        onError?.(true);
        setIsGenerating(false);
      }
    } catch (err) {
      console.error("Error generating QR code:", err);
      setQrError(true);
      onError?.(true);
      toast.error("Unable to generate QR code. Please try again later.");
      setIsGenerating(false);
    }
  };

  // Generate QR code when modal is opened and data is available
  useEffect(() => {
    if (isModalOpen && qrCodeData && user?.user_id && !isGenerating) {
      // Small delay to ensure modal is fully rendered
      const timer = setTimeout(() => {
        generateQrCode();
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isModalOpen, qrCodeData, user]);

  if (isGenerating || isLoadingData) {
    return (
      <div className="flex justify-center my-4">
        <Spinner size="lg" color="primary" />
      </div>
    );
  }

  if (qrError) {
    return (
      <Button size="sm" color="primary" variant="shadow" className="text-xs" onPress={generateQrCode}>
        Try Again
      </Button>
    );
  }

  return null;
};

export default QRCodeGenerator;
