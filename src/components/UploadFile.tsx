import { ChangeEvent, useState } from "react";
import { Plus } from "lucide-react";
import toast from "react-hot-toast";
import { But<PERSON> } from "@heroui/react";
import { useUploadDocument } from "@/hooks/home-dashboard/useDiagnosisANDPrescriptions";

type UploadFileProps = {
  file?: File | null;
  type: "diagnosis" | "prescription";
  onSuccess?: () => void;
  onClose?: () => void;
};

const UploadFile = ({ file, type, onSuccess, onClose }: UploadFileProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const { mutate: UploadDocument } = useUploadDocument();

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size should be less than 5MB");
        return;
      }

      // Check file type
      const allowedTypes = ["application/pdf", "image/jpeg", "image/png"];
      if (!allowedTypes.includes(file.type)) {
        toast.error("Only PDF, JPEG, and PNG files are allowed");
        return;
      }

      try {
        setIsUploading(true);
        const formData = new FormData();
        formData.append("file", file);
        console.log(formData);

        await UploadDocument({ type, data: formData });

        toast.success("File uploaded successfully");
        onSuccess?.();
        onClose?.();
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error("Failed to upload file");
      } finally {
        setIsUploading(false);
      }
    }
  };

  return (
    <div className="flex items-center">
      <input id={`file-upload-${type}`} type="file" className="hidden" accept=".pdf,.jpg,.jpeg,.png" onChange={handleFileChange} disabled={isUploading} />
      <Button size="sm" color="primary" variant="shadow" disabled={isUploading} onClick={() => document.getElementById(`file-upload-${type}`)?.click()}>
        <Plus className="w-4 h-4" />
        {isUploading ? "Uploading..." : "Upload"}
      </Button>
    </div>
  );
};

export default UploadFile;
