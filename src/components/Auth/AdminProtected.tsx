"use client";
import AdminAuthDialog from "@/components/Auth/AdminAuthDialog";
import { useAdminAuth } from "@/hooks/settings-dashboard/useAdminAuth";
import { Spinner } from "@heroui/react";
import { usePathname } from "next/navigation";
import { ReactNode, useEffect, useState } from "react";
interface AdminProtectedProps {
  children: ReactNode;
}

const AdminProtected = ({ children }: AdminProtectedProps) => {
  const { showDialog, setShowDialog } = useAdminAuth();
  const currentPath = usePathname();
  const [isRT, setIsRT] = useState(false);
  const [adminPassword, setAdminPassword] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  // Check if the code is running on the server
  useEffect(() => {
    const isServer = typeof window === "undefined";
    if (!isServer) {
      setAdminPassword(localStorage.getItem("adminPassword") || "");
      setIsRT(!!localStorage.getItem("rT"));
    }
    setIsLoading(false);
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner />
      </div>
    );
  }

  if (adminPassword === process.env.NEXT_PUBLIC_ADMIN_PASSWORD) {
    return <>{children}</>;
  } else {
    if ((currentPath === "/hello" || currentPath === "/") && !isRT) {
      console.log("admin");
      return (
        <div className="bg-black text-white font-sans h-screen relative overflow-hidden">
          <div className="fixed top-0 left-0 w-screen h-[90vh] bg-cover bg-center opacity-30 z-0" style={{ backgroundImage: "url('/images/bg.png')" }}></div>

          <header className="p-5 relative z-10">
            <img src="/images/ravidD.png" alt="R.A.V.I.D. Logo" className="w-[120px] h-auto" />
          </header>

          <main className="relative z-10 p-0 h-screen flex flex-col justify-center">
            <div className="max-w-[800px] m-0 text-center mx-auto -translate-y-[20vh] px-5 md:text-left md:pl-[5%] md:mx-0 md:-translate-y-[10vh]">
              <h1 className="text-3xl mb-4 font-bold text-white md:text-4xl text-center md:text-left">Project R.A.V.I.D.</h1>
              <p className="text-sm text-white  mb-3 leading-6 text-center md:text-left">We hope to captivate your imagination.</p>
              <p className="text-sm text-white  mb-3 leading-6 text-center md:text-left">Project R.A.V.I.D. is coming soon.</p>
              <p className="text-sm text-white  mb-3 leading-6 text-center md:text-left">We seek your thoughts, feedback & collaboration. Stay tuned.</p>
            </div>

            <div className="absolute top-1/2 right-0 -translate-y-1/2 w-full h-full pointer-events-none -z-10">
              <img
                src="/images/robot.png"
                alt="AI Visual"
                className="absolute right-1/2 translate-x-1/2 top-1/2 -translate-y-1/2 h-[50vh] opacity-30 md:right-[5%] md:translate-x-0 md:h-[70vh] md:max-h-[700px] md:opacity-80"
              />
              <img src="/images/bg.png" alt="Cell Background" className="fixed left-0 top-0 w-screen h-screen object-cover opacity-50 -z-10" />
            </div>
          </main>
          <footer className="absolute bottom-0 left-0 w-full z-10 p-2.5 text-base text-white/80">
            <p className="text-center">Copyright © 2025 R.A.V.I.D. LLC. All rights reserved.</p>
          </footer>
        </div>
      );
    }
    return (
      <div>
        <AdminAuthDialog open={showDialog} onOpenChange={setShowDialog} />
        <div className="flex justify-center items-center h-screen">
          <Spinner />
        </div>
      </div>
    );
  }
};

export default AdminProtected;
