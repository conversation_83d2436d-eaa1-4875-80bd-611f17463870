import React, { useState } from "react";
import { useAdminAuth } from "@/hooks/settings-dashboard/useAdminAuth";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";

type AdminAuthDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

// Separate component for the auth form content
const AdminAuthForm = ({
  password,
  setPassword,
  error,
  handleKeyDown,
}: {
  password: string;
  setPassword: (password: string) => void;
  error: string | null;
  handleKeyDown: (e: React.KeyboardEvent) => void;
}) => {
  return (
    <div className="flex flex-col gap-3">
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Enter admin password"
        className="w-full px-4 py-2 border rounded-lg"
        onKeyDown={handleKeyDown}
      />
      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

const AdminAuthDialog = ({ open, onOpenChange }: AdminAuthDialogProps) => {
  const { password, setPassword, handleAuthentication, error } = useAdminAuth();

  const handleSubmit = async () => {
    const result = await handleAuthentication();
    if (result) {
      onOpenChange(false);
      window.location.reload();
    }
  };

  return (
    <Modal backdrop="blur" isOpen={open} onOpenChange={onOpenChange} isDismissable={false} hideCloseButton isKeyboardDismissDisabled={true}>
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1">Admin Authentication Required</ModalHeader>
            <ModalBody>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter admin password"
                className="w-full px-4 py-2 border rounded-lg"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSubmit();
                  }
                }}
              />
              {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
            </ModalBody>
            <ModalFooter>
              <Button color="primary" onPress={handleSubmit}>
                Submit
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default AdminAuthDialog;
