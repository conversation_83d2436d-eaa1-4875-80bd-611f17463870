import { Switch } from "@heroui/switch";
import { useState } from "react";

const MedicalProfessional = () => {
  const [isMedicalAdviceExpanded, setIsMedicalAdviceExpanded] = useState(false);

  return (
    <div className="rounded-lg border dark:border-gray-800 p-2">
      <div className="flex items-center justify-between">
        <div className="flex-grow">
          <div className={`flex ${isMedicalAdviceExpanded ? "flex-col" : "items-center"} gap-2`}>
            <h4 className="font-medium text-foreground whitespace-nowrap text-xs">Medical Professional Consent:</h4>
            {!isMedicalAdviceExpanded ? (
              <>
                <span className="muted hidden md:block">I hereby declare that I have the Consent from ... </span>
                <button onClick={() => setIsMedicalAdviceExpanded(true)} className="text-blue-500 hover:underline inline text-xs">
                  Show more
                </button>
              </>
            ) : (
              <span className="muted mr-1">
                I hereby declare that I have the Consent from a Certified Clinician or Medical Professional to help oversee, guide and/ or offer me their
                professional counsel/ guidance/ services upon reviewing any of my records and/ or files after enabling AI Analysis or any AI feature on the
                R.A.V.I.D. platform. I am fully & wholly responsible for the validity of this Statement and Consent and relinquish any and all liabilities to
                all parties associated with the AI Analysis features associated on the R.A.V.I.D. platform from any actions taken or not taken by myself or my
                Certified Clinician or Medical Professional as a result of any analysis or feature or content generated from any AI feature on the R.A.V.I.D.
                platform.{" "}
                <button onClick={() => setIsMedicalAdviceExpanded(false)} className="text-blue-500 hover:underline inline">
                  Show less
                </button>
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative group">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4 text-muted-foreground cursor-help">
              <path
                fillRule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z"
                clipRule="evenodd"
              />
            </svg>
            <div className="absolute hidden group-hover:block right-0 w-64 p-2 mt-2 text-xs bg-gray-800 rounded-lg shadow-lg z-10">
              It is mandatory for you to enable this permission in order to use AI Features on R.A.V.I.D. Platform.
            </div>
          </div>
          <Switch size="sm" color="success" />
        </div>
      </div>
    </div>
  );
};

export default MedicalProfessional;
