import { Button } from "@heroui/button";

type props = {
  className?: string;
  type?: "submit" | "button";
  label?: string;
  isLoadingValue?: string;
  disabled?: boolean;
  isLoading?: boolean;
  onClick?: () => void;
  size?: "sm" | "md" | "lg";
  variant?: "solid" | "light" | "flat" | "bordered" | "faded" | "shadow" | "ghost";
  color?: "primary" | "secondary" | "danger" | "success" | "warning";
  icon?: React.ReactNode;
};

const SubmitButton = ({ type, variant, color = "primary", className, label, disabled, isLoading, isLoadingValue, size = "sm", onClick, icon }: props) => {
  return (
    <Button type={type} disabled={disabled} color={color} variant={variant} className={className} size={size} isLoading={isLoading} onPress={onClick}>
      {isLoading ? isLoadingValue : label}
      {icon && icon}
    </Button>
  );
};

export default SubmitButton;
