"use client";
import { getNavigationStructure, SIDEBAR_ITEMS } from "@/config/constants";
import { useStore } from "@/store/store";
import { Link } from "@heroui/link";
import { useTheme } from "next-themes";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import LanguageToggle from "./LanguageToggle";

const Footer = () => {
  const { user } = useStore();
  const { t } = useTranslation();
  const { theme } = useTheme();

  const imageSrc = theme === "dark" ? "/images/ravidD.png" : "/images/ravidL.png";

  // Get the navigation structure
  const { mainNavItems, accordionSections, additionalNavItems, quickLinks } = getNavigationStructure(user, t);

  // Create a single public profile nav item
  // const publicProfileNavItem = user?.user_id;
  // ? {
  //     id: "public-profile-footer",
  //     label: t(SIDEBAR_ITEMS.MY_PUBLIC_PROFILE),
  //     href: `/my/${user.user_id}`,
  //     showInFooter: true,
  //   }
  // : null;

  // Filter items that should show in footer in the same sequence as sidebar
  const footerItems = [
    // First: Clinic and Enterprise Solutions (from accordion sections)
    ...accordionSections
      .filter((section) => section.id !== "public-profile" && section.showInFooter)
      .flatMap((section) => section.items.filter((item) => item.showInFooter)),

    // Second: Main Navigation Items
    ...mainNavItems.filter((item) => item.showInFooter),

    // Third: Public Profile as a single item
    // ...(publicProfileNavItem ? [publicProfileNavItem] : []),

    // Fourth: Additional Navigation Items
    ...additionalNavItems.filter((item) => item.showInFooter),
  ];

  return (
    <footer className="w-full relative z-10 dark:bg-slate-950 text-slate-800 dark:text-slate-200 mt-auto">
      {user?.user_id && (
        <div className="lg:hidden">
          {/* Logo and Language Section */}
          <div className="flex items-center justify-between p-4">
            <Link href={`${user && user?.user_id ? `/my/${user?.user_id}/home` : "/"}`} className="block">
              <Image src={imageSrc} alt="R.A.V.I.D. Logo" width={96} height={96} />
            </Link>
            <div className="flex w-1/3 items-center justify-center">
              <LanguageToggle />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 p-4">
            {/* Dashboard Shortcuts Column */}
            <div>
              <h2 className="text-base font-medium mb-3">{t("ravid.footer.shortcuts")}</h2>
              <nav className="flex flex-col space-y-2">
                {footerItems.map((item) => (
                  <Link key={item.id} href={item.href || ""} className="text-sm text-blue-400 hover:text-blue-300">
                    {item.label}
                  </Link>
                ))}
              </nav>
            </div>

            {/* Quick Links Column */}
            <div>
              <h2 className="text-base font-medium mb-3">{t("ravid.footer.quickLinks")}</h2>
              <nav className="flex flex-col space-y-2">
                {quickLinks.map((link) => (
                  <Link key={link.id} href={link.href || ""} className="text-sm text-blue-400 hover:text-blue-300">
                    {link.label}
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Add divider line */}
      <div className="h-[1.5px] bg-gray-200 dark:bg-gray-800"></div>
      <div className="px-4 py-3">
        <div className="text-center text-xs font-light tracking-tight">
          <p className="text-center">Copyright © {new Date().getFullYear()} R.A.V.I.D. LLC. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
