"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

// Preload các routes quan trọng
const IMPORTANT_ROUTES = [
  "/signin",
  "/signup", 
  "/about",
  "/communities",
  "/solutions",
];

export default function RoutePreloader() {
  const router = useRouter();

  useEffect(() => {
    // Preload routes sau 2 giây để không ảnh hưởng initial load
    const timer = setTimeout(() => {
      IMPORTANT_ROUTES.forEach((route) => {
        router.prefetch(route);
      });
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  return null;
}
