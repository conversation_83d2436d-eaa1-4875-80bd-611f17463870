"use client";

import { cn } from "@/lib/utils";
import { useIsFetching, useIsMutating } from "@tanstack/react-query";

type LoaderProps = {
  className?: string;
};

const Loader = ({ className }: LoaderProps) => {
  const isFetching = useIsFetching();
  const isMutating = useIsMutating();
  if (!isFetching && !isMutating) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full z-50">
      <div className={cn("w-full h-[1.5px] bg-gray-800 relative overflow-hidden z-50 top-0", className)}>
        <div className="absolute h-full from-red-600 to-yellow-700 bg-gradient-to-r loading-animation">
          <style>{`
          @tailwind components;
          
          @layer components {
            .loading-animation {
              animation: loading 2s ease-out forwards;
            }
          }
          
          @keyframes loading {
            0% {
              width: 0%;
            }
            20% {
              width: 20%;
            }
            60% {
              width: 60%;
            }
            90% {
              width: 90%;
            }
            100% {
              width: 100%;
            }
          }
        `}</style>
        </div>
      </div>
    </div>
  );
};

export default Loader;
