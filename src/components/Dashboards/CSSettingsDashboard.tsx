import Tabs from "@/components/Tabs/TabComponent";
import {
  AdminInfoTab,
  AppointmentInfoTab,
  BillingTab,
  ClinicInfoTab,
  CommunicationTab,
  ManageUsersTab,
  SubscriptionTab,
  VerificationTab,
} from "../Tabs/CSSettingsDashboardTabs";
import { useSearchParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { Card } from "@heroui/card";
import Disclaimer from "../Disclaimer";

export const CSSettingsDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState(searchParams.get("settingsTab") || "ClinicInfo");

  const tabs = [
    {
      key: "AdminInfo",
      title: "Admin Info",
      content: <AdminInfoTab />,
    },
    {
      key: "ClinicInfo",
      title: "Clinic Info",
      content: (
        <>
          <Disclaimer />
          <ClinicInfoTab />
        </>
      ),
    },
    {
      key: "ManageUsers",
      title: "Manage Users",
      content: (
        <>
          <Disclaimer />
          <ManageUsersTab />
        </>
      ),
    },
    {
      key: "AppointmentInfo",
      title: "Appointment Info",
      content: (
        <>
          <Disclaimer />
          <AppointmentInfoTab />
        </>
      ),
    },
    {
      key: "Verification",
      title: "Verification",
      content: (
        <>
          <Disclaimer />
          <VerificationTab />
        </>
      ),
    },
    {
      key: "Billing",
      title: "Billing",
      content: (
        <>
          <Disclaimer />
          <BillingTab />
        </>
      ),
    },
    {
      key: "Subscription",
      title: "Subscription",
      content: (
        <>
          <Disclaimer />
          <SubscriptionTab />
        </>
      ),
    },
    {
      key: "Communication",
      title: "Communication",
      content: (
        <>
          <Disclaimer />
          <CommunicationTab />
        </>
      ),
    },
  ];

  useEffect(() => {
    // Update URL when tab changes
    const params = new URLSearchParams(searchParams);
    params.set("settingsTab", currentTab);
    router.replace(`?${params.toString()}`, { scroll: false });
  }, [currentTab, router, searchParams]);

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("settingsTab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        variant="underlined"
        color="primary"
        aria-label="Settings tabs"
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        radius="lg"
      />
    </Card>
  );
};
