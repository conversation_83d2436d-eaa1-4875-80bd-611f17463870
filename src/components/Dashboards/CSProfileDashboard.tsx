"use client";

import {
  About,
  OurDoctors,
  OurHours,
  OurLocation,
  OurServices,
  Research,
  Reviews,
  PatientsAndFamilies,
  Awards,
  HealthCareProfessionals,
} from "@/components/Tabs/CSProfileTabs";
import Tabs from "@/components/Tabs/TabComponent";
import { Button } from "@heroui/react";
import { Card } from "@heroui/card";
import { useSearchParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Disclaimer from "../Disclaimer";

export const CSProfileDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState(searchParams.get("profileTab") || "About");

  const tabs = [
    {
      key: "About",
      title: "About",
      content: <About />,
    },
    {
      key: "OurDoctors",
      title: "Our Doctors",
      // add disclaimer
      content: (
        <>
          <Disclaimer />
          <OurDoctors />
        </>
      ),
    },
    {
      key: "OurServices",
      title: "Our Services",
      content: (
        <>
          <Disclaimer />
          <OurServices />
        </>
      ),
    },
    {
      key: "OurLocation",
      title: "Our Location",
      content: (
        <>
          <Disclaimer />
          <OurLocation />
        </>
      ),
    },
    {
      key: "OurHours",
      title: "Our Hours",
      content: (
        <>
          <Disclaimer />
          <OurHours />
        </>
      ),
    },
    {
      key: "PatientsAndFamilies",
      title: "Patients & Families",
      content: (
        <>
          <Disclaimer />
          <PatientsAndFamilies />
        </>
      ),
    },
    {
      key: "Reviews",
      title: "Reviews",
      content: (
        <>
          <Disclaimer />
          <Reviews />
        </>
      ),
    },
    {
      key: "Research",
      title: "Research",
      content: (
        <>
          <Disclaimer />
          <Research />
        </>
      ),
    },
    {
      key: "Awards",
      title: "Awards",
      content: (
        <>
          <Disclaimer />
          <Awards />
        </>
      ),
    },
    {
      key: "HealthCareProfessionals",
      title: "Healthcare Professionals",
      content: (
        <>
          <Disclaimer />
          <HealthCareProfessionals />
        </>
      ),
    },
  ];

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("profileTab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  useEffect(() => {
    // Update URL when tab changes
    const params = new URLSearchParams(searchParams);
    params.set("profileTab", currentTab);
    router.replace(`?${params.toString()}`, { scroll: false });
  }, [currentTab, router, searchParams]);

  return (
    <section>
      <div className="relative h-[300px] mb-6 rounded-xl overflow-hidden">
        <div className="absolute inset-0">
          <Image src="/images/bg-cover.png" alt="Clinic Cover" fill className="object-cover rounded-xl" priority />
          <div className="absolute inset-0 bg-gradient-to-b from-black/40 to-black/60" />
        </div>

        <div className="relative h-full p-6 flex flex-col justify-between">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Image src="/images/c-logo.png" alt="Logo" width={40} height={40} className="rounded-full bg-white p-1" />
              <span className="text-white text-sm">New City Heart Clinic & Associates</span>
            </div>
            <Button variant="bordered" className="text-white border-white hover:bg-white/10">
              Contact Us
            </Button>
          </div>

          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-white">New City Heart Clinic & Associates</h1>
            <p className="text-sm text-white/90">Specialties: Cardiology, Internal Medicine</p>
            <p className="text-sm text-white/90">Languages: ENG, ITA, ESP</p>
            <Button className="mt-4">Book Appointment</Button>
          </div>
        </div>
      </div>
      <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
        <Tabs
          tabs={tabs}
          selectedKey={currentTab}
          onSelectionChange={handleTabChange}
          variant="underlined"
          color="primary"
          aria-label="Profile tabs"
          classNames={{
            cursor: "bg-blue-500",
            tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
            tabContent: "text-gray-700 dark:text-white wrap",
            tabList: "flex flex-wrap md:flex-nowrap",
          }}
          radius="lg"
        />
      </Card>
    </section>
  );
};
