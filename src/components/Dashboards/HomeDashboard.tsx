"use client";
import { useSearchParams, useRouter } from "next/navigation";
import { useState } from "react";
import Tabs from "@/components/Tabs/TabComponent";
import { Card } from "@heroui/card";
import { Ai, Appointments, Diagnosis, MobileHealth, Notes, Prescriptions, PreventiveServices } from "../Tabs/HomeTabs";
import { useTranslation } from "react-i18next";

// Define tab IDs that are language independent
const TAB_IDS = {
  AI: "ai",
  APPOINTMENTS: "appointments",
  DIAGNOSIS: "diagnosis",
  MOBILE_HEALTH: "mobileHealth",
  NOTES: "notes",
  PRESCRIPTIONS: "prescriptions",
  PREVENTIVE_SERVICES: "preventiveServices",
} as const;

type TabId = typeof TAB_IDS[keyof typeof TAB_IDS];

interface TabItem {
  key: TabId;
  title: string;
  content: React.ReactNode;
}

const HomeDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState<TabId>(() => {
    const tabFromParams = searchParams.get("tab") as TabId | null;
    const defaultTab = TAB_IDS.AI;
    if (!tabFromParams) return defaultTab;
    return Object.values(TAB_IDS).includes(tabFromParams) ? tabFromParams : defaultTab;
  });

  const tabs: TabItem[] = [
    { key: TAB_IDS.AI, title: t("ravid.homeDashboard.tabs.ai"), content: <Ai /> },
    { key: TAB_IDS.APPOINTMENTS, title: t("ravid.homeDashboard.tabs.appointments"), content: <Appointments /> },
    { key: TAB_IDS.DIAGNOSIS, title: t("ravid.homeDashboard.tabs.diagnosis"), content: <Diagnosis /> },
    { key: TAB_IDS.MOBILE_HEALTH, title: t("ravid.homeDashboard.tabs.mobileHealth"), content: <MobileHealth /> },
    { key: TAB_IDS.NOTES, title: t("ravid.homeDashboard.tabs.notes"), content: <Notes /> },
    { key: TAB_IDS.PRESCRIPTIONS, title: t("ravid.homeDashboard.tabs.prescriptions"), content: <Prescriptions /> },
    { key: TAB_IDS.PREVENTIVE_SERVICES, title: t("ravid.homeDashboard.tabs.preventiveServices"), content: <PreventiveServices /> },
  ];

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString() as TabId;
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        variant="underlined"
        color="primary"
        aria-label="Options"
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        radius="lg"
      />
    </Card>
  );
};

export default HomeDashboard;
