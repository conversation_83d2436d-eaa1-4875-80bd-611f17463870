"use client";

import Disclaimer from "../Disclaimer";
import React, { useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Tabs from "@/components/Tabs/TabComponent";
import { PermissionManager } from "../Tabs/PermissionsDashboardTabs";
import { Card } from "@heroui/card";

interface TabItem {
  key: string;
  title: string;
  content: React.ReactNode;
}

const tabs: TabItem[] = [
  {
    key: "permission-manager",
    title: "Permission Manager",
    content: (
      <div className="p-1">
        <Disclaimer />
        <PermissionManager />
      </div>
    ),
  },
];

export default function PermissionsDashboard() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState(() => {
    const tabFromParams = searchParams.get("dashboardTab");
    const defaultTab = "permission-manager";
    if (!tabFromParams) return defaultTab;
    return tabs.find((tab) => tab.title === tabFromParams)?.key || defaultTab;
  });

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        variant="underlined"
        color="primary"
        aria-label="Dashboard tabs"
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        radius="lg"
      />
    </Card>
  );
}
