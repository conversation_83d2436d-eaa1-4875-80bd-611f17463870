"use client";
import { useState } from "react";
import Tabs from "@/components/Tabs/TabComponent";
import { useRouter, useSearchParams } from "next/navigation";
import { AdminInfoTab, CommunicationsTab, CompanyInfoTab, LicenseManagerTab, LicenseTab, VerificationTab } from "../Tabs/ESSettingsTabs";
import Disclaimer from "../Disclaimer";
import { Card } from "@heroui/card";

interface TabItem {
  key: string;
  title: string;
  content: React.ReactNode;
}

const tabs: TabItem[] = [
  {
    key: "admin-info",
    title: "Admin Info",
    content: <AdminInfoTab />,
  },
  {
    key: "company-info",
    title: "Company Info",
    content: <CompanyInfoTab />,
  },
  {
    key: "communications",
    title: "Communications",
    content: (
      <>
        <Disclaimer />
        <CommunicationsTab />
      </>
    ),
  },
  {
    key: "license",
    title: "License",
    content: (
      <>
        <Disclaimer />
        <LicenseTab />
      </>
    ),
  },
  {
    key: "license-manager",
    title: "License Manager",
    content: (
      <>
        <Disclaimer />
        <LicenseManagerTab />
      </>
    ),
  },
  {
    key: "verification",
    title: "Verification",
    content: (
      <>
        <Disclaimer />
        <VerificationTab />
      </>
    ),
  },
];

const ESSettings = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState(() => {
    const tabFromParams = searchParams.get("tab");
    const defaultTab = "license";
    if (!tabFromParams) return defaultTab;
    return tabs.find((tab) => tab.title.toLowerCase() === tabFromParams.toLowerCase())?.key || defaultTab;
  });

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        variant="underlined"
        color="primary"
        aria-label="Settings tabs"
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        radius="lg"
      />
    </Card>
  );
};

export default ESSettings;
