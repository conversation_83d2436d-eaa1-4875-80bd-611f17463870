"use client";
import PaymentGateway from "@/components/Tabs/PaymentsTabs/PaymentGateway";
import Tabs from "@/components/Tabs/TabComponent";
import { Card } from "@heroui/card";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";

// Define tab IDs that are language independent
const TAB_IDS = {
  PAYMENT_GATEWAY: "paymentGateway",
  PAY_DONATE_MANAGER: "payDonateManager",
} as const;

const tabs = [
  {
    key: TAB_IDS.PAYMENT_GATEWAY,
    title: "ravid.publicProfile.paymentGateway",
    content: <PaymentGateway />,
  },
  {
    key: TAB_IDS.PAY_DONATE_MANAGER,
    title: "ravid.publicProfile.payDonateManager",
    badge: "2",
    content: "",
  },
];

export default function PaymentDashboard() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState<string>(() => {
    const tabFromParams = searchParams.get("tab");
    const defaultTab = TAB_IDS.PAYMENT_GATEWAY;
    if (!tabFromParams) return defaultTab;
    return tabs.find((tab) => tab.title.toLowerCase() === tabFromParams.toLowerCase())?.key || defaultTab;
  });

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        variant="underlined"
        color="primary"
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        aria-label="Payment Options"
        radius="lg"
      />
    </Card>
  );
}
