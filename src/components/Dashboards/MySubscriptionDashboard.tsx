"use client";
import Disclaimer from "@/components/Disclaimer";
import { ServicesTab, SubscriptionTab } from "@/components/Tabs/MySubscriptionTabs";
import { Card } from "@heroui/card";
import Tabs from "@/components/Tabs/TabComponent";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { useTranslation } from "react-i18next";

// Define tab IDs that are language independent
const TAB_IDS = {
  BILLINGS: "billings",
  SERVICES: "services",
  SUBSCRIPTIONS: "subscriptions",
} as const;

type TabId = typeof TAB_IDS[keyof typeof TAB_IDS];

interface TabItem {
  key: TabId;
  title: string;
  content: React.ReactNode;
}

export const Subscriptions = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState<TabId>(() => {
    const tabFromParams = searchParams.get("tab") as TabId | null;
    const defaultTab = TAB_IDS.SUBSCRIPTIONS;
    if (!tabFromParams) return defaultTab;
    return Object.values(TAB_IDS).includes(tabFromParams) ? tabFromParams : defaultTab;
  });

  const tabs: TabItem[] = [
    // {
    //   key: TAB_IDS.BILLINGS,
    //   title: t("ravid.mySubscriptionDashboard.tabs.billings"),
    //   content: (
    //     <div className="p-1 rounded-2xl">
    //       <Disclaimer />
    //       <PaymentTab />
    //     </div>
    //   ),
    // },
    {
      key: TAB_IDS.SERVICES,
      title: t("ravid.mySubscriptionDashboard.tabs.services"),
      content: (
        <div className="p-1 rounded-2xl">
          <Disclaimer />
          <ServicesTab />
        </div>
      ),
    },
    {
      key: TAB_IDS.SUBSCRIPTIONS,
      title: t("ravid.mySubscriptionDashboard.tabs.subscriptions"),
      content: (
        <div className="p-1 rounded-2xl">
          <SubscriptionTab />
        </div>
      ),
    },
  ];

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString() as TabId;
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        variant="underlined"
        color="primary"
        aria-label="User subscription tabs"
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        radius="lg"
      />
    </Card>
  );
};
