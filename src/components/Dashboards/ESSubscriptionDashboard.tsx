"use client";

import Tabs from "@/components/Tabs/TabComponent";
import React from "react";
import Disclaimer from "../Disclaimer";
import { BillingTab, ESSubscriptionTab } from "../Tabs/ESSubscrptionTabs";
import { useSearchParams, useRouter } from "next/navigation";
import { Card } from "@heroui/card";

interface TabItem {
  key: string;
  title: string;
  content: React.ReactNode;
}

const tabs: TabItem[] = [
  {
    key: "billing",
    title: "Billing",
    content: (
      <>
        <Disclaimer />
        <BillingTab />
      </>
    ),
  },
  {
    key: "subscriptions",
    title: "Subscriptions",
    content: (
      <>
        <Disclaimer />
        <ESSubscriptionTab />
      </>
    ),
  },
];

const ESSubscription = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = React.useState(() => {
    const tabFromParams = searchParams.get("subscriptionTab");
    const defaultTab = "subscriptions";
    if (!tabFromParams) return defaultTab;
    return tabs.find((tab) => tab.title.toLowerCase() === tabFromParams.toLowerCase())?.key || defaultTab;
  });

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("subscriptionTab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        variant="underlined"
        color="primary"
        aria-label="Subscription Tabs"
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        radius="lg"
      />
    </Card>
  );
};

export default ESSubscription;
