"use client";
import { <PERSON>, <PERSON>, CardBody } from "@heroui/react";
import useAlerts from "@/hooks/useAlert";
import Alert from "@/components/Disclaimers/Alert";

const TelemedicineDashboard = () => {
  const { data: alerts } = useAlerts();
  // Find alert for this tab
  const tabAlert = alerts?.find((alert) => alert.tab === "TELEMEDICINE" && alert.end_date && new Date(alert.end_date) > new Date());

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <div className="p-2">
        {tabAlert && <Alert alert={tabAlert} />}
        <div className="flex flex-col gap-1">
          <h1 className="blue-heading">Telemedicine Services</h1>
          <p>Telemedicine allows you to connect with healthcare providers remotely through video consultations.</p>
        </div>
        <span className="text-xs mt-3">
          <Link className="text-xs" href="https://www.hopkinsmedicine.org/health/treatment-tests-and-therapies/benefits-of-telemedicine">
            Benefits of Telemedicine
          </Link>{" "}
          Source:{" "}
          <Link className="text-xs" href="https://www.hopkinsmedicine.org/health/treatment-tests-and-therapies/benefits-of-telemedicine">
            Hopkins Medicine
          </Link>
        </span>

        <div className="flex gap-4 mt-4">
          <Card className="dark:bg-transparent border rounded-xl dark:border-slate-900">
            <CardBody className="flex flex-col gap-1">
              <h2>Virtual Consultations</h2>
              <p>Schedule video appointments with your healthcare providers from the comfort of your home.</p>
            </CardBody>
          </Card>
          <Card className="dark:bg-transparent border rounded-xl dark:border-slate-900">
            <CardBody className="flex flex-col gap-1">
              <h2>Remote Monitoring</h2>
              <p>Track your health metrics and share them with your medical team in real-time.</p>
            </CardBody>
          </Card>
        </div>
      </div>
    </Card>
  );
};

export default TelemedicineDashboard;
