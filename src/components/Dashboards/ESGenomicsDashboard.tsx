"use client";
import { Card } from "@heroui/card";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Collaborations, DNAAnalysis, DnaServices, FileManager, GSAnalysisManager } from "../Tabs/DNADashboardTabs";
import Tabs, { TabItem } from "../Tabs/TabComponent";

// Create a custom event for tab changes
export const changeDNATab = (tabName: string) => {
  // Dispatch a custom event that the DNADashboard will listen for
  if (typeof window !== "undefined") {
    // Then dispatch the event
    const event = new CustomEvent("dna-tab-change", { detail: { tabName } });
    window.dispatchEvent(event);
  }
};

const TAB_IDS = {
  DNA_DASHBOARD: "dnaDashboard",
  GS_FILE_MANAGER: "gsFileManager",
  GS_ANALYSIS_MANAGER: "gsAnalysisManager",
  MESSAGES_COLLABORATIONS: "messagesCollaborations",
  SERVICES: "services",
} as const;

const tabs: TabItem[] = [
  {
    key: TAB_IDS.DNA_DASHBOARD,
    title: "DNA Dashboard",
    content: <DNAAnalysis />,
  },
  {
    key: TAB_IDS.GS_FILE_MANAGER,
    title: "GS File Manager",
    content: <FileManager />,
  },
  {
    key: TAB_IDS.GS_ANALYSIS_MANAGER,
    title: "GS Analysis Manager",
    content: <GSAnalysisManager />,
  },
  {
    key: TAB_IDS.MESSAGES_COLLABORATIONS,
    title: "Messages-Collaborations",
    content: <Collaborations />,
    badge: "2",
  },
  {
    key: TAB_IDS.SERVICES,
    title: "Services",
    content: <DnaServices />,
  },
];

const ESGenomicsDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Helper functions to convert between display names and URL-friendly names
  const toUrlParam = (name: string) => name.replace(/\s+/g, "-");
  const fromUrlParam = (param: string) => param.replace(/-+/g, " ");

  // Get initial tab from URL parameter or default to "DNA Dashboard"
  const initialTab = searchParams.get("esGenomicsTab") ? fromUrlParam(searchParams.get("esGenomicsTab") || "") : TAB_IDS.DNA_DASHBOARD;

  const [currentTab, setCurrentTab] = useState(initialTab);

  // Listen for custom tab change events
  useEffect(() => {
    // Check localStorage first in case an event was fired before component mounted
    const storedTab = localStorage.getItem("esGenomics_active_tab");
    if (storedTab && tabs.some((tab) => tab.title === storedTab) && currentTab !== storedTab) {
      setCurrentTab(storedTab);
    }

    const handleTabChange = (event: any) => {
      const { tabName } = event.detail;
      if (tabName && tabs.some((tab) => tab.title === tabName)) {
        setCurrentTab(tabName);
      }
    };

    window.addEventListener("es-genomics-tab-change", handleTabChange);
    return () => {
      window.removeEventListener("es-genomics-tab-change", handleTabChange);
    };
  }, []);

  // Update URL when tab changes - just like CSProfileDashboard
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    params.set("esGenomicsTab", toUrlParam(currentTab));
    router.replace(`?${params.toString()}`, { scroll: false });
  }, [currentTab, router, searchParams]);

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("esGenomicsTab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };
  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        aria-label="ES Genomics Dashboard Options"
        variant="underlined"
        color="primary"
        radius="lg"
      />
    </Card>
  );
};

export default ESGenomicsDashboard;
