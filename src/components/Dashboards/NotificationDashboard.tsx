"use client";
import React, { useState, useEffect } from "react";
import { Card, Tabs as TabsComponent, Tab, CardBody, CardHeader, Input, Button } from "@heroui/react";
import Tabs from "@/components/Tabs/TabComponent";
import { useAppointmentAvailability } from "@/hooks/use-appointments";
import toast from "react-hot-toast";
import { Pencil } from "lucide-react";
import { useSearchParams, useRouter } from "next/navigation";

export default function NotificationDashboard() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // State for email inputs
  const [primaryAppointmentEmail, setPrimaryAppointmentEmail] = useState("");
  const [secondaryAppointmentEmail, setSecondaryAppointmentEmail] = useState("");
  const [primaryMessageEmail, setPrimaryMessageEmail] = useState("");
  const [secondaryMessageEmail, setSecondaryMessageEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [dataInitialized, setDataInitialized] = useState(false);

  // Edit mode states
  const [editingPrimaryAppointment, setEditingPrimaryAppointment] = useState(false);
  const [editingSecondaryAppointment, setEditingSecondaryAppointment] = useState(false);
  const [editingPrimaryMessage, setEditingPrimaryMessage] = useState(false);
  const [editingSecondaryMessage, setEditingSecondaryMessage] = useState(false);

  // Get appointment hooks
  const { createDoctorContactInfoMutation, updateDoctorContactInfoMutation, doctorContactInfo, isLoading: isDataLoading } = useAppointmentAvailability();

  // Load existing data when available
  useEffect(() => {
    if (doctorContactInfo && !dataInitialized) {
      setPrimaryAppointmentEmail(doctorContactInfo[0]?.primary_email || "");
      setSecondaryAppointmentEmail(doctorContactInfo[0]?.secondary_email || "");
      setPrimaryMessageEmail(doctorContactInfo[0]?.primary_email || "");
      setSecondaryMessageEmail(doctorContactInfo[0]?.secondary_email || "");
      setDataInitialized(true);
    }
  }, [doctorContactInfo, dataInitialized]);

  // Handlers for saving changes
  const saveAppointmentChanges = () => {
    setLoading(true);
    const contactData = {
      primary_email: primaryAppointmentEmail || "",
      secondary_email: secondaryAppointmentEmail || "",
      is_primary_email_active: true,
      is_secondary_email_active: true,
    };

    if (doctorContactInfo?.id) {
      // Update existing contact info
      updateDoctorContactInfoMutation(
        {
          ...contactData,
          id: doctorContactInfo.id,
        },
        {
          onSuccess: () => {
            toast.success("Appointment notification settings saved successfully!");
            setLoading(false);
            // Disable edit mode after successful save
            setEditingPrimaryAppointment(false);
            setEditingSecondaryAppointment(false);
          },
          onError: (error) => {
            console.error("Error saving appointment notifications:", error);
            toast.error("Failed to save appointment notification settings");
            setLoading(false);
          },
        }
      );
    } else {
      // Create new contact info
      createDoctorContactInfoMutation(contactData, {
        onSuccess: () => {
          toast.success("Appointment notification settings saved successfully!");
          setLoading(false);
          // Disable edit mode after successful save
          setEditingPrimaryAppointment(false);
          setEditingSecondaryAppointment(false);
        },
        onError: (error) => {
          console.error("Error saving appointment notifications:", error);
          toast.error("Failed to save appointment notification settings");
          setLoading(false);
        },
      });
    }
  };

  const saveMessageChanges = () => {
    setLoading(true);
    const contactData = {
      primary_email: primaryMessageEmail || "",
      secondary_email: secondaryMessageEmail || "",
      is_primary_email_active: true,
      is_secondary_email_active: true,
    };

    if (doctorContactInfo?.id) {
      // Update existing contact info
      updateDoctorContactInfoMutation(
        {
          ...contactData,
          id: doctorContactInfo.id,
        },
        {
          onSuccess: () => {
            toast.success("Message notification settings saved successfully!");
            setLoading(false);
            // Disable edit mode after successful save
            setEditingPrimaryMessage(false);
            setEditingSecondaryMessage(false);
          },
          onError: (error) => {
            console.error("Error saving message notifications:", error);
            toast.error("Failed to save message notification settings");
            setLoading(false);
          },
        }
      );
    } else {
      // Create new contact info
      createDoctorContactInfoMutation(contactData, {
        onSuccess: () => {
          toast.success("Message notification settings saved successfully!");
          setLoading(false);
          // Disable edit mode after successful save
          setEditingPrimaryMessage(false);
          setEditingSecondaryMessage(false);
        },
        onError: (error) => {
          console.error("Error saving message notifications:", error);
          toast.error("Failed to save message notification settings");
          setLoading(false);
        },
      });
    }
  };

  const renderNotificationsContent = () => (
    <div className="flex flex-col items-center gap-6 sm:p-10 p-4">
      <TabsComponent
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white p-4",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        aria-label="Options"
        disableAnimation
      >
        <Tab key="appointment" title="Appointment Notifications">
          <Card className="w-full dark:bg-slate-950 border dark:border-slate-800">
            <CardHeader className="flex flex-col gap-2 items-start">
              <h1>Appointment Notification</h1>
              <p>Add a primary & secondary email where you wish to receive or send all Appointment related notifications.</p>
            </CardHeader>
            <CardBody className="flex flex-col gap-2">
              <div className="flex items-center w-3/5 gap-2 relative">
                <Input
                  classNames={{
                    base: "w-full",
                    input: "text-xs",
                    label: "text-xs text-gray-500",
                    inputWrapper: "dark:bg-slate-900",
                  }}
                  size="sm"
                  label="Primary Email"
                  value={primaryAppointmentEmail}
                  onChange={(e) => setPrimaryAppointmentEmail(e.target.value)}
                  isDisabled={(primaryAppointmentEmail && !editingPrimaryAppointment) || (isDataLoading && !dataInitialized)}
                  placeholder={isDataLoading && !dataInitialized ? "Loading..." : "Enter primary email"}
                  autoComplete="off"
                />
                {primaryAppointmentEmail && !editingPrimaryAppointment && (
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    className="absolute right-2 top-1/2 -translate-y-1/3"
                    onPress={() => setEditingPrimaryAppointment(true)}
                  >
                    <Pencil className="w-4 h-4 text-gray-500" />
                  </Button>
                )}
              </div>

              <div className="flex items-center w-3/5 gap-2 relative">
                <Input
                  classNames={{
                    base: "w-full",
                    input: "text-xs",
                    label: "text-xs text-gray-500",
                    inputWrapper: "dark:bg-slate-900",
                  }}
                  size="sm"
                  label="Secondary Email"
                  value={secondaryAppointmentEmail}
                  onChange={(e) => setSecondaryAppointmentEmail(e.target.value)}
                  isDisabled={(secondaryAppointmentEmail && !editingSecondaryAppointment) || (isDataLoading && !dataInitialized)}
                  placeholder={isDataLoading && !dataInitialized ? "Loading..." : "Enter secondary email"}
                  autoComplete="off"
                />
                {secondaryAppointmentEmail && !editingSecondaryAppointment && (
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    className="absolute right-2 top-1/2 -translate-y-1/3"
                    onPress={() => setEditingSecondaryAppointment(true)}
                  >
                    <Pencil className="w-4 h-4 text-gray-500" />
                  </Button>
                )}
              </div>

              <div className="mt-4">
                <Button
                  color="primary"
                  size="sm"
                  isLoading={loading || (isDataLoading && !dataInitialized)}
                  onPress={saveAppointmentChanges}
                  isDisabled={!(editingPrimaryAppointment || editingSecondaryAppointment)}
                >
                  Save Changes
                </Button>
              </div>
            </CardBody>
          </Card>
        </Tab>
        <Tab key="message" title="Message Notifications">
          <Card className="w-full dark:bg-slate-950 border dark:border-slate-800">
            <CardHeader className="flex flex-col gap-2 items-start">
              <h1>Message Notification</h1>
              <p>Add a primary & secondary email where you wish to receive or send all Message related notifications.</p>
            </CardHeader>
            <CardBody className="flex flex-col gap-2">
              <div className="flex items-center w-3/5 gap-2 relative">
                <Input
                  classNames={{
                    base: "w-full",
                    input: "text-xs",
                    label: "text-xs text-gray-500",
                    inputWrapper: "dark:bg-slate-900",
                  }}
                  size="sm"
                  label="Primary Email"
                  value={primaryMessageEmail}
                  onChange={(e) => setPrimaryMessageEmail(e.target.value)}
                  isDisabled={(primaryMessageEmail && !editingPrimaryMessage) || (isDataLoading && !dataInitialized)}
                  placeholder={isDataLoading && !dataInitialized ? "Loading..." : "Enter primary email"}
                  autoComplete="off"
                />
                {primaryMessageEmail && !editingPrimaryMessage && (
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    className="absolute right-2 top-1/2 -translate-y-1/3"
                    onPress={() => setEditingPrimaryMessage(true)}
                  >
                    <Pencil className="w-4 h-4 text-gray-500" />
                  </Button>
                )}
              </div>

              <div className="flex items-center w-3/5 gap-2 relative">
                <Input
                  classNames={{
                    base: "w-full",
                    input: "text-xs",
                    label: "text-xs text-gray-500",
                    inputWrapper: "dark:bg-slate-900",
                  }}
                  size="sm"
                  label="Secondary Email"
                  value={secondaryMessageEmail}
                  onChange={(e) => setSecondaryMessageEmail(e.target.value)}
                  isDisabled={(secondaryMessageEmail && !editingSecondaryMessage) || (isDataLoading && !dataInitialized)}
                  placeholder={isDataLoading && !dataInitialized ? "Loading..." : "Enter secondary email"}
                  autoComplete="off"
                />
                {secondaryMessageEmail && !editingSecondaryMessage && (
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    className="absolute right-2 top-1/2 -translate-y-1/3"
                    onPress={() => setEditingSecondaryMessage(true)}
                  >
                    <Pencil className="w-4 h-4 text-gray-500" />
                  </Button>
                )}
              </div>

              <div className="mt-4">
                <Button
                  color="primary"
                  size="sm"
                  isLoading={loading || (isDataLoading && !dataInitialized)}
                  onPress={saveMessageChanges}
                  isDisabled={!(editingPrimaryMessage || editingSecondaryMessage)}
                >
                  Save Changes
                </Button>
              </div>
            </CardBody>
          </Card>
        </Tab>
      </TabsComponent>
    </div>
  );

  const tabs = [
    {
      key: "notifications",
      title: "Notifications",
      content: renderNotificationsContent(),
    },
  ];

  const [currentTab, setCurrentTab] = useState<string>(() => {
    const tabFromParams = searchParams.get("tab");
    const defaultTab = "notifications";
    if (!tabFromParams) return defaultTab;
    return tabs.find((tab) => tab.title.toLowerCase() === tabFromParams.toLowerCase())?.key || defaultTab;
  });

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        variant="underlined"
        color="primary"
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap",
        }}
        aria-label="Notifications Options"
        radius="lg"
        tabs={tabs}
      />
    </Card>
  );
}
