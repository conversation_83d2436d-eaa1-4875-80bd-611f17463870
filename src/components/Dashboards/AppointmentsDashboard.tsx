"use client";
import { Card } from "@heroui/card";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { AppointmentRequests, AppointmentsManager } from "../Tabs/PublicProfileTabs";
import Tabs from "../Tabs/TabComponent";

// Define tab IDs that are language independent
const TAB_IDS = {
  APPOINTMENT_SCHEDULE: "appointmentSchedule",
  APPOINTMENT_REQUESTS: "appointmentRequests",
  NOTIFICATIONS_SETTINGS: "notificationsSettings",
} as const;

const tabs = [
  {
    key: TAB_IDS.APPOINTMENT_SCHEDULE,
    title: "ravid.publicProfile.appointmentSchedule",
    content: <AppointmentsManager />,
  },
  {
    key: TAB_IDS.APPOINTMENT_REQUESTS,
    title: "ravid.publicProfile.appointmentRequests",
    badge: "2",
    content: <AppointmentRequests />,
  },
];

const AppointmentsDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState(searchParams.get("tab") || TAB_IDS.APPOINTMENT_SCHEDULE);

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.key);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs tabs={tabs} variant="underlined" color="primary" selectedKey={currentTab} onSelectionChange={handleTabChange} aria-label="Options" radius="lg" />
    </Card>
  );
};

export default AppointmentsDashboard;
