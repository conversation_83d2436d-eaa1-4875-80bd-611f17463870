"use client";
import { useSearchParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card } from "@heroui/card";
import { useTranslation } from "react-i18next";

import Tabs from "../Tabs/TabComponent";
import AdminAppointments from "../Tabs/Admin";

// Define tab IDs that are language independent
const TAB_IDS = {
  ADMINISTRATION: "administration",
} as const;

const PublicProfileDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState<string>(() => {
    const tabFromParams = searchParams.get("tab");
    const defaultTab = TAB_IDS.ADMINISTRATION;
    if (!tabFromParams) return defaultTab;
    return tabItems.find((tab) => tab.title.toLowerCase() === tabFromParams.toLowerCase())?.key || defaultTab;
  });

  const tabItems = [
    {
      key: TAB_IDS.ADMINISTRATION,
      title: t("ravid.publicProfile.administration"),
      content: <AdminAppointments />,
    },
  ];

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabItems.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        variant="underlined"
        color="primary"
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        classNames={{
          cursor: "bg-blue-500",
          tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
          tabContent: "text-gray-700 dark:text-white wrap",
          tabList: "flex flex-wrap md:flex-nowrap ",
        }}
        placement="top"
        aria-label="Options"
        radius="lg"
        tabs={tabItems}
      />
    </Card>
  );
};

export default PublicProfileDashboard;
