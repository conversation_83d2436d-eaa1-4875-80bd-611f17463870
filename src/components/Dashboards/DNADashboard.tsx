"use client";
import { Card } from "@heroui/card";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Collaborations, DNAAnalysis, DnaServices, FileManager, GSAnalysisManager } from "../Tabs/DNADashboardTabs";
import Tabs, { TabItem } from "../Tabs/TabComponent";

export const TAB_IDS = {
  DNA_DASHBOARD: "dnaDashboard",
  GS_FILE_MANAGER: "gsFileManager",
  GS_ANALYSIS_MANAGER: "gsAnalysisManager",
  MESSAGES_COLLABORATIONS: "messagesCollaborations",
  SERVICES: "services",
} as const;

export const changeDNATab = (tabName: string) => {
  if (typeof window !== "undefined") {
    const event = new CustomEvent("dna-tab-change", { detail: { tabName } });
    window.dispatchEvent(event);
  }
};

const DNADashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get initial tab from URL parameter or default to DNA Dashboard
  const initialTab = searchParams.get("dnaTab") || TAB_IDS.DNA_DASHBOARD;
  const [currentTab, setCurrentTab] = useState(initialTab);

  const tabs: TabItem[] = [
    {
      key: TAB_IDS.DNA_DASHBOARD,
      title: "DNA Dashboard",
      content: <DNAAnalysis />,
    },
    {
      key: TAB_IDS.GS_FILE_MANAGER,
      title: "GS File Manager",
      content: <FileManager />,
    },
    {
      key: TAB_IDS.GS_ANALYSIS_MANAGER,
      title: "GS Analysis Manager",
      content: <GSAnalysisManager />,
    },
    {
      key: TAB_IDS.MESSAGES_COLLABORATIONS,
      title: "Messages-Collaborations",
      content: <Collaborations />,
      badge: "2",
    },
    {
      key: TAB_IDS.SERVICES,
      title: "Services",
      content: <DnaServices />,
    },
  ];

  // Listen for custom tab change events
  useEffect(() => {
    const handleTabChange = (event: any) => {
      const { tabName } = event.detail;
      if (tabName && tabs.some((tab) => tab.key === tabName)) {
        setCurrentTab(tabName);
      }
    };

    window.addEventListener("dna-tab-change", handleTabChange);
    return () => {
      window.removeEventListener("dna-tab-change", handleTabChange);
    };
  }, []);

  // Update URL when tab changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    params.set("dnaTab", currentTab);
    router.replace(`?${params.toString()}`, { scroll: false });
  }, [currentTab, router, searchParams]);

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("dnaTab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        aria-label="DNA Dashboard Options"
        variant="underlined"
        color="primary"
        radius="lg"
      />
    </Card>
  );
};

export default DNADashboard;
