"use client";

import Disclaimer from "../Disclaimer";
import React, { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Tabs from "../Tabs/TabComponent";
import { MetricsTab } from "../Tabs/ESDashboardTabs";
import { Card } from "@heroui/card";

const EnterpriseMetricsDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState(searchParams.get("dashboardTab") || "Metrics");

  useEffect(() => {
    // Update URL when tab changes
    const params = new URLSearchParams(searchParams);
    params.set("dashboardTab", currentTab);
    router.replace(`?${params.toString()}`, { scroll: false });
  }, [currentTab, router, searchParams]);

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("dashboardTab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const tabs = [
    {
      key: "Metrics",
      title: "Metrics",
      content: (
        <>
          <Disclaimer />
          <MetricsTab />
        </>
      ),
    },
  ];

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs tabs={tabs} selectedKey={currentTab} onSelectionChange={handleTabChange} aria-label="Dashboard tabs" />
    </Card>
  );
};

export default EnterpriseMetricsDashboard;
