"use client";
import Tabs, { TabItem } from "@/components/Tabs/TabComponent";
import { useGetMessages } from "@/hooks/public-profile/useProfileCategory";
import { useStore } from "@/store/store";
import { Card } from "@heroui/card";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Message } from "../Tabs/PublicProfileTabs";

// Define tab IDs that are language independent
const TAB_IDS = {
  MESSAGE_MANAGER: "messageManager",
} as const;

const MessageDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState(searchParams.get("tab") || TAB_IDS.MESSAGE_MANAGER);
  const { user } = useStore();
  const { data: messages } = useGetMessages(user?.user_id);

  // Calculate unread messages count
  const unreadCount = messages?.filter((message: any) => !message.is_read)?.length || 0;

  const tabs: TabItem[] = [
    {
      key: TAB_IDS.MESSAGE_MANAGER,
      title: "ravid.publicProfile.messages",
      badge: unreadCount > 0 ? unreadCount.toString() : undefined,
      content: <Message />,
    },
  ];

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="w-full dark:bg-slate-950 border rounded-xl dark:border-gray-800">
      <Tabs tabs={tabs} selectedKey={currentTab} onSelectionChange={handleTabChange} variant="underlined" />
    </Card>
  );
};

export default MessageDashboard;
