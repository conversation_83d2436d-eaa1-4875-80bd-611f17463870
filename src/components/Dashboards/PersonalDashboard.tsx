"use client";
import Tabs, { TabItem } from "@/components/Tabs/TabComponent";
import { Card } from "@heroui/card";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import {
  AccessSecurity,
  AccountSharing,
  BillingsTab,
  Communications,
  Emergency,
  Insurance,
  MedicalTeam,
  Personal,
  QRCode,
  Storage,
  Verification,
} from "../Tabs/SettingsDashboard";

// Define tab IDs that are language independent
const TAB_IDS = {
  PERSONAL: "personal",
  EMERGENCY: "emergency",
  MEDICAL_TEAM: "medicalTeam",
  INSURANCE: "insurance",
  QR_CODE: "qrCode",
  ACCESS_SECURITY: "accessSecurity",
  VERIFICATION: "verification",
  COMMUNICATIONS: "communications",
  ACCOUNT_SHARING: "accountSharing",
  STORAGE: "storage",
  BILLINGS: "billings",
} as const;

type TabId = typeof TAB_IDS[keyof typeof TAB_IDS];

const tabs: TabItem[] = [
  {
    key: TAB_IDS.PERSONAL,
    title: "ravid.ravidSettings.tabs.personal",
    content: <Personal />,
    titleClassName: "text-orange-500",
  },
  {
    key: TAB_IDS.EMERGENCY,
    title: "ravid.ravidSettings.tabs.emergency",
    content: <Emergency />,
    titleClassName: "text-orange-500",
  },
  {
    key: TAB_IDS.MEDICAL_TEAM,
    title: "ravid.ravidSettings.tabs.medical",
    content: <MedicalTeam />,
    titleClassName: "text-orange-500",
  },
  {
    key: TAB_IDS.INSURANCE,
    title: "ravid.ravidSettings.tabs.insurance",
    content: <Insurance />,
    titleClassName: "text-orange-500",
  },
  {
    key: TAB_IDS.QR_CODE,
    title: "ravid.ravidSettings.tabs.qrCode",
    content: <QRCode />,
    titleClassName: "text-orange-500",
  },
  {
    key: TAB_IDS.ACCESS_SECURITY,
    title: "ravid.ravidSettings.tabs.accessSecurity",
    content: <AccessSecurity />,
  },
  {
    key: TAB_IDS.ACCOUNT_SHARING,
    title: "ravid.ravidSettings.tabs.accountSharing",
    content: <AccountSharing />,
  },
  {
    key: TAB_IDS.BILLINGS,
    title: "ravid.ravidSettings.tabs.billings",
    content: <BillingsTab />,
  },
  {
    key: TAB_IDS.COMMUNICATIONS,
    title: "ravid.ravidSettings.tabs.communications",
    content: <Communications />,
  },
  {
    key: TAB_IDS.STORAGE,
    title: "ravid.ravidSettings.tabs.storage",
    content: <Storage />,
  },
  {
    key: TAB_IDS.VERIFICATION,
    title: "ravid.ravidSettings.tabs.verification",
    content: <Verification />,
  },
];

const PersonalDashboard = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState<TabId>(() => {
    const tabFromParams = searchParams.get("tab") as TabId | null;
    const defaultTab = TAB_IDS.PERSONAL;
    if (!tabFromParams) return defaultTab;
    return Object.values(TAB_IDS).includes(tabFromParams) ? tabFromParams : defaultTab;
  });

  const handleTabChange = (key: React.Key | null) => {
    if (!key) return;
    const tabKey = key.toString();
    setCurrentTab(tabKey as TabId);
    const tab = tabs.find((t) => t.key === tabKey);
    if (tab) {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tab.title);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <Tabs
        tabs={tabs}
        selectedKey={currentTab}
        onSelectionChange={handleTabChange}
        variant="underlined"
        color="primary"
        aria-label="Personal Dashboard Options"
        radius="lg"
      />
    </Card>
  );
};

export default PersonalDashboard;
