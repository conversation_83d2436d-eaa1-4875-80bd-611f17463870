import React from "react";
import { Control } from "react-hook-form";
import { FormInput } from "@/components/Forms/commons";
import { RoleMedicalTeamFormValues } from "@/lib/utils/validations";
import { Button } from "@heroui/button";
import { Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";
interface PractitionerFormProps {
  control: Control<RoleMedicalTeamFormValues>;
  namePrefix: string;
  role?: string;
  onDelete?: () => void;
  showDelete?: boolean;
}

export const PractitionerForm: React.FC<PractitionerFormProps> = ({ control, namePrefix, role, onDelete, showDelete = false }) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-3">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {role ? <h2>{role}</h2> : <FormInput size="sm" name={`${namePrefix}.role`} control={control} label="Additional Practitioner" className="w-full" />}
        {showDelete && onDelete && (
          <Button type="button" variant="ghost" size="sm" onPress={onDelete} className="text-red-500 hover:bg-red-50 ml-auto w-fit">
            <Trash2 size={16} />
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        <FormInput size="sm" name={`${namePrefix}.name`} control={control} label={t("ravid.ravidSettings.medical.name")} />
        <FormInput size="sm" name={`${namePrefix}.email`} control={control} type="email" label={t("ravid.ravidSettings.medical.email")} />
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        <FormInput size="sm" name={`${namePrefix}.contact_number`} control={control} type="tel" label={t("ravid.ravidSettings.medical.contact")} />
        <FormInput size="sm" name={`${namePrefix}.affiliation`} control={control} label={t("ravid.ravidSettings.medical.affiliation")} />
      </div>
    </div>
  );
};
