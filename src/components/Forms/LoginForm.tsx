import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card } from "@heroui/card";
import { useLogin } from "@/hooks/useUser";
import SubmitButton from "../SubmitButton";
import { LoginFormType, LoginFormValidation } from "@/lib/utils/validations";
import Link from "next/link";
import OAuthLogin from "./OauthComponent";
import { FormInput } from "./commons";
const LoginForm = () => {
  const { mutateAsync: loginUser } = useLogin();

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
  } = useForm<LoginFormType>({
    resolver: zodResolver(LoginFormValidation),
    mode: "onChange",
  });

  const onSubmit = async (data: LoginFormType) => {
    try {
      await loginUser(data);
    } catch (error) {
      console.error("Login error:", error);
    }
  };

  return (
    <Card className="p-6 dark:bg-[#000] border dark:border-gray-800 col-span-4 w-full max-w-md mx-auto">
      <h2 className="text-lg mb-3 text-left">Log In to R.A.V.I.D.</h2>

      <p className="text-xs text-gray-600 dark:text-gray-400 mb-6 text-left">
        Don&apos;t have an account?{" "}
        <Link className="text-blue-500 underline text-xs" href="/signup">
          Sign up here
        </Link>
      </p>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormInput name="email" size="sm" type="email" label="Email" control={control} />
        <FormInput name="password" size="sm" type="password" label="Password" control={control} />
        <Link className="text-xs text-primary" href="/forget">
          Forgot Password?
        </Link>

        <SubmitButton type="submit" color="primary" className="w-full" size="sm" isLoading={isSubmitting} isLoadingValue="Logging in..." label="Login" />
      </form>
      <div className="mt-4">
        <OAuthLogin type="login" />
      </div>
    </Card>
  );
};

export default LoginForm;