import { FormDatePicker, FormInput } from "@/components/Forms/commons";
import { useFormContext } from "react-hook-form";

export const EducationCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return (
    <>
      <FormInput name={`categories.${categoryIndex}.name`} control={control} label="Education Name" className="w-full text-xs hidden" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.institution`} control={control} label="Institution" className="w-full text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.degree`} control={control} label="Degree" className="w-full text-xs" />
      <FormDatePicker name={`categories.${categoryIndex}.contents.0.content.startDate`} control={control} label="Start Date" className="w-1/2 text-xs" />
      <FormDatePicker name={`categories.${categoryIndex}.contents.0.content.endDate`} control={control} label="End Date" className="w-1/2 text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.description`} control={control} label="Description" className="w-full text-xs" />
    </>
  );
};

export const PracticesCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return (
    <>
      <FormInput name={`categories.${categoryIndex}.contents.0.content.practiceName`} control={control} label="Practice Name" className="w-full text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.address`} control={control} label="Practice Address" className="w-full text-xs" />
      <FormInput
        name={`categories.${categoryIndex}.contents.0.content.website`}
        control={control}
        label="Website"
        // type="url"
        className="w-full text-xs"
      />
    </>
  );
};

export const ResearchCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return (
    <>
      <FormInput name={`categories.${categoryIndex}.contents.0.content.title`} control={control} label="Research Title" className="w-full text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.journal`} control={control} label="Journal Name" className="w-full text-xs" />
      <FormDatePicker
        name={`categories.${categoryIndex}.contents.0.content.publicationDate`}
        control={control}
        label="Publication Date"
        className="w-full text-xs"
      />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.url`} control={control} label="Research URL" className="w-full text-xs" />
    </>
  );
};

export const AwardsCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return (
    <>
      <FormInput name={`categories.${categoryIndex}.contents.0.content.title`} control={control} label="Award Title" className="w-full text-xs" />
      <FormDatePicker name={`categories.${categoryIndex}.contents.0.content.awardDate`} control={control} label="Award Date" className="w-full text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.description`} control={control} label="Award Description" className="w-full text-xs" />
    </>
  );
};

export const PodcastsCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return (
    <>
      <FormInput name={`categories.${categoryIndex}.contents.0.content.title`} control={control} label="Podcast Title" className="w-full text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.url`} control={control} label="Podcast URL" className="w-full text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.description`} control={control} label="Podcast Description" className="w-full text-xs" />
    </>
  );
};

export const AffiliationsCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return <FormInput name={`categories.${categoryIndex}.contents.0.content.description`} control={control} label="Affiliations" className="w-full" />;
};

export const PositionsCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return (
    <>
      <FormInput name={`categories.${categoryIndex}.contents.0.content.title`} control={control} label="Position Title" className="w-full text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.company`} control={control} label="Company Name" className="w-full text-xs" />
      <FormDatePicker name={`categories.${categoryIndex}.contents.0.content.startDate`} control={control} label="Start Date" className="w-1/2 text-xs" />
      <FormDatePicker name={`categories.${categoryIndex}.contents.0.content.endDate`} control={control} label="End Date" className="w-1/2 text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.summary`} control={control} label="Position Summary" className="w-full text-xs" />
    </>
  );
};

export const CoursesCategory = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  return (
    <>
      <FormInput name={`categories.${categoryIndex}.contents.0.content.courseTitle`} control={control} label="Course Title" className="w-full text-xs" />
      <FormDatePicker name={`categories.${categoryIndex}.contents.0.content.startDate`} control={control} label="Start Date" className="w-1/2 text-xs" />
      <FormDatePicker name={`categories.${categoryIndex}.contents.0.content.endDate`} control={control} label="End Date" className="w-1/2 text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.instructor`} control={control} label="Instructor(s)" className="w-1/2 text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.location`} control={control} label="Course Location" className="w-1/2 text-xs" />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.duration`} control={control} label="Course Duration" className="w-full text-xs" />
      <FormInput
        name={`categories.${categoryIndex}.contents.0.content.link`}
        control={control}
        label="Important Link"
        // type="url"
        className="w-full text-xs"
      />
      <FormInput name={`categories.${categoryIndex}.contents.0.content.information`} control={control} label="Course Information" className="w-full text-xs" />
    </>
  );
};
