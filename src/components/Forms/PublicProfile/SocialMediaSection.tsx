import { But<PERSON> } from "@heroui/react";
import { Facebook, Linkedin, Mail, SearchSlash } from "lucide-react";
import { FaXTwitter } from "react-icons/fa6";
import { useStore } from "@/store/store";

const SocialMediaSection = () => {
  const { user } = useStore();

  let profileUrl = `${process.env.NEXT_PUBLIC_APP_URL}/${user?.user_id}/user/${user?.username}`;
  const handleShareTwitter = () => {
    const twitterShareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(profileUrl)}&text=Check out my Ravid profile:`;
    window.open(twitterShareUrl, "_blank");
  };

  const handleShareLinkedin = () => {
    const linkedinShareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(profileUrl)}`;
    window.open(linkedinShareUrl, "_blank");
  };

  const handleShareFacebook = () => {
    const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(profileUrl)}`;
    window.open(facebookShareUrl, "_blank");
  };

  const handleShareEmail = () => {
    const emailShareUrl = `mailto:?subject=Check out my Ravid profile&body=${encodeURIComponent(`Check out my Ravid profile: ${profileUrl}`)}`;
    window.location.href = emailShareUrl;
  };

  return (
    <div className="flex gap-3 justify-center items-center md:justify-start flex-wrap">
      <Button isIconOnly size="sm" className="text-black bg-gray-200 rounded-full p-2" onClick={handleShareEmail}>
        <Mail className="h-4 w-4" />
      </Button>
      <Button isIconOnly size="sm" className="text-black bg-gray-200 rounded-full p-2" onClick={handleShareTwitter}>
        <FaXTwitter className="h-4 w-4" />
      </Button>
      <Button isIconOnly size="sm" className="text-black bg-gray-200 rounded-full p-2" onClick={handleShareLinkedin}>
        <Linkedin className="h-4 w-4" />
      </Button>
      <Button isIconOnly size="sm" className="text-black bg-gray-200 rounded-full p-2" onClick={handleShareFacebook}>
        <Facebook className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default SocialMediaSection;
