import { capitalize } from "@/lib/convertText";
import React from "react";
import { Control, UseFieldArrayPrepend, UseFieldArrayRemove, useWatch } from "react-hook-form";
import { AddButton } from ".";
interface CategoryTabsProps {
  fields: Record<"id", string>[];
  control: Control<any>;
  remove: UseFieldArrayRemove;
  prepend: UseFieldArrayPrepend<any>;
  onTabClick: (categoryId: string) => void;
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({ control, fields, remove, prepend, onTabClick }) => {
  const categoryTitles = useWatch({
    control,
    name: fields.map((_, index) => `categories.${index}.name`),
  });

  return (
    <div className="flex justify-between gap-2">
      <div className="flex flex-wrap gap-3">
        {fields.map((cat: any, index: number) => {
          return (
            <div
              key={cat.id}
              onClick={(e) => {
                e.stopPropagation();
                onTabClick(cat.id);
              }}
              className="w-auto min-w-[150px] h-10 px-4 relative group flex items-center bg-white dark:bg-gray-800 rounded-t-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 border border-b-1 border-b-gray-400 dark:border-gray-600"
            >
              <div className="flex items-center justify-between">
                <input
                  type="text"
                  className="bg-transparent border-none focus:outline-none w-full text-xs cursor-pointer"
                  placeholder="Tab name"
                  readOnly
                  value={capitalize(categoryTitles?.[index] || cat?.name)}
                />
                <button
                  className=" text-gray-500 hover:text-red-500 transition-opacity ml-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    remove(index);
                  }}
                >
                  ×
                </button>
              </div>
            </div>
          );
        })}
      </div>
      <div className="flex-shrink-0">
        <AddButton
          onPress={() =>
            prepend({
              contents: [{ content: { title: "", description: "" } }],
              name: `customize-${fields.length + 1}`,
              description: "custom",
            })
          }
        >
          Add a tab
        </AddButton>
      </div>
    </div>
  );
};

export default CategoryTabs;
