import { EditAvatarIcon } from "@/components/icons";
import CustomModal from "@/components/Modals/CustomModal";
import { useGetUser, useUpdateUser } from "@/hooks/useUser";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, Card, CardBody } from "@heroui/react";
import { BadgeCheck, Check, Copy } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { FormInput, FormTextArea } from "../commons";
import BusinessCardSection from "./BusinessCardSection";
import ProfileImageUpload from "./ProfileImageUpload";
import ProfilePreviewModal from "./ProfilePreviewModal";
import SocialMediaSection from "./SocialMediaSection";

interface ProfileCardProps {
  handleCategorySubmit: () => void;
}

const ProfileCard = ({ handleCategorySubmit }: ProfileCardProps) => {
  // Hooks
  const { t } = useTranslation();
  const router = useRouter();
  const { data: user } = useGetUser();
  const { control: mainFormControl, setValue, handleSubmit } = useFormContext();
  const { mutate: updateProfile, isPending: isLoading } = useUpdateUser();

  // State
  const [copy, setCopy] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [isAdvFreeActive, setIsAdvFreeActive] = useState(false);

  // Memoized Values
  const displayUrl = useMemo(
    () => `${process.env.NEXT_PUBLIC_APP_URL}/${user?.user_id}/user/${user?.username ? user.username : user?.first_name}`,
    [user?.user_id, user?.username, user?.first_name]
  );

  // Form Handlers
  const onSubmit = async (data: any) => {
    const { profile_picture, ...dataToUpdate } = data;
    await updateProfile(dataToUpdate);
  };

  // Profile Actions
  const handlePublish = async () => {
    try {
      await updateProfile({ is_public_profile: true });
      setShowPreviewModal(false);
    } catch (error) {
      toast.error("Failed to publish profile");
    }
  };

  const unpublishProfile = async (value: boolean) => {
    try {
      await updateProfile({ is_public_profile: value });
      toast.success("Profile unpublished successfully");
    } catch (error) {
      toast.error("Failed to unpublish profile");
    }
  };

  const copyLink = () => {
    navigator.clipboard.writeText(displayUrl);
    setCopy(true);
    setTimeout(() => setCopy(false), 3000);
  };

  // Verification Modal Handlers
  const handleContinueVerification = () => {
    router.push(`/my/${user?.user_id}/edit?tab=verification`);
    setShowVerification(false);
  };

  const handleCancelVerification = () => {
    setShowVerification(false);
  };

  // Payment Modal Handlers
  const handleContinueToPayment = () => {
    localStorage.setItem("activeTab", "paymentInfo");
    setTimeout(() => {
      localStorage.removeItem("activeTab");
    }, 5000);
    router.push(`/my/edit/${user?.user_id}`);
    setShowPaymentModal(false);
    setIsAdvFreeActive(true);
  };

  const handleCancelPaymentModal = () => {
    setShowPaymentModal(false);
    setIsAdvFreeActive(false);
  };

  // UI Components
  const renderProfileActionButtons = (isMobile = false) => (
    <>
      <Button color="primary" size="sm" type="submit" onPress={handleCategorySubmit}>
        {t("ravid.buttons.saveChanges")}
      </Button>

      {!user?.is_public_profile ? (
        <Button
          className={cn("bg-[#531143] text-white hover:bg-[#63134b]/80", isMobile ? "col-span-2" : "w-full sm:w-auto")}
          onPress={() => setShowPreviewModal(true)}
          size="sm"
        >
          {t("ravid.publicProfile.publishProfile")}
        </Button>
      ) : (
        <>
          <Link href={`/${user?.user_id}/user/${user?.username ? user.username : user?.first_name || ""}`} target="_blank">
            <Button className="bg-[#531143] w-full text-white hover:bg-[#63134b]/80" size="sm">
              {t("ravid.publicProfile.liveVersion")}
            </Button>
          </Link>
          <Button onPress={() => unpublishProfile(false)} className="bg-red-500 text-xs text-white hover:bg-red-600" size="sm">
            {t("ravid.publicProfile.unPublish")}
          </Button>
        </>
      )}
    </>
  );

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card className="dark:bg-[#060C1B]">
          <CardBody className="p-4">
            <div className="flex flex-col md:flex-row justify-between items-start">
              <div className="w-full md:w-1/3 border-none md:border-r md:border-gray-700 pr-0 md:pr-4 mb-4 md:mb-0">
                <div className="relative flex justify-center mb-6">
                  <ProfileImageUpload value="profile_picture" currentImage={user?.profile_picture || null} className="rounded-full w-32 h-32" />
                  <div className="absolute inset-0 bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <EditAvatarIcon />
                    <input
                      id="profile-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          setValue("profile_picture", file);
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="mb-6">
                  <span className="text-sm flex items-center gap-2 mt-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-transparent bg-clip-text font-medium animate-pulse p-2 justify-center">
                    {t("ravid.publicProfile.createYourOwnPersonalURL")}
                  </span>
                  <div className="flex justify-between items-start mb-2">
                    <FormInput
                      name="username"
                      control={mainFormControl}
                      placeholder={t("ravid.publicProfile.publicDomainName")}
                      className="w-full text-xs"
                      inputProps={{
                        inputWrapper: "rounded-l-md rounded-r-none border border-slate-800",
                      }}
                    />
                    <Button type="submit" size="sm" className="block h-10 bg-blue-700 text-white hover:bg-blue-800 rounded-l-none px-3">
                      {t("ravid.buttons.save")}
                    </Button>
                  </div>
                  <div className="flex gap-1 items-center justify-center md:justify-start">
                    <Link
                      href={`/${user?.user_id}/user/${user?.username ? user.username : user?.first_name}`}
                      target="_blank"
                      className="text-xs text-blue-400 hover:text-blue-300 transition-colors truncate max-w-[200px]"
                    >
                      {displayUrl}
                    </Link>
                    {!copy ? (
                      <Copy onClick={copyLink} className="h-4 w-4 text-gray-500 cursor-pointer transition-all duration-300 hover:scale-110 flex-shrink-0" />
                    ) : (
                      <Check className="h-4 w-4 text-green-400 animate-in zoom-in duration-300 flex-shrink-0" />
                    )}
                  </div>
                </div>

                <p className="text-xs text-gray-400 md:block hidden mb-4">{t("ravid.publicProfile.startAPostOn")}</p>
                <SocialMediaSection />
              </div>
              <div className="w-full md:w-2/3 pl-0 md:pl-4">
                <div className="flex flex-col sm:flex-row md:justify-between gap-2 md:gap-unset items-center mb-6">
                  {/* Left side - Get Verified Profile (desktop) */}
                  <div className="hidden sm:block">
                    <Button
                      variant="bordered"
                      size="sm"
                      onPress={() => setShowVerification(true)}
                      className="rounded-lg px-3 py-1 text-xs text-white bg-[#531143] hover:bg-[#63134b]/80 border-none"
                    >
                      <BadgeCheck className="mr-1 h-4 w-4" />
                      {t("ravid.publicProfile.getVerifiedProfile")}
                    </Button>
                  </div>

                  {/* Mobile grid layout */}
                  <div className="w-full grid grid-cols-2 gap-2 sm:hidden">
                    <Button
                      variant="bordered"
                      size="sm"
                      onPress={() => setShowVerification(true)}
                      className="rounded-lg px-3 py-1 text-xs text-white bg-[#531143] hover:bg-[#63134b]/80 border-none"
                    >
                      <BadgeCheck className="mr-1 h-4 w-4" />
                      {t("ravid.publicProfile.verify")}
                    </Button>
                    {renderProfileActionButtons(true)}
                  </div>

                  {/* Right side buttons (desktop) */}
                  <div className="hidden sm:flex gap-2">{renderProfileActionButtons()}</div>
                </div>
                <div className="space-y-4">
                  <div className="flex flex-col md:flex-row gap-3">
                    <div className="flex-1">
                      <FormInput name={`first_name`} control={mainFormControl} required placeholder={t("ravid.publicProfile.firstName")} />
                    </div>
                    <div className="flex-1">
                      <FormInput name={`middle_name`} control={mainFormControl} placeholder={t("ravid.publicProfile.middleName")} />
                    </div>
                    <div className="flex-1">
                      <FormInput name={`last_name`} control={mainFormControl} placeholder={t("ravid.publicProfile.lastName")} />
                    </div>
                  </div>
                  <div className="flex justify-between items-start">
                    <FormInput
                      className="text-xs"
                      name={`professional_title`}
                      control={mainFormControl}
                      placeholder={t("ravid.publicProfile.professionalNameAndTitle")}
                      inputProps={{
                        inputWrapper: "rounded-l-md rounded-r-none border border-orange-300",
                      }}
                    />
                    <Button
                      disabled={isLoading}
                      type="submit"
                      size="sm"
                      className="h-10 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 rounded-l-none px-3"
                    >
                      {isLoading ? t("ravid.buttons.saving") : t("ravid.buttons.save")}
                    </Button>
                  </div>
                  <div className="flex flex-col md:flex-row gap-2">
                    <FormInput name={`speciality`} control={mainFormControl} placeholder={t("ravid.publicProfile.speciality")} className="text-xs" />
                    <FormInput name={`locations`} control={mainFormControl} placeholder={t("ravid.publicProfile.location")} className="text-xs" />
                  </div>
                  <FormTextArea name={`bio`} control={mainFormControl} placeholder={t("ravid.publicProfile.summary")} className="text-xs" />
                </div>

                <div className="mt-3 flex flex-col lg:grid lg:grid-cols-12 gap-4">
                  <BusinessCardSection />
                  <div className="w-full lg:col-span-8">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 lg:gap-6 dark:bg-transparent border rounded-xl dark:border-slate-900 p-3 sm:p-5">
                      <Link className="as-button w-full" prefetch={true} href={`/my/${user?.user_id}/appointments?tab=appointmentSchedule`}>
                        {t("ravid.publicProfile.appointments")}
                      </Link>
                      <button className="as-button w-full bg-gray-800 text-gray-400" disabled>
                        {t("ravid.publicProfile.donate")}
                      </button>
                      <Link className="as-button w-full" prefetch={true} href={`/my/${user?.user_id}/message`}>
                        {t("ravid.publicProfile.messages")}
                      </Link>
                      <Link className="as-button w-full" prefetch={true} href={`/my/${user?.user_id}/notifications`}>
                        {t("ravid.publicProfile.notifications")}
                      </Link>
                      <Link className="as-button w-full" prefetch={true} href={`/my/${user?.user_id}/payments`}>
                        {t("ravid.publicProfile.payments")}
                      </Link>
                      <button className="as-button w-full bg-gray-800 text-gray-400" disabled>
                        {t("ravid.publicProfile.telemedicine")}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </form>
      <CustomModal
        title="Why Verify?"
        body={
          <p className="text-sm">
            Verification builds trust that 'you' are who 'you' are & 'your' credentials are authentic & submitted to us for our records, from a trusted source,
            'you'.
          </p>
        }
        primaryButtonText="Verify Now."
        secondaryButtonText="Not now, later."
        onPrimaryAction={handleContinueVerification}
        onSecondaryAction={handleCancelVerification}
        isOpen={showVerification}
        onOpenChange={setShowVerification}
        primaryButtonColor="bg-[#63134b] hover:bg-[#63134b]/80"
      />
      <CustomModal
        title="Confirm Action"
        body={<p className="text-sm">Would you like to activate "Adv Free" mode? You'll be redirected to complete payment.</p>}
        primaryButtonText="Proceed"
        secondaryButtonText="Cancel"
        onPrimaryAction={handleContinueToPayment}
        onSecondaryAction={handleCancelPaymentModal}
        isOpen={showPaymentModal}
        onOpenChange={setShowPaymentModal}
      />
      <ProfilePreviewModal open={showPreviewModal} onOpenChange={setShowPreviewModal} onPublish={handlePublish} user={user} />
    </>
  );
};

export default ProfileCard;
