import DefaultAvatar from "@/components/DefaultAvatar";
import { useUpdateProfileImages } from "@/hooks/useUser";
import { Pencil } from "lucide-react";
import Image from "next/image";
import React from "react";
import { toast } from "react-hot-toast";

interface ProfileImageUploadProps {
  currentImage: string | null;
  className?: string;
  onImageSelect?: (file: File) => void;
  value: string;
}

const ProfileImageUpload: React.FC<ProfileImageUploadProps> = ({ currentImage, className, onImageSelect, value }) => {
  const { mutate: updateProfileImages } = useUpdateProfileImages();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "image/jpeg" && file.type !== "image/jpg" && file.type !== "image/png") {
        toast.error("Please select a JPEG, JPG or PNG image.");
        return;
      }

      const formData = new FormData();
      formData.append(value, file);

      try {
        await updateProfileImages(formData);
        toast.success("Profile image uploaded successfully!");

        if (onImageSelect) {
          onImageSelect(file);
        }
      } catch (error) {
        console.error("Error uploading profile image:", error);
        toast.error("An error occurred while uploading the profile image");
      }
    }
  };

  return (
    <div className={`relative group ${className}`}>
      {/* Main Image */}
      {currentImage ? <Image src={currentImage} alt="Profile" fill className="object-cover rounded-full" /> : <DefaultAvatar />}

      {/* Upload Button Overlay */}
      <div className="absolute bottom-0 right-0">
        <label
          className="flex items-center justify-center w-8 h-8 bg-blue-600 hover:bg-blue-700 
          rounded-full cursor-pointer transition-all duration-200 shadow-lg"
        >
          <Pencil className="w-4 h-4 text-white" />
          <input type="file" onChange={handleFileSelect} accept=".jpg,.jpeg,.png" className="hidden" />
        </label>
      </div>
    </div>
  );
};

export default ProfileImageUpload;
