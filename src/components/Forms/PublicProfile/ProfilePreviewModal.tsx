"use client";

import { useProfileCategory } from "@/hooks/public-profile/useProfileCategory";
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, <PERSON><PERSON>r, Link, Modal, ModalBody, <PERSON>dal<PERSON>ontent, <PERSON><PERSON>Head<PERSON>, <PERSON>b, Tabs } from "@heroui/react";
import { Link2, Map<PERSON><PERSON>, Stethoscope } from "lucide-react";
import { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import ProfileImageUpload from "./ProfileImageUpload";
import SocialMediaSection from "./SocialMediaSection";

interface ProfilePreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPublish: () => void;
  user?: User | null;
}

const ProfilePreviewDialog: React.FC<ProfilePreviewDialogProps> = ({ open, onOpenChange, onPublish, user }) => {
  const { t } = useTranslation();
  const { data: categories } = useProfileCategory() as { data: any[] };
  const contentRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  //   const { publicProfile } = useAuthStore();

  const hasCategoryContent = (category: any) => {
    return (
      category.contents &&
      category.contents.length > 0 &&
      category.contents.some((content: any) => {
        const contentData = (content as any).content || {};
        return Object.values(contentData).some((value) => value !== null && value !== undefined && value !== "");
      })
    );
  };

  const activeCategories = categories?.filter((category: any) => !category.hidden && category.name && hasCategoryContent(category));

  const renderCategoryContent = (category: any) => {
    return category.contents?.map((content: any, index: number) => {
      const contentData = (content as any).content || {};

      switch (category.name?.toUpperCase()) {
        case "EDUCATION":
          return (
            <div key={index} className="p-6 rounded-lg border hover:border-border transition-all duration-300">
              {contentData.institution && <h3 className="text-sm font-semibold mb-4">{contentData.institution}</h3>}
              <div className="space-y-3">
                {contentData.degree && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Degree: {contentData.degree}
                  </p>
                )}
                {contentData.field && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Field: {contentData.field}
                  </p>
                )}
                {contentData.startDate && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Duration: {contentData.startDate} - {contentData.endDate || "Present"}
                  </p>
                )}
                {contentData.description && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    {contentData.description}
                  </p>
                )}
              </div>
            </div>
          );

        case "PRACTICES":
          return (
            <div key={index} className="p-6 rounded-lg border hover:border-border transition-all duration-300">
              {contentData.practiceName && <h3 className="text-sm font-semibold mb-4">{contentData.practiceName}</h3>}
              <div className="space-y-3">
                {contentData.role && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Role: {contentData.role}
                  </p>
                )}
                {contentData.address && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Address: {contentData.address}
                  </p>
                )}
                {contentData.phone && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Phone: {contentData.phone}
                  </p>
                )}
                {contentData.website && (
                  <a
                    href={contentData.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 text-xs text-blue-500 hover:text-blue-500/80"
                  >
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Visit Website
                  </a>
                )}
              </div>
            </div>
          );

        case "RESEARCH PAPERS":
          return (
            <div key={index} className="p-6 rounded-lg border hover:border-border transition-all duration-300">
              {contentData.title && <h3 className="text-sm font-semibold mb-4">{contentData.title}</h3>}
              <div className="space-y-3">
                {contentData.description && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    {contentData.description}
                  </p>
                )}
                {contentData.authors && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Authors: <AUTHORS>
                  </p>
                )}
                {contentData.journal && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Journal: {contentData.journal}
                  </p>
                )}
                {contentData.publicationYear && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Year: {contentData.publicationYear}
                  </p>
                )}
                {contentData.doi && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    DOI: {contentData.doi}
                  </p>
                )}
                {contentData.url && (
                  <a
                    href={contentData.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 text-xs text-blue-500 hover:text-blue-500/80"
                  >
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    View Research Paper
                  </a>
                )}
              </div>
            </div>
          );

        case "AWARDS":
          return (
            <div key={index} className="p-6 rounded-lg border hover:border-border transition-all duration-300">
              {contentData.title && <h3 className="text-sm font-semibold mb-4">{contentData.title}</h3>}
              <div className="space-y-3">
                {contentData.organization && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Organization: {contentData.organization}
                  </p>
                )}
                {contentData.awardDate && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Award Date: {contentData.awardDate}
                  </p>
                )}
                {contentData.description && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    {contentData.description}
                  </p>
                )}
              </div>
            </div>
          );

        case "PODCASTS":
          return (
            <div key={index} className="p-6 rounded-lg border hover:border-border transition-all duration-300">
              {contentData.title && <h3 className="text-sm font-semibold mb-4">{contentData.title}</h3>}
              <div className="space-y-3">
                {contentData.platform && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Platform: {contentData.platform}
                  </p>
                )}
                {contentData.description && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    {contentData.description}
                  </p>
                )}
                {contentData.url && (
                  <a
                    href={contentData.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 text-xs hover:text-primary/80 text-blue-500"
                  >
                    {/* <span className="h-2 w-2 rounded-full underline text-blue-500 bg-primary"></span> */}
                    <link className="w-3 h-3" /> Listen to Podcast
                  </a>
                )}
              </div>
            </div>
          );

        case "AFFILIATIONS":
          return (
            <div key={index} className="p-6 rounded-lg border hover:border-border transition-all duration-300">
              {contentData.organization && <h3 className="text-sm font-semibold mb-4">{contentData.organization}</h3>}
              <div className="space-y-3">
                {contentData.role && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    Role: {contentData.role}
                  </p>
                )}
                {contentData.description && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    {contentData.description}
                  </p>
                )}
              </div>
            </div>
          );

        default:
          return (
            <div key={index} className="p-6 rounded-lg border hover:border-border transition-all duration-300">
              {contentData.title && <h3 className="text-sm font-semibold mb-4">{contentData.title}</h3>}
              <div className="space-y-3">
                {contentData.description && (
                  <p className="flex items-center gap-3 text-xs">
                    <span className="h-2 w-2 rounded-full bg-primary"></span>
                    {contentData.description}
                  </p>
                )}
              </div>
            </div>
          );
      }
    });
  };

  // Scroll handler for tab clicks
  const handleTabClick = (value: string) => {
    contentRefs.current[value]?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  };

  // Update active tab based on scroll position
  useEffect(() => {
    if (!open) return;

    const handleScroll = () => {
      const scrollPosition = window.scrollY + 200; // Offset for header

      for (const category of activeCategories) {
        const element = contentRefs.current[category.name?.toLowerCase() || ""];
        if (element) {
          const { top } = element.getBoundingClientRect();
          if (top <= 200) {
            const tabsList = document.querySelector('[role="tablist"]');
            const tab = document.querySelector(`[data-state="active"]`);
            if (tab && tabsList) {
              tab.setAttribute("data-state", "inactive");
            }
            const newTab = document.querySelector(`[value="${category.name?.toLowerCase()}"]`);
            if (newTab) {
              newTab.setAttribute("data-state", "active");
            }
          }
        }
      }
    };

    document.addEventListener("scroll", handleScroll);
    return () => document.removeEventListener("scroll", handleScroll);
  }, [open, activeCategories]);

  return (
    <Modal isOpen={open} onOpenChange={onOpenChange} className="dark:bg-slate-950">
      <ModalContent className=" max-w-6xl h-[90vh] overflow-y-auto">
        <ModalHeader className="mt-4 flex flex-row justify-between items-center bg-background z-50 ">
          <h1 className="text-sm font-semibold">Profile Preview</h1>
          <div className="flex gap-2">
            <Button variant="bordered" size="sm" onPress={() => onOpenChange(false)} className="px-4">
              Edit
            </Button>
            <Button variant="flat" onPress={onPublish} size="sm" className="px-4 bg-[#531143] hover:bg-[#531143]/80 text-white">
              Publish
            </Button>
          </div>
        </ModalHeader>

        <ModalBody className="space-y-8 py-4">
          <Card className="shadow-lg rounded-2xl dark:bg-slate-900">
            <CardBody className="p-6">
              <div className="flex flex-col md:flex-row gap-8 md:items-start items-center">
                <div className="flex-shrink-0">
                  <div className="relative">
                    <ProfileImageUpload
                      value=""
                      currentImage={user?.profile_picture || null}
                      className="rounded-full w-48 h-48 object-cover border-4 border-background shadow-lg"
                    />
                  </div>
                </div>

                <div className="flex-grow space-y-6">
                  <div className="md:space-y-4 space-y-2">
                    <div className="flex flex-col">
                      <h1 className="text-sm font-bold">
                        {user?.title ? (
                          user.title
                        ) : (
                          <>
                            {user?.first_name} {user?.last_name}
                          </>
                        )}
                      </h1>
                      <p className="text-xs text-muted-foreground">@{user?.username}</p>
                    </div>
                    <div className="flex flex-col sm:flex-row md:gap-4 gap-2">
                      <div className="flex-1 flex items-center gap-2">
                        <Stethoscope className="w-4 h-4" />
                        <span className="text-xs">{user?.speciality}</span>
                      </div>
                      <div className="flex-1 flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        <span className="text-xs">{user?.locations}</span>
                      </div>
                      <div className="flex-1 flex items-center gap-2">
                        <Link2 className="w-4 h-4" />
                        <Link href={`/my/user/${user?.user_id}/user/${user?.username || null}`} className="text-xs" target="_blank">
                          test.ravid.cloud/my/${user?.user_id}/user/${user?.username || null}
                        </Link>
                      </div>
                    </div>
                  </div>

                  {user?.bio && (
                    <div className="space-y-2">
                      <p className="text-xs leading-relaxed">{user.bio}</p>
                    </div>
                  )}

                  <div className="flex flex-col gap-4">
                    <div className="grid grid-cols-2 md:flex md:flex-wrap gap-3">
                      <Button variant="bordered" className="rounded-md text-xs w-full md:w-32">
                        {t("ravid.publicProfile.appointment")}
                      </Button>
                      <Button variant="bordered" className="rounded-md text-xs w-full md:w-32">
                        {t("ravid.publicProfile.message")}
                      </Button>
                      <Button variant="bordered" className="rounded-md text-xs w-full md:w-32">
                        {t("ravid.publicProfile.payments")}
                      </Button>
                      <Button variant="bordered" className="rounded-md text-xs w-full md:w-32">
                        {t("ravid.publicProfile.telemedicine")}
                      </Button>
                      <SocialMediaSection />
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {activeCategories.length > 0 && (
            <>
              <div className="bg-background border-b px-4">
                <Tabs
                  defaultSelectedKey={activeCategories[0]?.name?.toLowerCase()}
                  items={activeCategories}
                  // color="primary"
                  classNames={{
                    cursor: "bg-blue-500",
                    tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
                    tabContent: "text-gray-700 dark:text-white wrap",
                    tabList: "flex flex-wrap md:flex-nowrap ",
                  }}
                >
                  {/* <TabsList className="w-full justify-start overflow-x-auto">
                    {activeCategories.map((category) => (
                      <TabsTrigger
                        key={category.name}
                        value={category.name?.toLowerCase() || ""}
                        className="text-sm p-2"
                        onClick={() => handleTabClick(category.name?.toLowerCase() || "")}
                      >
                        {category.name}
                      </TabsTrigger>
                    ))}
                  </TabsList> */}
                  {(category) => (
                    <Tab
                      key={category.name}
                      onClick={() => handleTabClick(category.name?.toLowerCase() || "")}
                      title={<span className={`${category.color} group-data-[selected=true]:!text-blue-500`}>{category.name}</span>}
                    >
                      {category.content}
                    </Tab>
                  )}
                </Tabs>
              </div>
              <div className="space-y-8">
                {activeCategories.map((category) => (
                  <div
                    key={category.name}
                    ref={(el) => {
                      if (el) {
                        contentRefs.current[category.name?.toLowerCase() || ""] = el;
                      }
                    }}
                    id={category.name?.toLowerCase()}
                  >
                    <Card className="shadow-md rounded-xl hover:shadow-lg transition-all duration-300 dark:bg-slate-900">
                      <CardHeader className="p-6">
                        <h1 className="text-sm font-semibold">{category.name}</h1>
                      </CardHeader>
                      <Divider />
                      <CardBody className="flex flex-col gap-4">{renderCategoryContent(category)}</CardBody>
                    </Card>
                  </div>
                ))}
              </div>
            </>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default ProfilePreviewDialog;
