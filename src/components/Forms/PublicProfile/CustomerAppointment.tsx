"use client";
import CustomModal from "@/components/Modals/CustomModal";
import { useAppointmentAvailability, useFetchAvailableDays } from "@/hooks/use-appointments";
import { useUploadDocument } from "@/hooks/use-upload";
import { But<PERSON> } from "@heroui/button";
import { Calendar, DateValue } from "@heroui/calendar";
import { Input, Textarea } from "@heroui/input";
import { Checkbox, Radio, RadioGroup } from "@heroui/react";
import { Upload } from "lucide-react";
import { usePathname } from "next/navigation";
import React, { useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";

interface CustomerAppointmentProps {
  publicProfile: User;
  user: any;
}

// Interface for time slots matching what we get from the API
interface TimeSlot {
  start_time: string;
  end_time: string;
  availability_id: string;
  title: string;
  clinic_id: string | null;
  enterprise_id: string | null;
  recurrence_type: string;
  status: string;
  booking_info: any;
  mode?: string;
}

interface ScheduleDay {
  date: string;
  day_of_week: string;
  slots: TimeSlot[];
  total_slots: number;
  available_slots: number;
  booked_slots: number;
}

interface AvailableSlotResponse {
  doctor_id: string;
  start_date: string;
  end_date: string;
  timezone: string;
  total_days: number;
  total_slots: number;
  total_available_slots: number;
  total_booked_slots: number;
  schedule: ScheduleDay[];
}

const CustomerAppointment = ({ publicProfile, user }: CustomerAppointmentProps) => {
  const pathname = usePathname();
  // Extract doctor ID from pathname
  const doctorId = pathname?.split("/")?.[1] || "";

  console.log("Public profile:", publicProfile);
  const { t } = useTranslation();
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [shareRavidData, setShareRavidData] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [appointmentMode, setAppointmentMode] = useState<string>("in_person");
  const [availableModes, setAvailableModes] = useState<string[]>([""]);

  const { createAppointment, isCreatingAppointment, fetchSlots } = useAppointmentAvailability();
  const { mutateAsync: uploadDocument } = useUploadDocument();

  // Format date for API call (YYYY-MM-DD)
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };
  // Query for available slots when a date is selected
  const formattedDate = selectedDate ? formatDate(selectedDate) : "";
  console.log("Querying slots with:", { doctorId, date: formattedDate });

  const { data: availableSlotsResponse, isLoading: isLoadingSlots, error: slotsError, refetch: refetchSlots } = fetchSlots(doctorId, formattedDate) as {
    data: AvailableSlotResponse | undefined;
    isLoading: boolean;
    error: any;
    refetch: () => void;
  };
  // fetch available days within 2 months
  const startDate = new Date();
  const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 2, startDate.getDate());
  const startDateString = startDate.toISOString().split("T")[0];
  const endDateString = endDate.toISOString().split("T")[0];
  const { data: availableDays } = useFetchAvailableDays(doctorId, startDateString, endDateString) as {
    data: AvailableSlotResponse | undefined;
  };
  const handleShowAvailableDays = (date: DateValue) => {
    // Transform schedule dates to objects with day, month, and year properties
    const availableDates =
      availableDays?.schedule?.map((day: ScheduleDay) => {
        const [yearStr, monthStr, dayStr] = day.date.split("-");
        return {
          day: parseInt(dayStr),
          month: parseInt(monthStr),
          year: parseInt(yearStr),
        };
      }) || [];
    // Check if the date is in available dates and not booked
    const isAvailable = availableDates.some(
      (d: { day: number; month: number; year: number }) => d.day === date.day && d.month === date.month && d.year === date.year
    );
    // If the date is not in available dates, it's unavailable
    return !isAvailable;
  };
  // Generate time slots from available slots
  const timeSlots = useMemo(() => {
    console.log("Processing slots response:", availableSlotsResponse);
    if (!availableSlotsResponse?.schedule?.[0]?.slots) return [];

    // Check for available modes in the slots
    if (availableSlotsResponse?.schedule?.[0]?.slots.length > 0) {
      const modes = new Set<string>();
      availableSlotsResponse.schedule[0].slots.forEach((slot) => {
        const mode = slot.mode || slot.booking_info?.mode;
        if (mode) {
          modes.add(mode);
        }
      });

      // Convert Set to array and update available modes
      const availableModesList = Array.from(modes);
      setAvailableModes(availableModesList);

      // If the current selected mode is not available, select the first available mode
      if (availableModesList.length > 0 && !availableModesList.includes(appointmentMode)) {
        setAppointmentMode(availableModesList[0]);
      }
    }

    return availableSlotsResponse.schedule[0].slots;
  }, [availableSlotsResponse, appointmentMode]);

  const { control, handleSubmit, reset, setValue, watch } = useForm({
    defaultValues: {
      title: user?.first_name + " " + user?.last_name || "",
      email: user?.email || "",
      notes: "",
    },
  });

  const handleAppointmentClick = () => {
    if (!user) {
      toast.error("Please login to book an appointment");
      return;
    }
    setShowAppointmentDialog(true);
    reset({
      title: user?.first_name + " " + user?.last_name || "",
      email: user?.email || "",
      notes: "",
    });
    // Don't set a default mode here, let the API response determine it
    setAvailableModes([]); // Reset available modes to empty array
  };

  // Format time for display
  const formatTimeDisplay = (timeStr: string) => {
    // Convert from 24-hour format (HH:MM) to 12-hour format with AM/PM
    const [hours, minutes] = timeStr.split(":").map(Number);
    const period = hours >= 12 ? "PM" : "AM";
    const hour12 = hours % 12 || 12;
    return `${hour12}:${minutes.toString().padStart(2, "0")} ${period}`;
  };

  // Calendar date change handler
  const handleDateChange = (date: any) => {
    const jsDate = new Date(date.year, date.month - 1, date.day);
    console.log("Date selected:", jsDate);
    setSelectedDate(jsDate);
    setSelectedSlot(null);
    // Force refetch slots for the new date
    const newFormattedDate = formatDate(jsDate);
    console.log("Fetching slots for date:", newFormattedDate);
    refetchSlots();
  };

  // Add file validation function
  const validateFile = (file: File) => {
    const validTypes = ["image/png", "image/jpeg", "application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"];
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes

    if (!validTypes.includes(file.type)) {
      toast.error("Invalid file type. Please upload PNG, JPEG, PDF, or DOCX");
      return false;
    }

    if (file.size > maxSize) {
      toast.error("File size exceeds 10MB limit");
      return false;
    }

    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    // Convert FileList to array for easier handling
    const newFiles = Array.from(files);

    // Check if adding new files would exceed the limit
    if (selectedFiles.length + newFiles.length > 3) {
      toast.error("Maximum 3 files allowed");
      return;
    }

    // Validate each file
    const validFiles = newFiles.filter((file) => validateFile(file));

    // Update selected files
    setSelectedFiles((prev) => [...prev, ...validFiles]);

    // Reset input value to allow selecting the same file again
    event.target.value = "";
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: any) => {
    if (!selectedDate) {
      toast.error("Please select a date");
      return;
    }

    if (!selectedSlot) {
      toast.error("Please select a time slot");
      return;
    }

    try {
      setIsUploading(true);
      // Upload files first if any exist
      const fileIds: string[] = [];
      if (selectedFiles.length > 0) {
        for (const file of selectedFiles) {
          const formData = new FormData();
          formData.append("file", file);
          formData.append("type", "diagnosis");
          const response = await uploadDocument({ data: formData });
          console.log("Upload response:", response);
          // Handle array response and extract file_data.id
          if (Array.isArray(response) && response[0]?.file_data?.id) {
            fileIds.push(response[0].file_data.id);
          }
        }
      }

      // Format the date as YYYY-MM-DD
      const dateStr = formatDate(selectedDate);

      // Create appointment data using the exact time strings from the slot
      const appointmentData = {
        email: user?.email || data.email,
        appointment_type: "booking",
        doctor_id: doctorId || "",
        start_time: `${dateStr}T${selectedSlot.start_time}`,
        end_time: `${dateStr}T${selectedSlot.end_time}`,
        availability_id: selectedSlot.availability_id,
        title: data.title,
        notes: data.notes || "",
        location: "Clinic Room tung 2",
        mode: appointmentMode,
        is_all_day: false,
        file_ids: fileIds,
      };

      await createAppointment(appointmentData);
      setShowAppointmentDialog(false);
      reset();
      setSelectedDate(null);
      setSelectedSlot(null);
      setShareRavidData(false);
      setSelectedFiles([]); // Reset files
    } catch (error) {
      console.error("Error during appointment creation:", error);
      toast.error("Failed to create appointment. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const modalBody = (
    <div className="w-full max-w-[800px]">
      <form id="appointment-form" onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col md:flex-row gap-2">
          {/* Left side - Calendar */}
          <div className="flex-1 space-y-4">
            <div>
              <RadioGroup
                classNames={{
                  label: "text-xs text-white",
                }}
                label="Choose the type of appointment"
                orientation="horizontal"
                value={appointmentMode}
                onValueChange={setAppointmentMode}
              >
                <Radio size="sm" classNames={{ label: "text-xs" }} value="video_call" isDisabled={!availableModes.includes("video_call")}>
                  Video Call (Telemedicine)
                </Radio>
                <Radio size="sm" classNames={{ label: "text-xs" }} value="in_person" isDisabled={!availableModes.includes("in_person")}>
                  In-Person Visit
                </Radio>
              </RadioGroup>
            </div>
            <h3 className="text-xs font-medium mb-3">Select a Date</h3>
            <div className="dark:bg-gray-950 rounded-lg overflow-hidden flex flex-col justify-center">
              <Calendar
                classNames={{
                  cellButton: "text-xs h-9 w-9",
                  gridHeaderCell: "text-xs",
                  title: "text-xs font-medium",
                  headerWrapper: "dark:bg-slate-950",
                  gridHeaderRow: "dark:bg-slate-950",
                }}
                className="border dark:border-gray-800 w-full rounded-lg border-gray-300"
                aria-label="Select appointment date"
                visibleMonths={1}
                onChange={handleDateChange}
                isDateUnavailable={handleShowAvailableDays}
              />
              <div className="flex flex-col gap-2 mt-3">
                <h3 className="text-xs font-medium mb-3">Select a Slot</h3>
                <div className="grid grid-cols-2 gap-2 mb-6 max-h-60 overflow-y-auto">
                  {isLoadingSlots ? (
                    <div className="col-span-3 text-xs text-center">Loading available slots...</div>
                  ) : slotsError ? (
                    <div className="col-span-3 text-xs text-red-500 text-center">Failed to load slots</div>
                  ) : !selectedDate ? (
                    <div className="col-span-3 text-center text-xs">Please select a date first</div>
                  ) : timeSlots.length > 0 ? (
                    timeSlots.map((slot: TimeSlot, index: number) => (
                      <Button
                        key={slot.availability_id}
                        size="sm"
                        variant="bordered"
                        className={`text-xs ${
                          slot.status === "booked"
                            ? "border-red-500 cursor-not-allowed"
                            : selectedSlot?.availability_id === slot.availability_id
                            ? "bg-blue-600 text-white border-blue-600"
                            : ""
                        }`}
                        onPress={() => slot.status !== "booked" && setSelectedSlot(slot)}
                        isDisabled={slot.status === "booked"}
                      >
                        {formatTimeDisplay(slot.start_time)}-{formatTimeDisplay(slot.end_time)}
                      </Button>
                    ))
                  ) : (
                    <div className="col-span-3 text-xs text-center">No slots available for this date</div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Time slots and form */}
          <div className="flex-1">
            <div className="space-y-4 mt-6">
              <div>
                <Controller
                  name="title"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <Input
                      size="sm"
                      isRequired
                      required
                      {...field}
                      label="Name"
                      classNames={{
                        input: "text-xs",
                        label: "text-xs text-gray-500 ",
                        inputWrapper: "dark:bg-slate-900",
                      }}
                    />
                  )}
                />
              </div>

              <div>
                <Controller
                  name="email"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <Input
                      label="Email"
                      {...field}
                      size="sm"
                      classNames={{
                        input: "text-xs",
                        label: "text-xs text-gray-500 ",
                        inputWrapper: "dark:bg-slate-900",
                      }}
                      type="email"
                      disabled={!!user?.email}
                    />
                  )}
                />
              </div>

              <div>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      size="sm"
                      classNames={{
                        input: "text-xs",
                        label: "text-xs text-gray-500 ",
                        inputWrapper: "dark:bg-slate-900",
                      }}
                      label="Reason"
                    />
                  )}
                />
              </div>

              {/* File Upload Section */}
              <div>
                <p className="text-xs font-medium mb-2">Upload Medical Files (Max 3):</p>
                <div className="border-2 border-dashed dark:border-gray-700 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors">
                  <input type="file" onChange={handleFileChange} accept=".png,.jpg,.jpeg,.pdf,.docx" className="hidden" id="file-upload" multiple />

                  <label htmlFor="file-upload" className="cursor-pointer">
                    <div className="flex flex-col items-center justify-center">
                      <Upload className="w-7 h-7 text-gray-400 mb-3" />
                      <p className="text-xs text-gray-500">File type can be PNG, JPEG, PDF & DOCX</p>
                      <p className="text-xs text-gray-500">Max file size: 10 MB per file</p>
                      <p className="text-xs text-gray-500">Maximum 3 files allowed</p>
                    </div>
                  </label>
                </div>

                {/* Display selected files */}
                {selectedFiles.length > 0 && (
                  <div className="mt-4 space-y-2">
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-100 dark:bg-gray-800 p-2 rounded-lg">
                        <span className="text-xs truncate">{file.name}</span>
                        <Button size="sm" variant="light" className="text-red-500 hover:text-red-700" onPress={() => handleRemoveFile(index)}>
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <Checkbox size="sm" color="warning" isSelected={shareRavidData} onValueChange={setShareRavidData}>
                <label className="text-xs">Share My R.A.V.I.D. Emergency QR Code Data</label>
              </Checkbox>
            </div>
          </div>
        </div>
      </form>
    </div>
  );

  const handleSubmitBooking = () => {
    const formNode = document.getElementById("appointment-form");
    if (formNode) {
      formNode.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }));
    }
  };

  return (
    <>
      <Button
        size="sm"
        variant="bordered"
        className="rounded-lg w-full px-2 py-1 text-xs bg-blue-700 hover:bg-blue-800 text-white border-none"
        onPress={handleAppointmentClick}
      >
        {t("ravid.publicProfile.appointments")}
      </Button>

      <CustomModal
        size="2xl"
        title="Schedule an appointment"
        body={modalBody}
        isOpen={showAppointmentDialog}
        onOpenChange={setShowAppointmentDialog}
        primaryButtonText={isCreatingAppointment || isUploading ? "Processing..." : "Book Appointment"}
        secondaryButtonText="Cancel"
        onPrimaryAction={handleSubmitBooking}
        primaryButtonColor="bg-blue-600 hover:bg-blue-700 text-white"
        secondaryButtonColor="text-gray-500"
        isDisabled={isCreatingAppointment || isUploading}
        isPending={isCreatingAppointment || isUploading}
      />
    </>
  );
};

export default CustomerAppointment;
