"use client";

import { AddIcon } from "@/components/icons";
// import useAuthStore from "@/store/store";
import { useSaveProfileCategory } from "@/hooks/public-profile/useProfileCategory";
import { Button, ButtonProps } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { DevTool } from "@hookform/devtools";
import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { useFieldArray, UseFieldArrayUpdate, useFormContext } from "react-hook-form";
import { FormInput, FormSelect, FormTypeSelect } from "../commons";
import CategoryTabs from "./CategoryTabs";
import {
  AffiliationsCategory,
  AwardsCategory,
  CoursesCategory,
  EducationCategory,
  PodcastsCategory,
  PositionsCategory,
  PracticesCategory,
  ResearchCategory,
} from "./DefaultCategories";

interface CategoryField {
  id: string;
  name: string;
  description: string;
  contents?: any[];
}

const categoryOptions = [
  { key: "education", label: "Education" },
  { key: "practices", label: "Practices" },
  { key: "research", label: "Research" },
  { key: "awards", label: "Awards" },
  { key: "podcasts", label: "Podcasts" },
  { key: "affiliations", label: "Affiliations" },
  { key: "positions", label: "Positions" },
  { key: "courses", label: "Courses" },
  { key: "custom", label: "Customize" },
];

const DynamicProfileBuilder = () => {
  const { mutate: saveCategories } = useSaveProfileCategory();
  const { control, handleSubmit } = useFormContext();

  const { append, prepend, remove, fields, update } = useFieldArray<{ categories: CategoryField[] }>({
    name: "categories",
    control: control as any,
    shouldUnregister: false,
  });
  const scrollToCategory = (categoryId: string) => {
    const element = document.getElementById(categoryId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <form
      className="flex flex-col gap-3 p-2"
      onSubmit={handleSubmit((data: any) => {
        saveCategories(data);
      })}
    >
      {/* <div className="flex justify-end">
          <SaveButton type="submit">Save Your Profile</SaveButton>
        </div> */}
      <CategoryTabs control={control} fields={fields} remove={remove} prepend={prepend} onTabClick={scrollToCategory} />
      <div className="rounded-2xl">
        {fields.map((category, categoryIndex) => (
          <Card key={category.id} id={category.id} className="dark:bg-[#060C1B] border rounded-xl dark:border-slate-800 mt-4">
            <CardBody className="grid grid-cols-2 gap-3">
              <CategoryTypeSelector defaultValue={category.description} categoryIndex={categoryIndex} fields={fields} update={update} />
              <div className="flex justify-end col-span-2 gap-2">
                <SaveButton type="submit">Save</SaveButton>
                <RemoveButton onPress={() => remove(categoryIndex)}>Remove</RemoveButton>
              </div>
            </CardBody>
          </Card>
        ))}
        <DevTool control={control} />
      </div>
    </form>
  );
};

interface CategoryTypeSelectorProps {
  categoryIndex: number;
  defaultValue: string;
  fields: CategoryField[];
  update: UseFieldArrayUpdate<{ categories: CategoryField[] }>;
}

const CategoryTypeSelector = ({ categoryIndex, defaultValue, fields, update }: CategoryTypeSelectorProps) => {
  const { control } = useFormContext();
  const [selectedValue, setSelectedValue] = useState(defaultValue);

  const availableOptions = categoryOptions.filter((option) => {
    if (option.key === "custom") return true;
    const isUsed = fields?.some((cat: any, idx: number) => idx !== categoryIndex && cat.description?.toLowerCase() === option.key);
    return !isUsed;
  });

  const validateTabName = (value: string) => {
    if (value.length < 3) {
      return "Tab name must be at least 3 characters long";
    }
    // Check if tab name already exists in other fields
    const isDuplicate = fields.some((field, idx) => idx !== categoryIndex && field.name?.toLowerCase() === value.toLowerCase());
    if (isDuplicate) {
      return "Tab name must be unique";
    }
    return true;
  };

  return (
    <>
      <div className="space-y-1">
        <FormInput name={`categories.${categoryIndex}.name`} control={control} className="hidden" />
        <FormSelect
          name={`categories.${categoryIndex}.description`}
          control={control}
          options={availableOptions}
          // disabledKeys={["education"]}
          label="Category Type"
          defaultSelectedKeys={[defaultValue]}
          selectorIcon={<ChevronDown className="size-4" />}
          onChange={(e) => {
            const selectedValue = e.target.value;
            // Reset the entire category with the new type, preserving the ID
            const categoryId = fields[categoryIndex].id;
            update(categoryIndex, {
              id: categoryId,
              description: selectedValue,
              name: selectedValue,
              contents: [], // Initialize empty contents for the new category type
            });
            setSelectedValue(selectedValue);
          }}
        />
      </div>
      <div className="space-y-1 col-span-2 flex flex-col gap-1">
        <CategoryComponent categoryIndex={categoryIndex} selectedType={selectedValue} />
      </div>
      {selectedValue === "custom" && (
        <>
          <div className="space-y-1 col-span-2">
            <FormInput
              name={`categories.${categoryIndex}.name`}
              label="Tab Name"
              control={control}
              placeholder="Enter Category Name"
              validate={validateTabName}
            />
          </div>
          <CreateNewContent categoryIndex={categoryIndex} />
        </>
      )}
    </>
  );
};

const CreateNewContent = ({ categoryIndex }: { categoryIndex: number }) => {
  const { control } = useFormContext();
  const { append, remove, fields } = useFieldArray({
    name: `categories.${categoryIndex}.contents`,
    control,
    shouldUnregister: false,
  });

  return (
    <>
      {fields.map((content, contentIndex) => (
        <div key={content.id} className="col-span-2 grid grid-cols-1 gap-3 rounded-lg border dark:border-slate-800 p-4">
          <div className="space-y-1">
            <FormInput name={`categories.${categoryIndex}.contents.${contentIndex}.content.title`} label="Title" control={control} placeholder="Enter title" />
          </div>
          <FormTypeSelect name={`categories.${categoryIndex}.contents.${contentIndex}.content.description`} control={control} />
          <div className="flex justify-end">
            <RemoveButton onPress={() => remove(contentIndex)}>Remove</RemoveButton>
          </div>
        </div>
      ))}
      <div className="flex justify-end col-span-2">
        <AddButton onPress={() => append({ content: { title: "", description: "" } })}>Add a field</AddButton>
      </div>
    </>
  );
};

const CategoryComponent = ({ categoryIndex, selectedType }: { categoryIndex: number; selectedType: string }) => {
  switch (selectedType) {
    case "education":
      return <EducationCategory categoryIndex={categoryIndex} />;
    case "practices":
      return <PracticesCategory categoryIndex={categoryIndex} />;
    case "research":
      return <ResearchCategory categoryIndex={categoryIndex} />;
    case "awards":
      return <AwardsCategory categoryIndex={categoryIndex} />;
    case "podcasts":
      return <PodcastsCategory categoryIndex={categoryIndex} />;
    case "affiliations":
      return <AffiliationsCategory categoryIndex={categoryIndex} />;
    case "positions":
      return <PositionsCategory categoryIndex={categoryIndex} />;
    case "courses":
      return <CoursesCategory categoryIndex={categoryIndex} />;
    default:
      return null;
  }
};

// UI for button and tab
export const SaveButton = ({ children, ...props }: { children: React.ReactNode } & ButtonProps) => {
  return (
    <Button color="primary" size="sm" {...props}>
      {children}
    </Button>
  );
};

export const RemoveButton = ({ children, ...props }: { children: React.ReactNode } & ButtonProps) => {
  return (
    <Button size="sm" className="bg-[#991B1B] text-white" {...props}>
      {children}
    </Button>
  );
};

export const AddButton = ({ children, ...props }: { children: React.ReactNode } & ButtonProps) => {
  return (
    <Button
      className="text-xs w-fit rounded-lg border border-slate-800 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
      size="sm"
      startContent={<AddIcon className="size-4" />}
      {...props}
    >
      {children}
    </Button>
  );
};

export default DynamicProfileBuilder;
