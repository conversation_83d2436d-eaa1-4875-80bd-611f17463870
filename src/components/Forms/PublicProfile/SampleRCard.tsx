import { <PERSON><PERSON>, CardBody } from "@heroui/react";

import { Card } from "@heroui/react";

const card = {
  profile_picture: "/images/r-name.png",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  professional_title: "Software Engineer",
  address: "123 Main St, Anytown, USA",
  phone: "****** 444 3344",
  email: "joan<PERSON>@example.com",
  additional_information: "Additional information",
  url: "www.joan2449/user/ravid.com",
};

const SampleRCard = ({ onClose, onCreate, isCreating }: { onClose: () => void; onCreate: () => void; isCreating: boolean }) => {
  return (
    <div className="flex flex-col w-full  justify-center">
      <p className="text-gray-300 text-sm mb-6">Here you can add your information that you would like to share with people as your Business Card</p>
      <Card className="max-w-3xl w-full bg-slate-950 border border-yellow-800 rounded-lg overflow-hidden">
        <CardBody className="p-0">
          <div className="flex flex-col md:flex-row">
            <div className="p-8 flex flex-col items-center">
              <div className="w-40 h-40 rounded-full overflow-hidden">
                <img src={card.profile_picture} alt="Profile" className="w-full h-full object-cover" />
              </div>
            </div>

            <div className="flex-1 p-4 md:p-8 space-y-6">
              <div className="rounded-md space-y-2 p-2 relative">
                <h2 className="text-white">
                  {card.first_name} {card.last_name}
                </h2>

                <p className="text-gray-300">{card.professional_title}</p>
                <div className="mt-2 text-sm text-blue-400 underline">{card.url}</div>
              </div>

              <div className="rounded-md space-y-2 p-2 relative">
                <p className="text-gray-300 font-bold">Address</p>
                <p className="text-gray-300">{card.address}</p>
                <p className="text-gray-300 mt-2 font-bold">Phone</p>
                <p className="text-gray-300">{card.phone}</p>
                <p className="text-gray-300 font-bold">Email</p>
                <p className="text-gray-300">{card.email}</p>
                <p className="text-gray-300 mt-2 font-bold">Additional Information:</p>
                <p className="text-gray-300">{card.additional_information}</p>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
      <div className="flex justify-end gap-3 mt-4">
        <Button variant="flat" className="bg-gray-700 hover:bg-gray-600" size="sm" type="button" onPress={onClose}>
          Close
        </Button>
        <Button color="primary" size="sm" type="button" onPress={onCreate} isLoading={isCreating}>
          Create My R Card
        </Button>
      </div>
    </div>
  );
};

export default SampleRCard;
