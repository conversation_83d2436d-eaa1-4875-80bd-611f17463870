import DefaultAvatar from "@/components/DefaultAvatar";
import CustomModal from "@/components/Modals/CustomModal";
import QRCodeGenerator from "@/components/QRCodeGenerator";
import { useCreateEditCard, useGetCard } from "@/hooks/useCard";
import { useGetUser } from "@/hooks/useUser";
import { Button, Checkbox, useDisclosure } from "@heroui/react";
import { Edit, QrCode } from "lucide-react";
import Link from "next/link";
import { useEffect, useMemo, useRef, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { FormInput } from "../commons";
import SampleRCard from "./SampleRCard";

// QR Modal Form State Interface
interface QRModalFormState {
  id?: string;
  first_name?: string;
  middle_name?: string;
  last_name?: string;
  professional_title?: string;
  address?: string;
  email?: string;
  phone?: string;
  additional_information?: string;
  url?: string;
}

const BusinessCardSection = () => {
  const { t } = useTranslation();
  const { data: user } = useGetUser();
  const { data: existingCard, isLoading: isCardLoading } = useGetCard(user?.user_id || "");
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
  const { isOpen: isQrOpen, onOpen: onQrOpen, onOpenChange: onQrOpenChange } = useDisclosure();
  const qrRef = useRef<HTMLDivElement>(null);
  const [qrError, setQrError] = useState(false);
  const [includeUrl, setIncludeUrl] = useState(true);
  const { mutate: createCard, isPending: isCreateCardLoading } = useCreateEditCard();

  const currentProfileUrl = `${process.env.NEXT_PUBLIC_APP_URL}/share/${user?.user_id}/Ravid-Name-Card/`;
  const displayUrl = `${process.env.NEXT_PUBLIC_APP_URL}/${user?.user_id}/user/${user?.username ? user.username : user?.first_name}`;

  // Memoized QR form default values - prioritize existing card data over user data
  const qrFormDefaults = useMemo(
    (): QRModalFormState => ({
      id: existingCard?.id || undefined,
      first_name: existingCard?.first_name || user?.first_name || "",
      middle_name: existingCard?.middle_name || user?.middle_name || "",
      last_name: existingCard?.last_name || user?.last_name || "",
      professional_title: existingCard?.professional_title || "",
      address: existingCard?.address || user?.address || "",
      email: existingCard?.email || user?.email || "",
      phone: existingCard?.phone || user?.mobile || "",
      additional_information: existingCard?.additional_information || "",
      url: includeUrl ? displayUrl : "",
    }),
    [existingCard, user, includeUrl, displayUrl]
  );

  // QR Modal Form
  const qrForm = useForm<QRModalFormState>({
    defaultValues: qrFormDefaults,
  });

  // Reset form when modal opens with fresh user data
  useEffect(() => {
    if (isOpen) {
      qrForm.reset(qrFormDefaults);
    }
  }, [isOpen, qrForm, qrFormDefaults]);

  // Simplified form submission handlers
  const handleQrFormSubmit = (data: QRModalFormState) => {
    // Prepare data for submission
    const submissionData = {
      ...data,
      user: user?.user_id, // Add user ID to the submission
    };

    // Remove empty/undefined values except for id
    const cleanedData = Object.fromEntries(
      Object.entries(submissionData).filter(([key, value]) => {
        if (key === "id") return value !== undefined && value !== null;
        return value !== undefined && value !== null && value !== "";
      })
    );

    createCard(cleanedData);
    onOpenChange();
  };

  // Simplified URL checkbox handler
  const handleUrlCheckboxChange = (checked: boolean) => {
    setIncludeUrl(checked);
    qrForm.setValue("url", checked ? currentProfileUrl : "");
  };

  const handleQrError = (error: boolean) => {
    setQrError(error);
  };

  // Simplified QR edit modal content
  const renderQRModalContent = () => (
    <>
      {existingCard ? (
        <FormProvider {...qrForm}>
          <form onSubmit={qrForm.handleSubmit(handleQrFormSubmit)}>
            <div className="flex flex-col md:flex-row gap-6">
              {/* Left side - Profile image */}
              {user?.profile_picture ? (
                <div className="flex flex-col items-center">
                  <div className="w-[120px] h-[120px] rounded-full overflow-hidden mb-2">
                    <img src={user?.profile_picture} alt="Profile" className="w-full h-full object-cover" />
                  </div>
                </div>
              ) : (
                <div className="w-[120px] h-[120px] rounded-full overflow-hidden mb-2">
                  <DefaultAvatar />
                </div>
              )}

              {/* Right side - Profile data */}
              <div className="flex-1 space-y-3">
                {/* Name fields in one row */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                  <FormInput label="First Name" name="first_name" control={qrForm.control} placeholder="First Name" size="sm" className="text-xs" />
                  <FormInput label="Middle Name" name="middle_name" control={qrForm.control} placeholder="Middle Name" size="sm" className="text-xs" />
                  <FormInput label="Last Name" name="last_name" control={qrForm.control} placeholder="Last Name" size="sm" className="text-xs" />
                </div>

                {/* Title and Address in one group */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <FormInput
                    label="Professional Title"
                    name="professional_title"
                    control={qrForm.control}
                    placeholder="Professional Title"
                    size="sm"
                    className="text-xs"
                  />
                  <FormInput label="Address" name="address" control={qrForm.control} size="sm" className="text-xs" />
                </div>

                {/* Contact information in one row */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <FormInput label="Phone" name="phone" control={qrForm.control} type="tel" size="sm" className="text-xs" />
                  <FormInput label="Email" name="email" control={qrForm.control} type="email" size="sm" className="text-xs" />
                </div>

                {/* Additional text */}
                <FormInput
                  label="Additional Information"
                  name="additional_information"
                  control={qrForm.control}
                  placeholder="Enter additional information"
                  size="sm"
                  className="text-xs"
                />

                {/* URL field */}
                <div className="flex flex-col gap-2">
                  <Checkbox isSelected={includeUrl} onValueChange={handleUrlCheckboxChange} size="sm">
                    <h2>Include Profile URL</h2>
                  </Checkbox>
                  <span className={`text-xs ${includeUrl ? "text-blue-400 font-medium" : "text-gray-500 opacity-50"}`}>{displayUrl}</span>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex justify-end gap-3 mt-4">
              <Button variant="flat" className="bg-gray-700 hover:bg-gray-600" size="sm" onPress={onClose} type="button">
                Cancel
              </Button>
              <Button color="primary" size="sm" type="submit">
                Save
              </Button>
            </div>
          </form>
        </FormProvider>
      ) : (
        <SampleRCard onClose={onClose} onCreate={() => createCard({})} isCreating={isCreateCardLoading} />
      )}
    </>
  );

  // QR share modal content
  const renderQrShareModalContent = () => (
    <div className="flex flex-col items-center gap-4">
      <h2>Share Your Profile</h2>
      <div ref={qrRef} className="flex justify-center items-center min-h-[300px] w-full"></div>
      <p className="muted">Scan this QR code to view your public profile</p>
      <QRCodeGenerator qrRef={qrRef} onError={handleQrError} isModalOpen={isQrOpen} url={currentProfileUrl} />
      <Link href={currentProfileUrl} target="_blank">
        URL
      </Link>
    </div>
  );

  return (
    <>
      <div className="w-full lg:col-span-4 flex flex-col gap-2 border border-slate-900 rounded-md p-4">
        <h2 className="">R Card - QR Enable</h2>
        <div className="flex flex-col sm:flex-row lg:flex-col gap-2">
          <Button size="sm" className="w-full" startContent={<Edit className="h-4 w-4" />} onPress={onOpen} isLoading={isCardLoading} disabled={isCardLoading}>
            {/* {existingCard ? "Edit QR Code" : "Create QR Code"} */}
            Edit R Card
          </Button>
          <Button size="sm" className="w-full" startContent={<QrCode className="h-4 w-4" />} onPress={onQrOpen}>
            Share R Card
          </Button>
        </div>
      </div>
      {/* QR Code Edit Modal */}
      <CustomModal
        isOpen={isOpen}
        size="2xl"
        onOpenChange={onOpenChange}
        title={existingCard ? "Edit R Card" : "Sample R Card"}
        body={renderQRModalContent()}
      />
      {/* Add QR Share Modal */}
      <CustomModal isOpen={isQrOpen} onOpenChange={onQrOpenChange} title="Share Your Profile" body={renderQrShareModalContent()} />
    </>
  );
};

export default BusinessCardSection;
