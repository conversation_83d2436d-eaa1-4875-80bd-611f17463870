"use client";
import { useRegister } from "@/hooks/useUser";
import { RegisterFormType, RegisterFormValidation } from "@/lib/utils/validations";
import { Card } from "@heroui/card";
import { zodResolver } from "@hookform/resolvers/zod";
import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import SubmitButton from "../SubmitButton";
import { FormInput } from "./commons";
import OAuthLogin from "./OauthComponent";
const RegisterForm: React.FC = () => {
  const { mutate: registerUser, isPending } = useRegister();
  const {
    control,
    handleSubmit,
    formState: { isSubmitting, errors },
    reset,
  } = useForm<RegisterFormType>({
    resolver: zodResolver(RegisterFormValidation),
    mode: "onChange", // This will enable real-time validation
  });

  const onSubmit = async (data: RegisterFormType) => {
    registerUser(data, {
      onSuccess: () => {
        toast.success("User registered successfully, please check your email for verification before logging in");
        reset();
      },
      onError: (error: any) => {
        console.log("error", error?.response?.data.errors.email[0]);
        toast.error(error?.response?.data?.errors?.email[0] || "Registration failed");
      },
    });
  };

  return (
    <Card className="p-6 dark:bg-black col-span-4 border dark:border-gray-800 w-full max-w-md mx-auto">
      <h2 className="text-lg mb-3 text-left">Sign Up for your R.A.V.I.D. account here:</h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormInput name="email" type="email" label="Email Address" size="sm" control={control} required />

        <FormInput name="password" type="password" label="Password" size="sm" control={control} required />

        <FormInput name="confirmPassword" type="password" label="Confirm Password" control={control} size="sm" required />

        <SubmitButton type="submit" className="w-full" size="sm" color="primary" isLoading={isPending} isLoadingValue="Submitting..." label="Create Account" />
      </form>
      <div className="mt-4">
        <OAuthLogin type="register" />
      </div>
    </Card>
  );
};

export default RegisterForm;
