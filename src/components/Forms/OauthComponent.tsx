"use client";
import { <PERSON><PERSON> } from "@heroui/button";
import React from "react";
import { AppleIcon, GoogleIcon } from "../icons";
import Separator from "../Seperator";
import toast from "react-hot-toast";

const OAuthLogin = ({ type }: { type: "login" | "register" }) => {
  const googleLogin = async () => {
    try {
      const googleLoginUrl = `https://test.ravid.cloud/api/sso/login/google/`;
      window.location.href = googleLoginUrl;
    } catch (error) {
      console.error("Google login failed:", error);
      toast.error("Google login failed. Please try again.");
    }
  };

  const handleApple = async () => {
    try {
      const appleLoginUrl = `https://test.ravid.cloud/api/sso/login/apple/`;
      window.location.href = appleLoginUrl;
    } catch (error) {
      console.error("Apple login failed:", error);
      toast.error("Apple login failed. Please try again.");
    }
  };

  return (
    <>
      <div className="relative mb-6 flex items-center">
        <Separator className="flex-1" color="gray" />
        <span className="mx-4 text-xs dark:text-gray-300">Or continue with</span>
        <Separator className="flex-1" color="gray" />
      </div>
      <div className="text-center mb-4 text-xs flex justify-center mx-auto">
        <p>
          By clicking Sign Up, you agree to R.A.V.I.D.'s <span className="text-blue-500">Terms & Conditions of Use/ User Agreement</span>,{" "}
          <span className="text-blue-500">Privacy Policy</span> and <span className="text-blue-500">Cookie Policy</span>.
        </p>
      </div>
      <div className="mb-6 flex items-center justify-center gap-2 p-1">
        <Button onPress={handleApple} variant="bordered" size="sm" className="w-full flex items-center gap-1 justify-center">
          <AppleIcon className="h-5 w-5 " />
          Apple
        </Button>
        <Button onPress={googleLogin} variant="bordered" size="sm" className="w-full flex items-center gap-1 justify-center">
          <GoogleIcon className="h-5 w-5 " />
          Google
        </Button>
      </div>
    </>
  );
};

export default OAuthLogin;
