"use client";

import { Select, SelectItem } from "@heroui/select";
import { useState } from "react";
import { Controller } from "react-hook-form";
import FormDatePicker from "./date-picker";
import FormInput from "./input";
import FormTextArea from "./text-area";

interface TypeSelectProps {
  name: string;
  control: any;
}

const inputTypes = [
  {
    key: "text",
    label: "Text",
  },
  {
    key: "date",
    label: "Date",
  },
  {
    key: "url",
    label: "URL",
  },
];

const TypeSelect = ({ name, control, ...props }: TypeSelectProps) => {
  const [selectedKey, setSelectedKey] = useState<string>("text");

  const renderComponent = (key: string) => {
    switch (key) {
      case "text":
        return <FormInput name={name} control={control} label="Value" />;
      case "textArea":
        return <FormTextArea name={name} control={control} label="Value" />;
      case "url":
        return <FormInput name={name} type="url" control={control} placeholder="Type your link..." label="Value" />;
      case "date":
        return <FormDatePicker name={name} control={control} label="Value" />;
      case "dateRange":
        return <FormDatePicker name={name} control={control} label="Value" isTimeRange />;
      default:
        return null;
    }
  };

  return (
    <>
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState: { invalid } }) => (
          <Select
            aria-label="select"
            label="Input Feature Format"
            classNames={{
              trigger: "dark:bg-slate-900 text-xs",
              value: "dark:bg-slate-900 text-xs",
              innerWrapper: "dark:bg-slate-900 text-xs",
              label: "text-xs",
            }}
            defaultSelectedKeys={["text"]}
            onSelectionChange={(keys) => setSelectedKey(keys.currentKey as string)}
            {...props}
          >
            {inputTypes.map((inputType) => (
              <SelectItem key={inputType.key}>{inputType.label}</SelectItem>
            ))}
          </Select>
        )}
      />
      {renderComponent(selectedKey)}
    </>
  );
};

export default TypeSelect;
