import { Input, InputProps } from "@heroui/input";
import { KeyboardEvent, useState } from "react";
import { Control, Controller } from "react-hook-form";
import { toast } from "react-hot-toast";

interface FormTagInputProps extends Omit<InputProps, "value" | "onChange"> {
  name: string;
  control: Control<any>;
  placeholder?: string;
}

const FormTagInput = ({ name, control, placeholder, ...props }: FormTagInputProps) => {
  const [inputValue, setInputValue] = useState("");
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { error } }) => {
        const tags = Array.isArray(value) ? value : [];
        const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
          if (e.key === "Enter" && inputValue.trim()) {
            e.preventDefault();
            const trimmedInput = inputValue.trim();
            if (!tags.includes(trimmedInput)) {
              const newTags = [...tags, trimmedInput];
              onChange(newTags);
              setInputValue("");
            } else {
              toast.error(`Tag "${trimmedInput}" already exists`);
              setInputValue("");
            }
          }
        };
        // Remove any existing duplicates from the tags array (handles initial data)
        const uniqueTags = [...new Set(tags)];
        if (uniqueTags.length !== tags.length) {
          // Update the form value if duplicates were found
          onChange(uniqueTags);
        }
        const removeTag = (index: number) => {
          const newTags = tags.filter((_, i) => i !== index);
          onChange(newTags);
        };

        return (
          <div>
            <div className="rounded-md p-2 border dark:border-slate-800">
              <div className="flex flex-wrap gap-2 items-center">
                {tags.map((tag, index) => (
                  <div key={index} className="bg-gray-800 text-white text-xs px-3 py-1 rounded-full flex items-center">
                    {tag}
                    <button type="button" onClick={() => removeTag(index)} className="ml-2 text-gray-400 hover:text-white">
                      ×
                    </button>
                  </div>
                ))}
                <Input
                  classNames={{
                    input: "text-xs",
                    label: "text-xs text-gray-500",
                    inputWrapper: "dark:bg-slate-900 min-h-[30px] py-1 px-2",
                    base: "w-auto min-w-[170px] flex-grow-0 inline-flex",
                  }}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onBlur={onBlur}
                  placeholder={"Press Enter after each input"}
                  size="sm"
                  {...props}
                />
              </div>
            </div>
            <div className="text-red-600 text-xs mt-1">{error?.message}</div>
          </div>
        );
      }}
    />
  );
};

export default FormTagInput;
