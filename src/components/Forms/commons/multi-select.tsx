import { useState, useRef, useEffect } from "react";
import { Search, X, Loader2 } from "lucide-react";
import { Control, Controller } from "react-hook-form";

interface Option {
  key: string;
  label: string;
}

export interface MultiSelectProps {
  name: string;
  control: Control<any>;
  options: Option[];
  placeholder?: string;
  isLoading?: boolean;
  onInputChange?: (value: string) => void;
}

const MultiSelect = ({ name, control, options, placeholder = "Search...", isLoading, onInputChange }: MultiSelectProps) => {
  const [inputValue, setInputValue] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value = [], onChange }, fieldState: { error } }) => {
        // Close dropdown when clicking outside
        useEffect(() => {
          function handleClickOutside(event: MouseEvent) {
            if (
              dropdownRef.current &&
              !dropdownRef.current.contains(event.target as Node) &&
              inputRef.current &&
              !inputRef.current.contains(event.target as Node)
            ) {
              setIsOpen(false);
            }
          }

          document.addEventListener("mousedown", handleClickOutside);
          return () => document.removeEventListener("mousedown", handleClickOutside);
        }, []);

        const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          const newValue = e.target.value;
          setInputValue(newValue);
          setIsOpen(true);
          if (onInputChange) {
            onInputChange(newValue);
          }
        };

        const handleSelectItem = (item: Option) => {
          if (!value.includes(item.key)) {
            onChange([...value, item.key]);
          }
          setInputValue("");
          setIsOpen(false);
          inputRef.current?.focus();
        };

        const handleRemoveItem = (itemKey: string) => {
          onChange(value.filter((key: string) => key !== itemKey));
        };

        const handleKeyDown = (e: React.KeyboardEvent) => {
          if (e.key === "Enter" && inputValue && options.length > 0) {
            e.preventDefault();
            handleSelectItem(options[0]);
          }
        };

        // Filter out already selected options
        const availableOptions = options.filter((option) => !value.includes(option.key));

        return (
          <div className="w-full">
            <div className="relative">
              <div className="flex items-center border border-gray-300 rounded-lg p-1 bg-white dark:bg-slate-900 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                {isLoading ? <Loader2 className="ml-2 text-gray-400 animate-spin" size={14} /> : <Search className="ml-2 text-gray-400" size={14} />}

                <div className="flex flex-wrap flex-1 gap-1 p-1">
                  {value.map((itemKey: string) => (
                    <div key={itemKey} className="flex items-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 rounded px-2 py-0.5 text-xs">
                      <span>{itemKey}</span>
                      <button
                        onClick={() => handleRemoveItem(itemKey)}
                        className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  ))}

                  <input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    className="flex-1 outline-none px-2 py-0.5 min-w-24 bg-transparent text-xs"
                    placeholder={value.length === 0 ? placeholder : ""}
                  />
                </div>
              </div>

              {isOpen && (inputValue.length >= 1 || isLoading || availableOptions.length > 0) && (
                <div
                  ref={dropdownRef}
                  className="absolute z-10 mt-1 w-full bg-white dark:bg-slate-900 border border-gray-300 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-auto"
                >
                  {isLoading ? (
                    <div className="px-3 py-2 text-xs text-gray-500">Searching...</div>
                  ) : availableOptions.length > 0 ? (
                    <ul className="py-1">
                      {availableOptions.map((item, index) => (
                        <li
                          key={index}
                          onClick={() => handleSelectItem(item)}
                          className="px-3 py-1.5 hover:bg-blue-50 dark:hover:bg-blue-900/50 cursor-pointer text-xs"
                        >
                          <span className="font-medium">{item.key}</span>
                          {item.label !== item.key && <span className="text-gray-500 dark:text-gray-400 ml-2">{item.label}</span>}
                        </li>
                      ))}
                    </ul>
                  ) : inputValue.length >= 1 ? (
                    <div className="px-3 py-2 text-xs text-gray-500">No results found</div>
                  ) : null}
                </div>
              )}
            </div>
            {error && <div className="text-red-600 text-xs mt-1">{error.message}</div>}
          </div>
        );
      }}
    />
  );
};

export default MultiSelect;
