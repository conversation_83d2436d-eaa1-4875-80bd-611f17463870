import { DatePicker, DatePickerProps, DateRangePicker } from "@heroui/date-picker";
import { CalendarDate } from "@internationalized/date";
import { Controller } from "react-hook-form";

interface FormDatePickerProps extends DatePickerProps {
  name: string;
  control: any;
  isTimeRange?: boolean;
  label?: string;
}

const formatDate = (date: any) => {
  return `${date.year}-${String(date.month).padStart(2, "0")}-${String(date.day).padStart(2, "0")}`;
};

// Converts various date formats to a CalendarDate object or null
const parseToCalendarDate = (value: any): CalendarDate | null => {
  if (!value) return null;

  // If already a CalendarDate, return it
  if (value instanceof CalendarDate) {
    return value;
  }

  try {
    // Convert string to CalendarDate
    if (typeof value === "string") {
      const parts = value.split("-").map(Number);
      if (parts.length !== 3) return null;

      const [year, month, day] = parts;

      // Validate date components
      if (!Number.isFinite(year) || !Number.isFinite(month) || !Number.isFinite(day)) {
        return null;
      }

      // Validate reasonable ranges
      if (year < 1900 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31) {
        return null;
      }

      return new CalendarDate(year, month, day);
    }

    // Convert Date object to CalendarDate
    if (value instanceof Date && !isNaN(value.getTime())) {
      return new CalendarDate(value.getFullYear(), value.getMonth() + 1, value.getDate());
    }

    return null;
  } catch (e) {
    console.error("Error parsing date:", e);
    return null;
  }
};

const FormDatePicker = ({ name, control, isTimeRange, label, ...props }: FormDatePickerProps) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error, invalid } }) => {
        const calendarDateValue = parseToCalendarDate(value);

        return (
          <div>
            {isTimeRange ? (
              <DateRangePicker
                onChange={(newValue: any) => {
                  const formattedValue = JSON.stringify({
                    start: formatDate(newValue.start),
                    end: formatDate(newValue.end),
                  });
                  onChange(formattedValue);
                }}
                aria-label="datePicker"
                classNames={{
                  input: "text-xs",
                  inputWrapper: "dark:bg-slate-900",
                  label: "text-xs",
                }}
              />
            ) : (
              <DatePicker
                label={label}
                classNames={{
                  label: "text-[10px]", // bugs hero ui maybe: 10px is equal to text-xs (12px) in normal input label
                  input: "text-xs text-white",
                  inputWrapper: "dark:bg-slate-900 hover:bg-slate-900",
                }}
                value={calendarDateValue as any}
                onChange={(newValue) => {
                  if (newValue) {
                    // Convert CalendarDate directly to YYYY-MM-DD string
                    onChange(formatDate(newValue as unknown as CalendarDate));
                  } else {
                    onChange("");
                  }
                }}
                aria-label="datePicker"
                {...props}
              />
            )}
            <div className="text-red-600 text-sm mt-2 ml-2">{error?.message}</div>
          </div>
        );
      }}
    />
  );
};

export default FormDatePicker;
