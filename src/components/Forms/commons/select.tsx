import { Select, SelectItem, SelectProps } from "@heroui/select";
import { Controller, Control } from "react-hook-form";

interface FormSelectProps extends Omit<SelectProps, "children"> {
  name: string;
  control: Control<any>;
  options: { key: string | boolean; label: string }[];
}

const FormSelect = ({ name, control, options, ...props }: FormSelectProps) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value, onChange }, fieldState: { error } }) => (
        <div>
          <Select
            classNames={{
              trigger: "dark:bg-slate-900 text-xs",
              value: "dark:bg-slate-900 text-xs",
              innerWrapper: "dark:bg-slate-900 text-xs",
              label: "text-xs",
            }}
            aria-label="select"
            selectedKeys={value !== undefined && value !== null ? [String(value)] : undefined}
            onSelectionChange={(keys) => {
              const selectedValue = Array.from(keys)[0];
              const finalValue = selectedValue === "true" ? true : selectedValue === "false" ? false : selectedValue;
              onChange(finalValue);
            }}
            errorMessage={error?.message}
            {...props}
          >
            {options.map((option) => (
              <SelectItem key={String(option.key)}>{option.label}</SelectItem>
            ))}
          </Select>
          <div className="text-red-600 text-xs mt-1">{error?.message}</div>
        </div>
      )}
    />
  );
};

export default FormSelect;
