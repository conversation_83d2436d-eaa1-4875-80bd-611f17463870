import { Input, InputProps } from "@heroui/input";
import { Control, Controller } from "react-hook-form";
import React, { useState } from "react";
import { Eye, EyeOff } from "lucide-react";

interface FormInputProps extends InputProps {
  name: string;
  control: Control<any>;
  inputProps?: any;
}

const FormInput = ({ name, control, inputProps, type, ...props }: FormInputProps) => {
  const [showPassword, setShowPassword] = useState(false);

  const isPasswordField = type === "password";
  const inputType = isPasswordField && showPassword ? "text" : type;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { error } }) => (
        <div className="w-full">
          <Input
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500 ",
              inputWrapper: "dark:bg-slate-900",
              ...inputProps,
            }}
            value={value ?? ""}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            errorMessage={error?.message}
            type={inputType}
            endContent={
              isPasswordField ? (
                <button
                  type="button"
                  className="flex items-center justify-center h-full px-2 focus:outline-none"
                  onClick={() => setShowPassword(!showPassword)}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff className="w-4 h-4 text-gray-500" /> : <Eye className="w-4 h-4 text-gray-500" />}
                </button>
              ) : null
            }
            {...props}
          />
          <div className="text-red-600 text-xs mt-1">{error?.message}</div>
        </div>
      )}
    />
  );
};

export default FormInput;