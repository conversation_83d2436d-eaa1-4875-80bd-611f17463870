import { Textarea, TextAreaProps } from "@heroui/input";
import { Controller } from "react-hook-form";

interface FormTextAreaProps extends TextAreaProps {
  name: string;
  control: any;
  customClassNames?: {
    input?: string;
    label?: string;
    inputWrapper?: string;
  };
}

const FormTextArea = ({ name, control, customClassNames, ...props }: FormTextAreaProps) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { error, invalid } }) => (
        <div>
          <Textarea
            classNames={{
              input: `text-xs ${customClassNames?.input || ""}`,
              label: `text-xs text-gray-500 placeholder:text-gray-500 ${customClassNames?.label || ""}`,
              inputWrapper: `dark:bg-slate-900 ${customClassNames?.inputWrapper || ""}`,
            }}
            onChange={onChange}
            value={value ?? ""}
            placeholder="Type something..."
            {...props}
          />
          <div className="text-red-600 text-sm mt-2 ml-2">{error?.message}</div>
        </div>
      )}
    />
  );
};

export default FormTextArea;
