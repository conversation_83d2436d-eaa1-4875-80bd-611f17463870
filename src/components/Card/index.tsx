import { Card, CardBody, Card<PERSON><PERSON>er, CardProps } from "@heroui/card";
import { useTranslation } from "react-i18next";

interface CardContainerProps extends CardProps {
  title: string;
  children: React.ReactNode;
  classNames?: {
    base?: string;
    header?: string;
    body?: string;
  };
}

const CardContainer = ({ title, children, classNames = {}, ...props }: CardContainerProps) => {
  const { t } = useTranslation();
  return (
    <Card
      classNames={{
        base: classNames.base || "col-span-12 md:col-span-3 dark:bg-[#060C1B] border rounded-xl dark:border-slate-800",
        header: classNames.header || "border-b dark:border-slate-800",
        body: classNames.body || "grid grid-cols-2 gap-2",
      }}
      {...props}
    >
      <CardHeader>
        <h2>{t(title)}</h2>
      </CardHeader>
      <CardBody>{children}</CardBody>
    </Card>
  );
};

export default CardContainer;
