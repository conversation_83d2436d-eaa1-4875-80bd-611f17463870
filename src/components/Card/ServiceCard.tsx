import { Button, Input } from "@heroui/react";
import { useState } from "react";

// Types
interface PriceInfo {
  price: number | string;
  discounted_price?: number | string;
  discount_description?: string | null;
  priceUnit: string;
}

interface ServiceInfo {
  title: string;
  benefits: string[];
  button_text?: string;
}

interface ServiceCardProps {
  serviceInfo?: ServiceInfo;
  priceInfo?: PriceInfo;
  status?: {
    isActive?: boolean;
    isDisabled?: boolean;
    isSelected?: boolean;
  };
  customization?: {
    showStorageInput?: boolean;
    onStorageChange?: (value: string) => void;
  };
  onClick: () => void;
  buttonText?: string;
}

// Components
const PriceDisplay = ({ priceInfo }: { priceInfo: PriceInfo }) => {
  const { price, discounted_price = price, priceUnit } = priceInfo;
  const hasDiscount = discounted_price !== price;

  return (
    <p className="text-xs font-medium text-foreground mb-2">
      {hasDiscount ? (
        <>
          <span className="line-through text-gray-400">${price}</span>
          <span className="text-blue-500 ml-1">${discounted_price}</span>
        </>
      ) : (
        `$${price}`
      )}
      <span className="text-xs font-normal"> per {priceUnit}</span>
    </p>
  );
};

const StatusBadges = ({ price, discounted_price, isActive }: { price: number | string; discounted_price: number | string; isActive?: boolean }) => (
  <>
    {isActive && <span className="px-2 py-1 bg-green-500/20 text-green-500 rounded-full text-xs">Active</span>}
    {discounted_price !== price && <span className="px-2 py-1 bg-blue-500/20 text-blue-500 rounded-full text-xs ml-1">Sale</span>}
  </>
);

const ServiceCard = ({ serviceInfo, priceInfo, status, customization, onClick, buttonText }: ServiceCardProps) => {
  const [customStorage, setCustomStorage] = useState("");
  const { isActive, isDisabled, isSelected } = status || {};

  if (!priceInfo) {
    return null; // Or render a fallback UI
  }

  return (
    <div className="rounded-[16px] bg-card p-2 flex flex-col border-t-2 border-blue-500 shadow-sm max-w-[320px] min-h-[220px] mx-auto w-full dark:bg-slate-950 relative">
      <div className="flex justify-between items-start mb-6">
        <h3 className="text-xs font-semibold text-foreground">{serviceInfo?.title}</h3>
        <StatusBadges price={priceInfo.price} discounted_price={priceInfo.discounted_price || priceInfo.price} isActive={isActive} />
      </div>

      <div className="space-y-4 flex-grow">
        {serviceInfo?.benefits.map((benefit, idx) => (
          <BenefitItem key={idx} text={benefit} />
        ))}

        {customization?.showStorageInput && (
          <div className="mb-4">
            <Input
              size="sm"
              value={customStorage}
              onChange={(e) => {
                setCustomStorage(e.target.value);
                customization.onStorageChange?.(e.target.value);
              }}
              placeholder="Enter storage in GB"
              classNames={{
                input: "text-xs",
                label: "text-xs text-gray-500",
                inputWrapper: "dark:bg-slate-900",
              }}
            />
          </div>
        )}
      </div>

      <div className="mt-6">
        <div className="h-px bg-gray-200 dark:bg-gray-700 mb-4"></div>

        <div className="flex justify-between gap-2">
          <PriceDisplay priceInfo={priceInfo} />
          {priceInfo?.discount_description && <p className="text-xs font-medium muted mb-2">{priceInfo.discount_description}</p>}
        </div>

        <Button onPress={onClick} size="sm" isDisabled={isActive || isDisabled} color={isSelected ? "default" : "primary"} className="w-full">
          {isActive ? "Subscribed" : isSelected ? "Selected" : buttonText || "Select"}
        </Button>
      </div>
    </div>
  );
};

const BenefitItem = ({ text }: { text: string }) => (
  <div className="flex items-center gap-3">
    <div className="w-4 h-4 rounded-full flex items-center justify-center">
      <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
        <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" />
      </svg>
    </div>
    <span className="text-xs text-foreground">{text}</span>
  </div>
);

export default ServiceCard;
