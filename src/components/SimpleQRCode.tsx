"use client";
import React, { useEffect, useState } from "react";
import { useGetUser } from "@/hooks/useUser";
import { useGenerateQRCode } from "@/hooks/settings-dashboard/useQRCode";
import { Spinner } from "@heroui/react";

interface SimpleQRCodeProps {
  onError?: (error: boolean) => void;
  onTokenGenerated?: (token: string) => void;
}

const SimpleQRCode = ({ onError, onTokenGenerated }: SimpleQRCodeProps) => {
  const { data: user } = useGetUser();
  const { data: qrCodeData, isLoading } = useGenerateQRCode();
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (qrCodeData && user?.user_id) {
      try {
        const token = qrCodeData.token;
        const url = `${process.env.NEXT_PUBLIC_APP_URL}/share/${user.user_id}?token=${token}`;

        // Pass token back to parent component
        if (onTokenGenerated) {
          onTokenGenerated(token);
        }

        // Use Google Charts API to generate QR code
        const qrCodeUrl = `https://chart.googleapis.com/chart?cht=qr&chl=${encodeURIComponent(url)}&chs=300x300&chco=000000&chf=bg,s,FFFFFF`;
        setQrCodeUrl(qrCodeUrl);
      } catch (err) {
        console.error("Error generating QR code:", err);
        setError(true);
        onError?.(true);
      }
    }
  }, [qrCodeData, user, onError, onTokenGenerated]);

  if (isLoading) {
    return (
      <div className="flex justify-center my-4">
        <Spinner size="lg" color="primary" />
      </div>
    );
  }

  if (error) {
    return <div className="text-center text-red-500 p-4">Unable to generate QR code. Please try again later.</div>;
  }

  if (!qrCodeUrl) {
    return <div className="text-center p-4">No QR code data available.</div>;
  }

  return (
    <div className="flex justify-center items-center p-4">
      <img src={qrCodeUrl} alt="Emergency QR Code" className="border border-gray-200 rounded-lg" width={300} height={300} />
    </div>
  );
};

export default SimpleQRCode;
