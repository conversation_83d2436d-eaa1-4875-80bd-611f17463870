"use client";
import { ArrowDownToLine } from "lucide-react";
import { useRef } from "react";

const DownloadAsPdf = ({ text, className }: { text: string; className: string }) => {
  const iframeRef = useRef<HTMLIFrameElement | null>(null);

  const downloadAsPDF = (): void => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const iframeDoc = iframe.contentWindow?.document;
    if (!iframeDoc) return;

    const formattedDate = new Date()
      .toISOString()
      .split("T")[0]
      .replace(/-/g, "/");

    iframeDoc.writeln(`
        <html>
          <head>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 40px;
                line-height: 1.6;
                color: #333;
              }
              .header {
                text-align: center;
                margin-bottom: 30px;
                color: #7C389B;
              }
              .content {
                white-space: pre-wrap;
                word-wrap: break-word;
                max-width: 800px;
                margin: 0 auto;
                font-size: 14px;
              }
              .footer {
                font-size: 12px;
                color: #8d8d8d;
                margin-top: 20px;
                text-align: left;
              }
              .logo {
                position: absolute;
                top: 20px;
                left: 20px;
              }
              .logo img {
                width: 32px;
                height: 32px;
              }
              code {
                background: #f4f4f4;
                padding: 2px 4px;
                border-radius: 4px;
                font-family: monospace;
              }
              pre code {
                display: block;
                padding: 10px;
                white-space: pre;
                overflow-x: auto;
              }
              ol {
                padding-left: 20px;
                margin: 10px 0;
              }
              li {
                margin: 5px 0;
              }
              @media print {
                body { padding: 20px; }
                pre code { 
                  white-space: pre-wrap;
                  word-wrap: break-word;
                }
              }
            </style>
          </head>
          <body>
            <div class="logo">
              <img src="/images/ravid.png" alt="RAVID" />
            </div>
            <div class="header">
              <h3>AI Agent Response</h3>
              <p>${formattedDate}</p>
            </div>
            <div class="content">${text}</div>
            <div class="footer">
              <h3>Disclaimer:</h3>
              <p>R.A.V.I.D. is not a doctor; please refer any analysis, advice, and/or assessment that you receive from our third party AI agent with a medical professional.</p>
              <p>Most importantly please note that if you ask or share your personally identifiable information with the AI agent then that information will be shared across multiple platforms and networks to help match your request for the most relevant responses.</p>
              <p>Remember only a healthcare professional can provide a diagnosis, analysis & an assessment tailored to your specific symptoms and medical history.</p>
            </div>
          </body>
        </html>
      `);
    iframeDoc.close();

    iframe.contentWindow?.focus();
    iframe.contentWindow?.print();
  };

  return (
    <>
      <button
        onClick={downloadAsPDF}
        className={`group-hover:opacity-100 border rounded-md p-1 hover:bg-gray-100 dark:hover:bg-gray-600 ${className || ""}`}
        title="Download as PDF"
        type="button"
      >
        <ArrowDownToLine width={16} height={16} className="transition-all hover:text-gray-700 dark:hover:text-gray-200 text-white-500 dark:text-gray-400" />
      </button>
      <iframe ref={iframeRef} style={{ display: "none" }} title="PDF Generator" />
    </>
  );
};

export default DownloadAsPdf;
