const WelcomeDescription = () => {
  return (
    <div className="p-4 rounded-lg space-y-4">
      <p>
        Welcome to <span className="boldSpan">R.A.V.I.D.</span>
      </p>

      <p className="description">
        We are on an exciting journey to empower every individual globally
        to save, store, collaborate and better understand their own medical
        and DNA / genomic data on the{" "}
        <span className="boldSpan">R.A.V.I.D.</span> platform.
      </p>

      <p className="description">
        Having the ability to store your various personalized medical data
        points to include Diagnosis, Prescriptions and also most importantly
        your own DNA / genomic data on a secure digital platform is your
        right.
      </p>

      <p className="description">
        Our value proposition is to provide every individual globally with
        secure personalized storage access to protect their valuable medical
        and DNA / genomic data with the necessary permission-based tools to
        analyze their own data and collaborate or share their data at their
        option as needed.
      </p>

      <p className="description">
        Additionally, on the R.A.V.I.D. platform we have taken steps to also
        empower the global healthcare community, to include doctors, nurses,
        researchers, scientists and medical students with a Public Profile
        builder. Our dynamic web-based tools are developed in a
        multi-language format to highlight important attributes such as
        credentials, services, academic and social media collateral with the
        objective to accelerate this critical communities interactivity with
        their respective interactivity with their respective stakeholders.
      </p>

      <p className="description">
        Finally and most importantly the R.A.V.I.D. platform is built on the
        founding principle of keeping a user / patient&apos;s perspective
        first and also to further advance the Patients Bill of Rights (PBR).
      </p>

      <p className="description">Sign Up for a R.A.V.I.D. account now.</p>
    </div>
  );
};

export default WelcomeDescription; 