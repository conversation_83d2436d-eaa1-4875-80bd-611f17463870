import { IconSvgProps } from "@/types";
import * as React from "react";

export const MoonIcon: React.FC<IconSvgProps> = (props) => {
  return (
    <svg aria-hidden="true" focusable="false" height="1em" role="presentation" viewBox="0 0 24 24" width="1em" {...props}>
      <path
        d="M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z"
        fill="currentColor"
      />
    </svg>
  );
};

export const Check: React.FC<IconSvgProps> = (props) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M15.6401 6.99226L14.5524 5.72857C14.3444 5.48863 14.1765 5.04074 14.1765 4.72082V3.36116C14.1765 2.51337 13.4807 1.81754 12.6329 1.81754H11.2732C10.9613 1.81754 10.5054 1.64959 10.2655 1.44164L9.00178 0.353911C8.44992 -0.11797 7.54614 -0.11797 6.98628 0.353911L5.73057 1.44964C5.49063 1.64959 5.03474 1.81754 4.72282 1.81754H3.33916C2.49138 1.81754 1.79555 2.51337 1.79555 3.36116V4.72882C1.79555 5.04074 1.62759 5.48863 1.42764 5.72857L0.347913 7.00025C-0.115971 7.55212 -0.115971 8.44789 0.347913 8.99975L1.42764 10.2714C1.62759 10.5114 1.79555 10.9593 1.79555 11.2712V12.6388C1.79555 13.4866 2.49138 14.1825 3.33916 14.1825H4.72282C5.03474 14.1825 5.49063 14.3504 5.73057 14.5584L6.99428 15.6461C7.54614 16.118 8.44992 16.118 9.00978 15.6461L10.2735 14.5584C10.5134 14.3504 10.9613 14.1825 11.2812 14.1825H12.6409C13.4887 14.1825 14.1845 13.4866 14.1845 12.6388V11.2792C14.1845 10.9673 14.3524 10.5114 14.5604 10.2714L15.6481 9.00775C16.112 8.45589 16.112 7.54412 15.6401 6.99226ZM11.3212 6.48838L7.45816 10.3514C7.34619 10.4634 7.19423 10.5274 7.03427 10.5274C6.87431 10.5274 6.72235 10.4634 6.61038 10.3514L4.67483 8.4159C4.44289 8.18396 4.44289 7.80005 4.67483 7.56811C4.90677 7.33617 5.29068 7.33617 5.52262 7.56811L7.03427 9.07973L10.4734 5.64059C10.7054 5.40865 11.0893 5.40865 11.3212 5.64059C11.5531 5.87253 11.5531 6.25643 11.3212 6.48838Z"
        fill="white"
      />
    </svg>
  );
};

export const SunIcon: React.FC<IconSvgProps> = (props) => {
  return (
    <svg aria-hidden="true" focusable="false" height="1em" role="presentation" viewBox="0 0 24 24" width="1em" {...props}>
      <g fill="currentColor">
        <path d="M19 12a7 7 0 11-7-7 7 7 0 017 7z" />
        <path d="M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z" />
      </g>
    </svg>
  );
};

export const Logo: React.FC<IconSvgProps> = ({ size = 66, width = 64, height = 85 }) => (
  <svg width={width || size} height={height || size} viewBox="0 0 95 55" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M55.3446 31.8113C58.1643 31.1363 60.2473 28.8119 60.2473 26.0426C60.2473 22.7516 57.3006 20.082 53.668 20.082H48.8838V23.396H52.9313C54.8534 23.396 56.4115 24.8075 56.4115 26.5489C56.4115 28.2902 54.2607 29.5637 52.347 29.5637H48.8923V32.6015H49.485C51.3987 32.6015 52.2116 33.5911 52.2116 33.5911L56.7332 39.03C56.7332 39.03 57.3175 38.6924 58.1981 38.1785C59.0872 37.6645 59.79 37.1428 59.79 37.1428L55.3446 31.8113Z"
      fill="currentColor"
    />
    <path
      d="M46.0389 14.5076C44.2184 14.546 41.5934 14.6917 40.3148 13.1345C39.5019 12.1679 38.7652 10.9865 38.9007 9.72078C37.8931 7.64188 35.8355 9.46763 34.3113 10.1504C30.4501 11.9454 23.7861 16.1109 19.4337 15.129L19.5184 15.1367C18.8156 15.0293 18.1297 14.6457 17.791 14.0627L17.7995 14.055C18.3668 14.0857 18.968 14.1164 19.5184 14.0167C24.4296 13.1191 28.782 10.5263 33.1343 8.31695C35.1242 7.25832 37.5459 5.66271 39.7221 7.31969C40.6196 7.97174 41.314 8.93832 41.2039 10.0353C41.2039 10.7411 41.7289 11.3778 42.1692 11.9608C43.0244 12.7202 44.9805 12.5668 46.022 12.5745V14.5076H46.0389ZM43.4732 15.4742C43.1938 16.7323 42.7027 17.9904 42.0845 19.1487V19.1564C43.9051 19.0643 44.7603 17.1158 45.0651 15.7197L43.4732 15.4742ZM41.3479 14.7608C40.8229 16.1723 40.0947 17.5608 39.0277 18.7115C38.6552 19.1104 38.3842 19.5016 37.91 19.8621C37.91 19.8621 38.8669 20.123 39.2987 20.0079C41.1023 19.5476 42.2708 16.5942 42.4995 15.2211L41.3479 14.7608ZM39.3495 13.9937C37.4697 16.2183 35.6238 18.6424 32.7871 19.8928V19.9005C34.0403 21.0435 35.8185 19.9465 36.7669 19.0413C38.2995 17.7219 39.5612 16.1569 40.5265 14.4539L39.3495 13.9937ZM36.5213 12.6972C33.8456 15.2441 30.7126 17.4687 26.9953 18.5504V18.558C28.4687 20.2841 31.2206 19.2715 32.8549 18.3202C35.0056 17.1312 36.6229 15.3361 38.2403 13.6254L36.5213 12.6972ZM34.379 10.9482C31.7795 12.4441 29.2646 14.2238 26.5126 15.4435C25.0139 16.0189 23.4558 16.3411 21.8893 16.7093V16.7169C23.0663 18.3739 25.5558 17.8446 27.2663 17.3C30.3231 16.0802 33.092 14.0704 35.6492 12.1756L34.379 10.9482ZM24.1925 10.7717C21.7454 10.7641 18.1213 10.7794 15.708 10.7641C17.139 14.2238 23.6675 11.2934 24.1925 10.7948V10.7717ZM48.9687 12.5745C50.0102 12.5591 51.9747 12.7202 52.8214 11.9608C53.2618 11.3778 53.7952 10.7411 53.7867 10.0353C53.6767 8.94599 54.371 7.97174 55.2686 7.31969C57.4447 5.66271 59.8665 7.25832 61.8564 8.31695C66.2087 10.5339 70.5695 13.1268 75.4722 14.0167C76.0226 14.1164 76.6238 14.078 77.1912 14.055L77.1996 14.0627C76.8609 14.6457 76.1835 15.0293 75.4722 15.1367L75.5569 15.129C71.2046 16.1109 64.5406 11.9378 60.6794 10.1427C59.1552 9.46763 57.0976 7.63421 56.0899 9.71311C56.2254 10.9789 55.4887 12.1602 54.6758 13.1268C53.3972 14.6917 50.7808 14.5383 48.9518 14.5V12.5745H48.9687ZM49.934 15.712C50.2388 17.1158 51.0856 19.0567 52.9146 19.1487V19.141C52.2964 17.9827 51.8138 16.7246 51.5259 15.4665L49.934 15.712ZM52.4997 15.2211C52.7283 16.5942 53.8968 19.5399 55.7004 20.0079C56.1238 20.1153 57.0891 19.8621 57.0891 19.8621C56.6149 19.5016 56.344 19.1104 55.9714 18.7115C54.9045 17.5684 54.1847 16.18 53.6513 14.7608L52.4997 15.2211ZM54.4726 14.4616C55.4379 16.1569 56.6996 17.7295 58.2322 19.049C59.1806 19.9465 60.9588 21.0512 62.212 19.9082V19.9005C59.3669 18.6501 57.521 16.2337 55.6496 14.0013L54.4726 14.4616ZM56.7589 13.6254C58.3762 15.3361 59.985 17.1312 62.1443 18.3202C63.7785 19.2715 66.5305 20.2764 68.0038 18.558V18.5504C64.2865 17.4687 61.1535 15.2441 58.4778 12.6972L56.7589 13.6254ZM59.3584 12.1756C61.9156 14.0704 64.693 16.0879 67.7413 17.3C69.4518 17.8446 71.9412 18.3739 73.1182 16.7169V16.7093C71.5517 16.3411 69.9937 16.0189 68.4949 15.4435C65.743 14.2238 63.2281 12.4441 60.6286 10.9482L59.3584 12.1756ZM70.8066 10.7948C71.3316 11.2934 77.8601 14.2238 79.2911 10.7641C76.8694 10.7794 73.2537 10.7641 70.8066 10.7717V10.7948Z"
      fill="currentColor"
    />
    <path
      d="M47.5041 9.72586C48.8088 9.72586 49.8665 8.76762 49.8665 7.58558C49.8665 6.40354 48.8088 5.44531 47.5041 5.44531C46.1993 5.44531 45.1416 6.40354 45.1416 7.58558C45.1416 8.76762 46.1993 9.72586 47.5041 9.72586Z"
      fill="currentColor"
    />
    <path
      d="M45.9632 35.3262C42.8471 36.8067 44.7185 37.8807 47.5636 38.7322C48.8422 39.1158 49.8752 39.5914 50.4002 40.3969C51.0438 41.3788 50.9252 42.7213 49.4773 43.3733C48.1309 42.9744 49.5365 42.2993 49.1301 41.5476C48.0039 39.4456 45.4806 40.581 43.2197 38.5481C41.8988 37.3591 43.3552 35.0117 45.1418 34.467C45.1503 34.4517 45.8193 35.2265 45.9632 35.3262Z"
      fill="currentColor"
    />
    <path
      d="M48.2405 45.8888C47.9103 43.6028 45.8103 43.7332 44.8027 43.1041C43.9813 42.5902 43.6596 41.708 43.7866 41.2247C44.0491 40.2198 44.1253 40.1507 45.3023 40.611C45.1922 40.9562 45.0652 41.8461 45.6325 42.2143C46.8773 43.0121 49.0534 43.3573 49.3159 45.0373C49.5361 46.188 48.3591 46.6943 48.2405 45.8888Z"
      fill="currentColor"
    />
    <path
      d="M49.4927 34.3438C51.2709 35.7246 53.0999 38.0259 50.5258 39.261C48.8238 38.3788 50.2633 38.8007 50.3564 37.2742C49.95 35.9163 48.0024 35.5635 49.4927 34.3438Z"
      fill="currentColor"
    />
    <path
      d="M47.0127 44.5625V49.1269C47.0127 49.3724 47.2329 49.5795 47.5123 49.5795C47.7832 49.5795 48.0119 49.38 48.0119 49.1269V46.0814C47.7917 45.5444 47.6139 45.0074 47.0127 44.5625Z"
      fill="currentColor"
    />
    <path
      d="M48.0034 40.7482C47.7409 40.6102 47.4615 40.4874 47.0127 40.334V42.5433C47.3091 42.6814 47.6816 42.8808 48.0034 43.1417V40.7482Z"
      fill="currentColor"
    />
    <path d="M48.0034 38.6402V11.2539H47.0127V38.1646C47.326 38.295 47.6986 38.4637 48.0034 38.6402Z" fill="currentColor" />
    <path
      d="M64.1174 6.48218C59.4941 2.90739 53.5922 0.935889 47.504 0.935889C41.4158 0.935889 35.5139 2.89972 30.8906 6.48218L30.2217 5.77643C35.0313 2.04822 41.1703 0 47.504 0C53.8378 0 59.9768 2.04822 64.7864 5.76876L64.1174 6.48218Z"
      fill="currentColor"
    />
    <path
      d="M43.2113 24.6178C43.4992 24.6178 43.7532 24.7713 43.8718 24.9937H46.1157V22.8841H45.1843C45.0657 23.1066 44.8117 23.2524 44.5238 23.2524C44.1173 23.2524 43.7956 22.9532 43.7956 22.5926C43.7956 22.2244 44.1258 21.9329 44.5238 21.9329C44.8032 21.9329 45.0572 22.0787 45.1843 22.3011H46.1157V20.1992C44.0242 20.2683 42.2206 21.3729 41.3146 22.9685L41.0267 23.5515C40.8319 24.0272 40.7134 24.5411 40.688 25.0704L39.6973 28.3844C39.6465 28.5455 39.7819 28.7143 39.9767 28.7143H41.3061L41.3569 30.563C41.3823 31.3072 42.0597 31.9055 42.8811 31.8978L44.4052 31.8825V33.1329H44.9641V30.3022H43.8125C43.694 30.5247 43.4399 30.6704 43.152 30.6704C42.7456 30.6704 42.4238 30.3713 42.4238 30.0107C42.4238 29.6425 42.7541 29.351 43.152 29.351C43.4315 29.351 43.6855 29.4967 43.8125 29.7192H44.9641V27.8704H42.0174C41.8988 28.0929 41.6448 28.2387 41.3569 28.2387C40.9505 28.2387 40.6287 27.9395 40.6287 27.5789C40.6287 27.2107 40.9589 26.9192 41.3569 26.9192C41.6363 26.9192 41.8904 27.065 42.0174 27.2874H45.3959C45.5145 27.2874 45.6076 27.3718 45.6076 27.4792V29.9187V33.3707V33.6545H46.1242V25.5998H43.8718C43.7532 25.8222 43.4992 25.968 43.2198 25.968C42.8133 25.968 42.4916 25.6688 42.4916 25.3083C42.4831 24.917 42.8133 24.6178 43.2113 24.6178Z"
      fill="currentColor"
    />
    <path
      d="M29.0024 35.6489C32.0761 39.6073 36.3522 42.3536 41.3651 43.5963C41.763 42.2692 41.0179 40.8807 39.6207 40.336C33.685 38.004 29.3072 32.9947 27.8254 26.9651L24.6924 25.5996C25.2428 29.2358 26.7246 32.7185 29.0024 35.6489Z"
      fill="currentColor"
    />
    <path
      d="M66.2011 35.6492C68.4958 32.6958 69.9861 29.1747 70.528 25.5078L67.4035 26.8733C65.9471 32.9412 61.5609 37.9966 55.5912 40.3363C54.1941 40.8809 53.4489 42.2694 53.8469 43.5966C58.8512 42.3538 63.1189 39.6075 66.2011 35.6492Z"
      fill="currentColor"
    />
    <path
      d="M42.5758 46.8859C30.899 44.8147 22.0419 35.5325 21.9064 24.3862L20.8818 23.9336C20.8818 23.995 20.8818 24.064 20.8818 24.1254C20.8818 30.5692 23.6507 36.6295 28.6805 41.1862C32.9058 45.0141 38.2997 47.3999 44.1338 48.0596L44.1254 48.0519C43.8629 47.4459 43.2786 47.0086 42.5758 46.8859Z"
      fill="currentColor"
    />
    <path
      d="M73.1024 24.3802C72.9669 35.5265 64.1098 44.8087 52.433 46.8799C51.7302 47.0026 51.146 47.4399 50.8835 48.0383L50.875 48.0459C56.7092 47.3785 62.103 45.0004 66.3283 41.1725C71.3581 36.6158 74.127 30.5555 74.127 24.1117C74.127 24.0503 74.127 23.9813 74.127 23.9199L73.1024 24.3802Z"
      fill="currentColor"
    />
    <path
      d="M5.16451 64.1882H3V53.5801H8.74578C9.9461 53.5801 10.8906 53.8621 11.5596 54.4419C12.2484 55.0217 12.5829 55.7895 12.5829 56.7766C12.5829 57.5288 12.3664 58.1712 11.9335 58.6726C11.5006 59.174 10.8513 59.5188 10.0051 59.7225L12.878 64.1882H10.4774L7.80127 59.9105H5.16451V64.1882ZM8.60804 58.4689C9.15901 58.4689 9.57223 58.3592 9.88707 58.1242C10.2019 57.9048 10.3397 57.5758 10.3397 57.1527V56.4319C10.3397 56.0088 10.1822 55.6798 9.88707 55.4604C9.57223 55.241 9.15901 55.1157 8.60804 55.1157H5.16451V58.4689H8.60804Z"
      fill="currentColor"
    />
    <path
      d="M17.5815 64.3757C17.1093 64.3757 16.7551 64.2816 16.5386 64.0779C16.3025 63.8742 16.2041 63.6392 16.2041 63.3415V63.1064C16.2041 62.8087 16.3222 62.5737 16.5386 62.37C16.7747 62.1663 17.1093 62.0723 17.5815 62.0723C18.0538 62.0723 18.408 62.1663 18.6244 62.37C18.8605 62.5737 18.9589 62.8087 18.9589 63.1064V63.3415C18.9589 63.6392 18.8409 63.8742 18.6244 64.0779C18.408 64.266 18.0538 64.3757 17.5815 64.3757Z"
      fill="currentColor"
    />
    <path
      d="M31.5902 64.1882L30.3899 61.3208H25.4116L24.2506 64.1882H22.0664L26.6119 53.5801H29.3273L33.8728 64.1882H31.5902ZM27.9499 55.1627H27.8515L25.9232 59.8478H29.8586L27.9499 55.1627Z"
      fill="currentColor"
    />
    <path
      d="M38.3012 64.3757C37.829 64.3757 37.4748 64.2816 37.2583 64.0779C37.0222 63.8742 36.9238 63.6392 36.9238 63.3415V63.1064C36.9238 62.8087 37.0419 62.5737 37.2583 62.37C37.4945 62.1663 37.829 62.0723 38.3012 62.0723C38.7735 62.0723 39.1277 62.1663 39.3441 62.37C39.5803 62.5737 39.6787 62.8087 39.6787 63.1064V63.3415C39.6787 63.6392 39.5606 63.8742 39.3441 64.0779C39.1277 64.266 38.7735 64.3757 38.3012 64.3757Z"
      fill="currentColor"
    />
    <path
      d="M45.5822 64.1882L41.2139 53.5801H43.4374L45.5626 58.8137L46.8416 62.5116H46.9203L48.219 58.8137L50.3442 53.5801H52.5283L48.1009 64.1882H45.5822Z"
      fill="currentColor"
    />
    <path
      d="M55.4389 64.3757C54.9667 64.3757 54.6125 64.2816 54.396 64.0779C54.1599 63.8742 54.0615 63.6392 54.0615 63.3415V63.1064C54.0615 62.8087 54.1796 62.5737 54.396 62.37C54.6322 62.1663 54.9667 62.0723 55.4389 62.0723C55.9112 62.0723 56.2654 62.1663 56.4818 62.37C56.718 62.5737 56.8164 62.8087 56.8164 63.1064V63.3415C56.8164 63.6392 56.6983 63.8742 56.4818 64.0779C56.2457 64.266 55.8915 64.3757 55.4389 64.3757Z"
      fill="currentColor"
    />
    <path d="M60.2207 64.1882V62.7936H62.031V54.9903H60.2207V53.5957H66.0255V54.9903H64.1955V62.7936H66.0255V64.1882H60.2207Z" fill="currentColor" />
    <path
      d="M70.8091 64.3757C70.3368 64.3757 69.9826 64.2816 69.7662 64.0779C69.53 63.8742 69.4316 63.6392 69.4316 63.3415V63.1064C69.4316 62.8087 69.5497 62.5737 69.7662 62.37C70.0023 62.1663 70.3368 62.0723 70.8091 62.0723C71.2813 62.0723 71.6355 62.1663 71.852 62.37C72.0881 62.5737 72.1865 62.8087 72.1865 63.1064V63.3415C72.1865 63.6392 72.0684 63.8742 71.852 64.0779C71.6158 64.266 71.2813 64.3757 70.8091 64.3757Z"
      fill="currentColor"
    />
    <path
      d="M76.1602 53.5801H80.863C81.7092 53.5801 82.4963 53.6898 83.185 53.9091C83.8737 54.1285 84.464 54.4576 84.9559 54.8963C85.4479 55.3351 85.8217 55.8835 86.0972 56.5573C86.3727 57.2154 86.4908 57.9988 86.4908 58.8763C86.4908 59.7538 86.353 60.5373 86.0972 61.1954C85.8217 61.8535 85.4479 62.4176 84.9559 62.8564C84.464 63.2951 83.8737 63.6242 83.185 63.8435C82.4963 64.0629 81.7289 64.1726 80.863 64.1726H76.1602V53.5801ZM80.8827 62.6683C81.8666 62.6683 82.6734 62.4176 83.2834 61.9319C83.8934 61.4304 84.2082 60.7096 84.2082 59.7381V58.0302C84.2082 57.0744 83.8934 56.3379 83.2834 55.8365C82.6734 55.3351 81.8666 55.0843 80.8827 55.0843H78.3247V62.6527H80.8827V62.6683Z"
      fill="currentColor"
    />
    <path
      d="M90.6225 64.3757C90.1503 64.3757 89.7961 64.2816 89.5796 64.0779C89.3435 63.8742 89.2451 63.6392 89.2451 63.3415V63.1064C89.2451 62.8087 89.3632 62.5737 89.5796 62.37C89.8158 62.1663 90.1503 62.0723 90.6225 62.0723C91.0948 62.0723 91.449 62.1663 91.6654 62.37C91.9016 62.5737 91.9999 62.8087 91.9999 63.1064V63.3415C91.9999 63.6392 91.8819 63.8742 91.6654 64.0779C91.449 64.266 91.0948 64.3757 90.6225 64.3757Z"
      fill="currentColor"
    />
  </svg>
);

export const TwitterIcon: React.FC<IconSvgProps> = ({ size = 24, width, height, ...props }) => {
  return (
    <svg height={size || height} viewBox="0 0 24 24" width={size || width} {...props}>
      <path
        d="M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z"
        fill="currentColor"
      />
    </svg>
  );
};

export const GithubIcon: React.FC<IconSvgProps> = ({ size = 24, width, height, ...props }) => {
  return (
    <svg height={size || height} viewBox="0 0 24 24" width={size || width} {...props}>
      <path
        clipRule="evenodd"
        d="M12.026 2c-5.509 0-9.974 4.465-9.974 9.974 0 4.406 2.857 8.145 6.821 9.465.499.09.679-.217.679-.481 0-.237-.008-.865-.011-1.696-2.775.602-3.361-1.338-3.361-1.338-.452-1.152-1.107-1.459-1.107-1.459-.905-.619.069-.605.069-.605 1.002.07 1.527 1.028 1.527 1.028.89 1.524 2.336 1.084 2.902.829.091-.645.351-1.085.635-1.334-2.214-.251-4.542-1.107-4.542-4.93 0-1.087.389-1.979 1.024-2.675-.101-.253-.446-1.268.099-2.64 0 0 .837-.269 2.742 1.021a9.582 9.582 0 0 1 2.496-.336 9.554 9.554 0 0 1 2.496.336c1.906-1.291 2.742-1.021 2.742-1.021.545 1.372.203 2.387.099 2.64.64.696 1.024 1.587 1.024 2.675 0 3.833-2.33 4.675-4.552 4.922.355.308.675.916.675 1.846 0 1.334-.012 2.41-.012 2.737 0 .267.178.577.687.479C19.146 20.115 22 16.379 22 11.974 22 6.465 17.535 2 12.026 2z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  );
};

export const HeartFilledIcon = ({ size = 24, width, height, ...props }: IconSvgProps) => (
  <svg aria-hidden="true" focusable="false" height={size || height} role="presentation" viewBox="0 0 24 24" width={size || width} {...props}>
    <path
      d="M12.62 20.81c-.34.12-.9.12-1.24 0C8.48 19.82 2 15.69 2 8.69 2 5.6 4.49 3.1 7.56 3.1c1.82 0 3.43.88 4.44 2.24a5.53 5.53 0 0 1 4.44-2.24C19.51 3.1 22 5.6 22 8.69c0 7-6.48 11.13-9.38 12.12Z"
      fill="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
    />
  </svg>
);

export const SearchIcon = (props: IconSvgProps) => (
  <svg aria-hidden="true" fill="none" focusable="false" height="1em" role="presentation" viewBox="0 0 24 24" width="1em" {...props}>
    <path
      d="M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
    />
    <path d="M22 22L20 20" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" />
  </svg>
);

export const GoogleIcon = (props: IconSvgProps) => (
  <svg width="800px" height="800px" viewBox="-0.5 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" {...props}>
    <title>Google-color</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="Icons" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="Color-" transform="translate(-401.000000, -860.000000)">
        <g id="Google" transform="translate(401.000000, 860.000000)">
          <path
            d="M9.82727273,24 C9.82727273,22.4757333 10.0804318,21.0144 10.5322727,19.6437333 L2.62345455,13.6042667 C1.08206818,16.7338667 0.213636364,20.2602667 0.213636364,24 C0.213636364,27.7365333 1.081,31.2608 2.62025,34.3882667 L10.5247955,28.3370667 C10.0772273,26.9728 9.82727273,25.5168 9.82727273,24"
            id="Fill-1"
            fill="#FBBC05"
          ></path>
          <path
            d="M23.7136364,10.1333333 C27.025,10.1333333 30.0159091,11.3066667 32.3659091,13.2266667 L39.2022727,6.4 C35.0363636,2.77333333 29.6954545,0.533333333 23.7136364,0.533333333 C14.4268636,0.533333333 6.44540909,5.84426667 2.62345455,13.6042667 L10.5322727,19.6437333 C12.3545909,14.112 17.5491591,10.1333333 23.7136364,10.1333333"
            id="Fill-2"
            fill="#EB4335"
          ></path>
          <path
            d="M23.7136364,37.8666667 C17.5491591,37.8666667 12.3545909,33.888 10.5322727,28.3562667 L2.62345455,34.3946667 C6.44540909,42.1557333 14.4268636,47.4666667 23.7136364,47.4666667 C29.4455,47.4666667 34.9177955,45.4314667 39.0249545,41.6181333 L31.5177727,35.8144 C29.3995682,37.1488 26.7323182,37.8666667 23.7136364,37.8666667"
            id="Fill-3"
            fill="#34A853"
          ></path>
          <path
            d="M46.1454545,24 C46.1454545,22.6133333 45.9318182,21.12 45.6113636,19.7333333 L23.7136364,19.7333333 L23.7136364,28.8 L36.3181818,28.8 C35.6879545,31.8912 33.9724545,34.2677333 31.5177727,35.8144 L39.0249545,41.6181333 C43.3393409,37.6138667 46.1454545,31.6490667 46.1454545,24"
            id="Fill-4"
            fill="#4285F4"
          ></path>
        </g>
      </g>
    </g>
  </svg>
);

export const AppleIcon = (props: IconSvgProps) => (
  <svg width="800px" height="800px" viewBox="-1.5 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" {...props}>
    <title>apple [#173]</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="Dribbble-Light-Preview" transform="translate(-102.000000, -7439.000000)" fill="#000000">
        <g id="icons" transform="translate(56.000000, 160.000000)">
          <path
            d="M57.5708873,7282.19296 C58.2999598,7281.34797 58.7914012,7280.17098 58.6569121,7279 C57.6062792,7279.04 56.3352055,7279.67099 55.5818643,7280.51498 C54.905374,7281.26397 54.3148354,7282.46095 54.4735932,7283.60894 C55.6455696,7283.69593 56.8418148,7283.03894 57.5708873,7282.19296 M60.1989864,7289.62485 C60.2283111,7292.65181 62.9696641,7293.65879 63,7293.67179 C62.9777537,7293.74279 62.562152,7295.10677 61.5560117,7296.51675 C60.6853718,7297.73474 59.7823735,7298.94772 58.3596204,7298.97372 C56.9621472,7298.99872 56.5121648,7298.17973 54.9134635,7298.17973 C53.3157735,7298.17973 52.8162425,7298.94772 51.4935978,7298.99872 C50.1203933,7299.04772 49.0738052,7297.68074 48.197098,7296.46676 C46.4032359,7293.98379 45.0330649,7289.44985 46.8734421,7286.3899 C47.7875635,7284.87092 49.4206455,7283.90793 51.1942837,7283.88393 C52.5422083,7283.85893 53.8153044,7284.75292 54.6394294,7284.75292 C55.4635543,7284.75292 57.0106846,7283.67793 58.6366882,7283.83593 C59.3172232,7283.86293 61.2283842,7284.09893 62.4549652,7285.8199 C62.355868,7285.8789 60.1747177,7287.09489 60.1989864,7289.62485"
            id="apple-[#173]"
            fill="currentColor"
          ></path>
        </g>
      </g>
    </g>
  </svg>
);

export const AddIcon = (props: IconSvgProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
);
export const RemoveIcon = (props: IconSvgProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-5" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
  </svg>
);

export const EditAvatarIcon = (props: IconSvgProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    // className="text-white h-6 w-6"
    className="size-6"
    {...props}
  >
    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7" />
    <line x1="16" y1="5" x2="22" y2="5" />
    <line x1="19" y1="2" x2="19" y2="8" />
    <circle cx="9" cy="9" r="2" />
    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
  </svg>
);

export const DeepIcon = (props: IconSvgProps) => (
  <svg width="10" height="13" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.94359 3.32398C9.02089 3.18599 8.97169 3.01147 8.83371 2.93417C7.0735 1.94813 5.57398 1.75436 4.40503 2.06711C3.23477 2.38021 2.43353 3.19201 2.0665 4.13541C1.70112 5.07459 1.7624 6.15622 2.33011 7.00677C2.90357 7.86594 3.96201 8.44362 5.49727 8.43304C5.65543 8.43195 5.78275 8.30285 5.78166 8.14469C5.78057 7.98654 5.65148 7.85921 5.49332 7.8603C4.11066 7.86983 3.25238 7.35684 2.8065 6.6888C2.35485 6.01213 2.29414 5.12999 2.60029 4.34308C2.90479 3.5604 3.5686 2.88379 4.55307 2.6204C5.53885 2.35666 6.88374 2.49833 8.55379 3.43387C8.69177 3.51116 8.86629 3.46197 8.94359 3.32398Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.665783 9.91772C0.588485 10.0557 0.637683 10.2302 0.775669 10.3075C2.53588 11.2936 4.03539 11.4873 5.20434 11.1746C6.3746 10.8615 7.17584 10.0497 7.54287 9.10629C7.90825 8.16711 7.84697 7.08548 7.27927 6.23493C6.7058 5.37576 5.64736 4.79808 4.11211 4.80866C3.95395 4.80975 3.82662 4.93885 3.82771 5.09701C3.8288 5.25516 3.9579 5.38249 4.11605 5.3814C5.49872 5.37187 6.35699 5.88486 6.80288 6.5529C7.25453 7.22957 7.31524 8.11171 7.00909 8.89862C6.70459 9.6813 6.04078 10.3579 5.05631 10.6213C4.07053 10.885 2.72563 10.7434 1.05559 9.80783C0.917604 9.73054 0.743082 9.77973 0.665783 9.91772Z"
      fill="currentColor"
    />
  </svg>
);

export const RsIcon = (props: IconSvgProps) => (
  <svg width="17" height="10" viewBox="0 0 17 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="16.7787" height="10" rx="4" fill="#FAEBB3" fillOpacity="0.16" />
    <path
      d="M7.303 8L5.809 5.54663H4.819V8H4V2H6.025C6.499 2 6.898 2.07748 7.222 2.23242C7.552 2.38737 7.798 2.59684 7.96 2.86083C8.122 3.12482 8.203 3.42611 8.203 3.76471C8.203 4.17791 8.077 4.54232 7.825 4.85796C7.579 5.1736 7.207 5.38307 6.709 5.48637L8.284 8H7.303ZM4.819 4.91822H6.025C6.469 4.91822 6.802 4.81492 7.024 4.60832C7.246 4.39598 7.357 4.11478 7.357 3.76471C7.357 3.4089 7.246 3.13343 7.024 2.93831C6.808 2.74319 6.475 2.64562 6.025 2.64562H4.819V4.91822Z"
      fill="currentColor"
    />
    <path
      d="M10.6587 8C10.2135 8 9.81421 7.9258 9.46086 7.77739C9.1075 7.62191 8.82835 7.40989 8.6234 7.14134C8.41846 6.86572 8.30538 6.55124 8.28418 6.19788H9.28065C9.30891 6.48763 9.44319 6.72438 9.68347 6.90813C9.93082 7.09187 10.2524 7.18375 10.6481 7.18375C11.0156 7.18375 11.3054 7.10247 11.5174 6.93993C11.7294 6.77739 11.8354 6.57244 11.8354 6.32509C11.8354 6.07067 11.7223 5.88339 11.4962 5.76325C11.27 5.63604 10.9202 5.51237 10.4467 5.39223C10.0156 5.27915 9.66227 5.16608 9.38665 5.053C9.1181 4.93286 8.88489 4.75972 8.68701 4.53357C8.49619 4.30035 8.40079 3.99647 8.40079 3.62191C8.40079 3.32509 8.48913 3.053 8.66581 2.80565C8.84248 2.5583 9.09337 2.36396 9.41846 2.22262C9.74354 2.07421 10.1146 2 10.5315 2C11.1746 2 11.6941 2.16254 12.0898 2.48763C12.4856 2.81272 12.6976 3.25795 12.7259 3.82332H11.7612C11.74 3.51944 11.6163 3.27562 11.3902 3.09187C11.1711 2.90813 10.8743 2.81625 10.4997 2.81625C10.1534 2.81625 9.87782 2.89046 9.67287 3.03887C9.46793 3.18728 9.36545 3.38163 9.36545 3.62191C9.36545 3.81272 9.42552 3.97173 9.54566 4.09894C9.67287 4.21908 9.82835 4.31802 10.0121 4.39576C10.2029 4.46643 10.4644 4.5477 10.7965 4.63958C11.2135 4.75265 11.5527 4.86572 11.8142 4.9788C12.0757 5.08481 12.2983 5.24735 12.4821 5.46643C12.6729 5.68551 12.7718 5.97173 12.7789 6.32509C12.7789 6.64311 12.6905 6.92933 12.5139 7.18375C12.3372 7.43816 12.0863 7.63958 11.7612 7.78799C11.4432 7.92933 11.0757 8 10.6587 8Z"
      fill="currentColor"
    />
    <defs>
      <linearGradient id="paint0_linear_3443_15184" x1="6.142" y1="2" x2="6.142" y2="8" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFF8DD" />
        <stop offset="1" stopColor="#C7B467" />
      </linearGradient>
      <linearGradient id="paint1_linear_3443_15184" x1="10.5315" y1="2" x2="10.5315" y2="8" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFF8DD" />
        <stop offset="1" stopColor="#C7B467" />
      </linearGradient>
    </defs>
  </svg>
);

export const AnalysisIcon = (props: IconSvgProps) => (
  <svg className="w-4 h-4" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M8.69544 4.49728C7.65841 3.45987 6.94861 1.6483 6.59636 0C6.24356 1.64864 5.53451 3.46059 4.49711 4.49799C3.46008 5.53468 1.64796 6.24448 0 6.59745C1.64864 6.9497 3.46042 7.65908 4.49745 8.69653C5.53447 9.73355 6.24427 11.5455 6.59707 13.1938C6.94932 11.5452 7.65892 9.73322 8.69577 8.69581C9.7328 7.65879 11.5449 6.94932 13.1929 6.59636C11.5446 6.2441 9.73284 5.53434 8.69544 4.49728Z"
      fill="url(#paint0_linear_1022_31082)"
    />
    <path
      d="M15.4311 12.8039C14.8121 12.1846 14.3873 11.1018 14.1769 10.1172C13.9661 11.1018 13.5428 12.1846 12.9231 12.8043C12.3034 13.4233 11.2208 13.8477 10.2363 14.0585C11.2211 14.2692 12.3034 14.6929 12.9231 15.3126C13.5428 15.932 13.9668 17.0148 14.1776 17.9994C14.388 17.0144 14.8121 15.932 15.4314 15.3123C16.0507 14.6929 17.1335 14.2689 18.1178 14.0578C17.1336 13.8473 16.0508 13.4233 15.4311 12.8039Z"
      fill="url(#paint1_linear_1022_31082)"
    />
    <defs>
      <linearGradient id="paint0_linear_1022_31082" x1="6.59644" y1="0" x2="6.59644" y2="13.1938" gradientUnits="userSpaceOnUse">
        <stop stopColor="#7D5191" />
        <stop offset="1" stopColor="#FFD52B" />
      </linearGradient>
      <linearGradient id="paint1_linear_1022_31082" x1="14.1771" y1="10.1172" x2="14.1771" y2="17.9994" gradientUnits="userSpaceOnUse">
        <stop stopColor="#7D5191" />
        <stop offset="1" stopColor="#FFD52B" />
      </linearGradient>
    </defs>
  </svg>
);
