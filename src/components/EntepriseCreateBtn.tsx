"use client";

import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import SubmitButton from "./SubmitButton";
import { useCreateEnterprise } from "@/hooks/useEnterpriseSolution";
import { ArrowRightIcon } from "lucide-react";

export const EnterpriseCreateBtn = () => {
  const { mutate: createEnterprise } = useCreateEnterprise();
  const router = useRouter();

  const handleCreateEnterprise = async () => {
    if (!localStorage.getItem("rrT")) {
      toast.error("Please log in to create an enterprise solution");
      router.push("/onboarding");
      return;
    }
    createEnterprise();
  };

  return (
    <SubmitButton
      label="Create Enterprise Solution"
      isLoadingValue="Creating..."
      size="sm"
      onClick={handleCreateEnterprise}
      className="bg-blue-600 hover:bg-blue-700 text-white text-xs"
      icon={<ArrowRightIcon className="w-4 h-4" />}
    />
  );
};
