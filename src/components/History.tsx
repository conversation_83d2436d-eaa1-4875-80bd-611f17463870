import { useChatHistory, useDeleteChat } from "@/hooks/home-dashboard/useAI";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { MessageCircle, Trash2, X } from "lucide-react";
import React from "react";

interface ChatHistoryItem {
  id: string;
  title: string;
  model: string;
  summary: string;
  timestamp: string;
}

interface HistoryProps {
  isOpen: boolean;
  onClose: () => void;
  currentChatId: string | null;
  onSelectChat: (chatId: string, model: string) => void;
}

const History: React.FC<HistoryProps> = ({ isOpen, onClose, currentChatId, onSelectChat }) => {
  const { data: chatHistory, isLoading, refetch } = useChatHistory(isOpen);
  const { mutate: deleteChat } = useDeleteChat();

  return (
    <div
      className={`absolute left-0 top-0 h-full w-64 transition-transform duration-300 ease-in-out transform ${
        isOpen ? "translate-x-0" : "-translate-x-full"
      } z-50`}
    >
      <Card className="h-full border-r dark:border-gray-800 bg-white dark:bg-slate-950 overflow-hidden">
        <div className="flex flex-col h-full">
          <div className="px-3 py-2 border-b dark:border-gray-800 flex justify-between items-center">
            <h2 className="text-sm font-medium">Chat History</h2>
            <Button size="sm" variant="light" isIconOnly onPress={onClose} className="hover:bg-gray-100 dark:hover:bg-slate-800 rounded-full">
              <X className="w-3.5 h-3.5" />
            </Button>
          </div>

          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <h2 className="text-center text-gray-500 dark:text-gray-400 text-sm py-2">Loading...</h2>
            ) : !chatHistory || chatHistory.length === 0 ? (
              <h2 className="text-center text-gray-500 dark:text-gray-400 text-sm py-2">No history</h2>
            ) : (
              <div className="space-y-0.5 p-1">
                {(chatHistory as ChatHistoryItem[]).map((chat) => (
                  <div
                    key={chat.id}
                    className={`group flex items-center justify-between px-2 py-1.5 rounded-md cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-900 ${
                      currentChatId === chat.id ? "bg-gray-100 dark:bg-slate-900" : ""
                    }`}
                  >
                    <div className="flex items-center space-x-2 min-w-0 flex-1" onClick={() => onSelectChat(chat.id, chat.model)}>
                      <MessageCircle className="w-3.5 h-3.5 flex-shrink-0 text-gray-500" />
                      <div className="min-w-0 flex-1">
                        <div className="text-xs font-medium truncate">{chat.title}</div>
                        <div className="text-[10px] text-gray-500 truncate">
                          {chat.summary} • {chat.timestamp}
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="light"
                      isIconOnly
                      className="opacity-0 group-hover:opacity-100 hover:bg-gray-200 dark:hover:bg-slate-800 rounded-full"
                      onPress={() => deleteChat({ chat_ids: [chat.id] })}
                    >
                      <Trash2 className="w-3.5 h-3.5 text-red-500" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default History;
