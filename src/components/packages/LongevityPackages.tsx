import React, { useState, useEffect } from "react";
import Image from "next/image";
import WomensWellnessDetails from "../packages/WomensWellnessDetails";
import MenWellnessDetails from "../packages/MenWellnessPackage";
import AdvancedWomensWellnessDetails from "../packages/AdvancedWomensWellnessDetails";
import AdvancedMensWellnessDetails from "../packages/AdvancedMensWellnessDetails";
import { Button } from "@heroui/react";
import { Card } from "@heroui/react";

interface LongevityPackagesProps {
  onChoosePackage: () => void;
  onSelectPackage: (packageType: string) => void;
  showPackageDetails: boolean;
}

const LongevityPackages = ({ onChoosePackage, onSelectPackage, showPackageDetails }: LongevityPackagesProps) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);

  useEffect(() => {
    if (!showPackageDetails) {
      setSelectedPackage(null);
    }
  }, [showPackageDetails]);

  const handleViewDetails = (packageType: string) => {
    if (
      packageType === "Essential Wellness Package for Women" ||
      packageType === "Essential Wellness Package for Men" ||
      packageType === "Advanced Wellness Package for Women" ||
      packageType === "Advanced Wellness Package for Men"
    ) {
      setSelectedPackage(packageType);
      onSelectPackage(packageType);
    } else {
      onChoosePackage();
    }
  };

  if (showPackageDetails) {
    if (selectedPackage === "Essential Wellness Package for Women") {
      return (
        <div className="space-y-4">
          <WomensWellnessDetails />
        </div>
      );
    }
    if (selectedPackage === "Essential Wellness Package for Men") {
      return (
        <div className="space-y-4">
          <MenWellnessDetails />
        </div>
      );
    }
    if (selectedPackage === "Advanced Wellness Package for Women") {
      return (
        <div className="space-y-4">
          <AdvancedWomensWellnessDetails />
        </div>
      );
    }
    if (selectedPackage === "Advanced Wellness Package for Men") {
      return (
        <div className="space-y-4">
          <AdvancedMensWellnessDetails />
        </div>
      );
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Essential Wellness Package for Women */}
      <Card className="overflow-hidden">
        <div className="relative h-48">
          <Image src="/images/l&w-1.png" alt="Women's Wellness" fill className="object-cover" />
        </div>
        <div className="p-3 space-y-3">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Essential Wellness Package for Women</h3>
          <p className="text-xs text-gray-600 dark:text-gray-300 text-justify">
            Our Women's Wellness Package offers personalized care with yoga, Ayurveda, and relaxation therapies. Focused on hormonal balance, stress relief, and
            vitality, it includes tailored nutrition plans, herbal remedies, and rejuvenating massages. Empower your health and embrace well-being through this
            holistic journey designed just for women.
          </p>
          <div className="space-y-1 mt-auto">
            <Button
              size="sm"
              variant="light"
              className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
              onClick={() => handleViewDetails("Essential Wellness Package for Women")}
            >
              View Full Package Details
            </Button>
            <Button
              size="sm"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              // onClick={handleBuyPackage}
            >
              Buy Package Now
            </Button>
          </div>
        </div>
      </Card>

      {/* Essential Wellness Package for Men */}
      <Card className="overflow-hidden">
        <div className="relative h-48">
          <Image src="/images/l&w-2.png" alt="Men's Wellness" fill className="object-cover" />
        </div>
        <div className="p-3 space-y-3">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Essential Wellness Package for Men</h3>
          <p className="text-xs text-gray-600 dark:text-gray-300 text-justify">
            Our Men's Wellness Package provides personalized care with fitness, Ayurveda, and relaxation therapies. Focused on boosting energy, reducing stress,
            and enhancing vitality, it includes customized nutrition plans, herbal remedies, and invigorating massages. Take control of your health and embrace
            well-being through this holistic journey designed specifically for men.
          </p>
          <div className="space-y-1 mt-auto">
            <Button
              size="sm"
              variant="light"
              className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
              onClick={() => handleViewDetails("Essential Wellness Package for Men")}
            >
              View Full Package Details
            </Button>
            <Button
              size="sm"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              // onClick={handleBuyPackage}
            >
              Buy Package Now
            </Button>
          </div>
        </div>
      </Card>

      {/* Advanced Wellness Package for Women */}
      <Card className="overflow-hidden">
        <div className="relative h-48">
          <Image src="/images/oldwoman.png" alt="Advanced Women's Wellness" fill className="object-cover" />
        </div>
        <div className="p-3 space-y-3">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Advanced Wellness Package for Women</h3>
          <p className="text-xs text-gray-600 dark:text-gray-300 text-justify">
            The Women's Wellness Package offers a thorough health evaluation, including tests for diabetes, thyroid, liver, renal, and pancreatic functions. It
            covers imaging (USG, X-Ray, mammography), cardiac assessments (ECG, 2D Echo, TMT), vitamin levels (B12, D3), and screenings for skeletal health,
            lipids, lungs, and viral infections, ensuring comprehensive care.
          </p>
          <div className="space-y-1 mt-auto">
            <Button
              size="sm"
              variant="light"
              className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
              onClick={() => handleViewDetails("Advanced Wellness Package for Women")}
            >
              View Full Package Details
            </Button>
            <Button
              size="sm"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              // onClick={handleBuyPackage}
            >
              Buy Package Now
            </Button>
          </div>
        </div>
      </Card>

      {/* Advanced Wellness Package for Men */}
      <Card className="overflow-hidden">
        <div className="relative h-48">
          <Image src="/images/oldman.png" alt="Advanced Men's Wellness" fill className="object-cover" />
        </div>
        <div className="p-3 space-y-3">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Advanced Wellness Package for Men</h3>
          <p className="text-xs text-gray-600 dark:text-gray-300 text-justify">
            The Men's Wellness Package provides a comprehensive health assessment, including tests for diabetes, thyroid, liver, renal, and pancreatic
            functions. It features prostate cancer screening (PSA), imaging (USG, X-Ray), cardiac evaluations (ECG, 2D Echo, TMT), skeletal health (BMD), and
            vitamin levels (B12, D3). Additional tests include lipid and lung function profiles and viral screenings for holistic health monitoring.
          </p>
          <div className="space-y-1 mt-auto">
            <Button
              size="sm"
              variant="light"
              className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
              onClick={() => handleViewDetails("Advanced Wellness Package for Men")}
            >
              View Full Package Details
            </Button>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white">
              Buy Package Now
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default LongevityPackages;
