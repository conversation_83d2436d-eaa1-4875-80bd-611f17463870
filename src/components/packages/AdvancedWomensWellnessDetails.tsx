import React from "react";
import { But<PERSON> } from "@heroui/react";
import Image from "next/image";

const AdvancedWomensWellnessDetails = () => {
  return (
    <div className="space-y-6">
      <div className="relative w-full h-[200px] rounded-lg overflow-hidden">
        <Image src="/images/oldwoman.png" alt="Advanced Women's Wellness" fill className="object-cover" />
        <div className="absolute bottom-6 left-6 h-fit flex flex-col gap-4 items-center justify-start w-[200px]">
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white w-fit">
            Buy Package Now
          </Button>
        </div>
      </div>

      <div className="text-gray-900 dark:text-white">
        <p className="mb-6 text-xs text-justify">
          The Women's Wellness Package offers a thorough health evaluation, including tests for diabetes, thyroid, liver, renal, and pancreatic functions. It
          covers imaging (USG, X-Ray, mammography), cardiac assessments (ECG, 2D Echo, TMT), vitamin levels (B12, D3), and screenings for skeletal health,
          lipids, lungs, and viral infections, ensuring comprehensive care.
        </p>

        <div className="space-y-8">
          <div>
            <h3 className="text-xs font-semibold mb-3">Package Coverage and Tests:</h3>
            <div className="w-full h-[1px] bg-gray-200 dark:bg-gray-700 my-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h4 className="font-medium mb-2">General Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Blood Picture ESR</li>
                  <li>Blood Grouping & RH Typing</li>
                  <li>Complete Urine Examination</li>
                  <li>Uric Acid</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Diabetes</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>HbA1c</li>
                  <li>FBS</li>
                  <li>PPBS</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Liver Function Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Total Bilirubin</li>
                  <li>Direct Bilirubin</li>
                  <li>Indirect Bilirubin</li>
                  <li>SGPT (ALT)</li>
                  <li>SGOT (AST)</li>
                  <li>ALP</li>
                  <li>Total Proteins Albumin(Serum)</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Renal Function Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Blood Urea</li>
                  <li>Creatinine</li>
                  <li>Sodium</li>
                  <li>Potassium</li>
                  <li>Chloride</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Thyroid Function Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>T3</li>
                  <li>T4</li>
                  <li>TSH</li>
                  <li>SGPT (ALT)</li>
                  <li>SGOT (AST)</li>
                  <li>ALP</li>
                  <li>Total Proteins Albumin(Serum)</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Viral Screening</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>HBsAg</li>
                  <li>HIV</li>
                  <li>Anti HCV</li>
                  <li>Vitamins</li>
                  <li>B12</li>
                  <li>D3(25-OH)</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Imaging</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>USG Abdomen + Pelvis</li>
                  <li>Chest X Ray</li>
                  <li>Lipid Profile</li>
                  <li>Total Cholesterol</li>
                  <li>HDL Cholesterol</li>
                  <li>VLDL Cholesterol</li>
                  <li>Triglycerides</li>
                  <li>Total Cholesterol /HDL Ratio</li>
                  <li>TG/HDL</li>
                  <li>Direct LDL Cholesterol</li>
                </ul>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Cardiac Evaluation</h4>
                  <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                    <li>ECG</li>
                    <li>2D Echo</li>
                    <li>TMT</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Pancreatic Functions</h4>
                  <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                    <li>Serum Lipase</li>
                    <li>Calcium</li>
                  </ul>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Renal Function Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Blood Urea</li>
                  <li>Creatinine</li>
                  <li>Sodium</li>
                  <li>Potassium</li>
                  <li>Chloride</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Lung Function Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>PFT</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Skeletal Health</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>BMD</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Cardiac Evaluation</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>ECG</li>
                  <li>2D Echo</li>
                  <li>TMT</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center space-x-4 pt-6">
        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default AdvancedWomensWellnessDetails;
