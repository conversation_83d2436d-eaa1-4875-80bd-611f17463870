import React from "react";
import { But<PERSON> } from "@heroui/react";
import Image from "next/image";

const WomensWellnessDetails = () => {
  return (
    <div className="space-y-6">
      <div className="relative w-full h-[200px] rounded-lg overflow-hidden">
        <Image src="/images/l&w-women.png" alt="Women's Wellness" fill className="object-cover" />
        <div className="absolute bottom-6 left-6 h-fit flex flex-col gap-4 items-center justify-start w-[200px]">
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white w-fit">
            Buy Package Now
          </Button>
        </div>
      </div>

      <div className="text-gray-900 dark:text-white">
        <p className="mb-6 text-xs text-justify">
          Our Women's Wellness Package offers personalized care with yoga, Ayurveda, and relaxation therapies. Focused on hormonal balance, stress relief, and
          vitality, it includes tailored nutrition plans, herbal remedies, and rejuvenating massages. Empower your health and embrace well-being through this
          holistic journey designed just for women.
        </p>

        <div className="space-y-8">
          <div>
            <h3 className="text-xs font-semibold mb-3">Package Coverage and Tests:</h3>
            <div className="w-full h-[1px] bg-gray-200 dark:bg-gray-700 my-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h4 className="font-medium mb-2">General Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Blood Picture ESR</li>
                  <li>Blood Grouping & RH Typing</li>
                  <li>Complete Urine Examination Uric Acid</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Diabetes</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>HbA1c</li>
                  <li>FBS</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Viral Screening</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>HBsAg</li>
                  <li>HIV</li>
                  <li>Anti HCV</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Liver Function Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Total Bilirubin</li>
                  <li>Direct Bilirubin</li>
                  <li>Indirect Bilirubin</li>
                  <li>SGPT (ALT)</li>
                  <li>SGOT (AST)</li>
                  <li>ALP</li>
                  <li>Total Proteins Albumin</li>
                  <li>(Serum)</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Renal Function Tests</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Blood Urea</li>
                  <li>Creatinine</li>
                  <li>Sodium</li>
                  <li>Potassium</li>
                  <li>Chloride</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Women's Health</h4>
                <ul className="space-y-1 list-disc ml-2 text-gray-600 dark:text-gray-400 text-xs">
                  <li>Mammography</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center space-x-4 pt-6">
        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default WomensWellnessDetails;
