import React from "react";
import { But<PERSON> } from "@heroui/react";
import Image from "next/image";

const MenMultidisciplinaryDetails = () => {
  return (
    <div className="space-y-6">
      <div className="relative w-full h-[200px] rounded-lg overflow-hidden">
        <Image src="/images/d-2.png" alt="Men's Wellness" fill className="object-cover" />
        <div className="absolute bottom-6 left-6 h-fit flex flex-col gap-4 items-center justify-start w-[200px]">
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white w-fit">
            Buy Package Now
          </Button>
        </div>
      </div>

      <div className="text-gray-900 dark:text-white">
        <p className="mb-6 text-xs text-justify">
          The Men's Wellness Package provides a comprehensive health assessment, including tests for diabetes, thyroid, liver, renal, and pancreatic functions.
          It features prostate cancer screening (PSA), imaging (USG, X-Ray), cardiac evaluations (ECG, 2D Echo, TMT), skeletal health (BMD), and vitamin levels
          (B12, D3). Additional tests include lipid and lung function profiles and viral screenings for holistic health monitoring.
        </p>

        <div className="space-y-8">
          <div>
            <h3 className="text-xs font-semibold mb-3">Package Coverage and Tests:</h3>
            <div className="w-full h-[1px] bg-gray-200 dark:bg-gray-700 my-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Column 1 */}
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-2">General</h4>
                  <ul className="space-y-1 text-gray-600 dark:text-gray-400 text-xs">
                    <li>CBP</li>
                    <li>ESR</li>
                    <li>Stool Routine & Occult Blood</li>
                    <li>Complete Urine Examination</li>
                    <li>Blood Grouping and Rh Typing</li>
                    <li>Urine Microalbumin</li>
                    <li>RA Factor</li>
                    <li>Uric Acid</li>
                    <li>IgG</li>
                    <li>Pap Smear</li>
                    <li>Diabetes</li>
                    <li>FBS</li>
                    <li>HbA1c</li>
                    <li>PPBS</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Renal Function Tests</h4>
                  <ul className="space-y-1 text-gray-600 dark:text-gray-400 text-xs">
                    <li>Blood Urea Serum</li>
                    <li>Creatinine Serum</li>
                    <li>K, Na, Cl Serum</li>
                    <li>Calcium</li>
                    <li>Serum Phosphorous</li>
                    <li>Lipids</li>
                    <li>Fasting Lipid Profile</li>
                    <li>Lipoprotein (Lp(a))</li>
                    <li>Apolipoprotein A1</li>
                    <li>Apolipoprotein B Serum</li>
                    <li>Homocysteine</li>
                    <li>HSCRP</li>
                  </ul>
                </div>
              </div>

              {/* Column 2 */}
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-2">Hormones</h4>
                  <ul className="space-y-1 text-gray-600 dark:text-gray-400 text-xs">
                    <li>T3, T4, TSH</li>
                    <li>Luteinising Hormone (LH)</li>
                    <li>Follicle Stimulating Hormone(FSH)</li>
                    <li>Prolactin</li>
                    <li>Liver Function Tests</li>
                    <li>Total Bilirubin</li>
                    <li>Direct Bilirubin</li>
                    <li>Indirect Bilirubin</li>
                    <li>SGPT (ALT)</li>
                    <li>SGOT (AST)</li>
                    <li>ALP</li>
                    <li>Total Proteins</li>
                    <li>Albumin (Serum)</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium mb-2">ENT</h4>
                  <ul className="space-y-1 text-gray-600 dark:text-gray-400 text-xs">
                    <li>Audiometry</li>
                    <li>Vitamins</li>
                    <li>Serum B12</li>
                    <li>Vitamin D</li>
                    <li>Serum Folic Acid</li>
                    <li>Imaging</li>
                    <li>Chest X-RAY</li>
                    <li>DEXA Scan(Spine +Hip)</li>
                    <li>MRI (2 Organs)</li>
                    <li>Ultrasound whole abdomen & Pelvis</li>
                    <li>Carotid Doppler</li>
                  </ul>
                </div>
              </div>

              {/* Column 3 */}
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-2">Cardiac Screening</h4>
                  <ul className="space-y-1 text-gray-600 dark:text-gray-400 text-xs">
                    <li>ECG</li>
                    <li>2D Echo</li>
                    <li>TMT</li>
                    <li>Troponin I</li>
                    <li>D-DIMER</li>
                    <li>CT Angiography / Stress thallium CT</li>
                    <li>Calcium Score</li>
                  </ul>
                </div>

                <div>
                  <h4 className="text-xs mb-1">GI Screening</h4>
                  <ul className="space-y-1 text-xs">
                    <li>Endoscopy Colonoscopy</li>
                    <li>Cancer Screening</li>
                    <li>CA 19.9</li>
                    <li>AFP</li>
                    <li>CEA</li>
                    <li>Lung Function Tests</li>
                    <li>PFT</li>
                    <li>Viral Screening</li>
                    <li>HIV I & II (ELISA)</li>
                    <li>HBsAg (ELISA)</li>
                    <li>Anti HCV</li>
                    <li>Urology</li>
                    <li>Uroflowmetry</li>
                    <li>Iron Profile</li>
                    <li>TIBC</li>
                    <li>Serum Iron</li>
                    <li>Ferritin</li>
                    <li>Transferrin %</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center space-x-4 pt-6">
        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default MenMultidisciplinaryDetails;
