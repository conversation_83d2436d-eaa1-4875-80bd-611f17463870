import React from "react";
import { But<PERSON> } from "@heroui/react";

interface AdvancedFollowUpDetailsProps {
  onBuyPackage: () => void;
}

const AdvancedFollowUpDetails = ({
  onBuyPackage,
}: AdvancedFollowUpDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/images/g11.png"
            alt="Advanced Genetic Follow-up"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">
            Advanced Genetic Follow up
          </h2>
          <p className="text-xs text-gray-600 dark:text-gray-300 text-justify mb-4">
            This package provides analysis results based on the latest databases
            for 7 disease groups: cancer, cardiovascular diseases, metabolic and
            other genetic disorders, Malignant Hyperthermia, kidney disease,
            Alzheimer's disease, and pharmacogenomics.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">
              Advanced Genetic Follow up
            </h3>
            <ul className="text-xs text-gray-700 dark:text-gray-300 space-y-2 mb-4">
              <li>1. Kidney disease risk report: Analyzing 103 genes</li>
              <li>2. Alzheimer's disease risk report: Analyzing 4 genes</li>
              <li>3. Pharmacogenomics report: Analyzing 11 genes</li>
              <li>4. Cancer risk report: Analyzing 92 genes (reanalyzed)</li>
              <li>
                5. Cardiovascular disease risk report: Analyzing 98 genes
                (reanalyzed) etc.
              </li>
            </ul>
            <div className="space-y-1">
              <p className="text-xs text-gray-700 dark:text-gray-300">
                Package Name: Advanced Genetic Follow up
              </p>
              <p className="text-xs text-gray-700 dark:text-gray-300">
                Package ID: LAB12
              </p>
              <p className="text-xs text-green-600 dark:text-green-500">
                Price: XYZ
              </p>
            </div>
            <Button
              size="sm"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4"
              onClick={onBuyPackage}
            >
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">
              Terms & Conditions by Hospital / Clinic / Services Provider
            </h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-600 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms &
                Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">
              Package Inclusion
            </h3>
            <ul className="space-y-2 text-xs text-gray-700 dark:text-gray-300">
              <li>• Laboratory tests</li>
              <li>• Doctor's post-consultation fee</li>
              <li>• The price includes pharmacist's fee</li>
              <li>• Hospital and nursing services</li>
              <li>
                • Additional finding report related to hereditary conditions and
                pharmacogenomics
              </li>
              <li>
                • Secure DNA data storage at Bumrungrad Hospital for at least 5
                years
              </li>
            </ul>
          </div>

          {/* Suitable For */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">
              Suitable For
            </h3>
            <ul className="space-y-2 text-xs text-gray-700 dark:text-gray-300 text-justify">
              <li>
                • Individuals with genetic data stored at Bumrungrad Hospital.
              </li>
              <li>
                • Healthy individuals with no family history of the diseases
                mentioned above but who want to know their risk.
              </li>
              <li>
                • Individuals with a family history of related conditions, as
                mentioned above.
              </li>
            </ul>
          </div>

          {/* Not Suitable For */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">
              Not Suitable For
            </h3>
            <ul className="space-y-2 text-xs text-gray-700 dark:text-gray-300 text-justify">
              <li>
                • Individuals who do not have genetic data stored at Bumrungrad
                Hospital.
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button
          size="sm"
          className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white"
          onClick={onBuyPackage}
        >
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default AdvancedFollowUpDetails;
