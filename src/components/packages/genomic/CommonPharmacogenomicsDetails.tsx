import React from "react";
import { But<PERSON> } from "@heroui/react";

interface CommonPharmacogenomicsDetailsProps {
  onBuyPackage: () => void;
}

const CommonPharmacogenomicsDetails = ({ onBuyPackage }: CommonPharmacogenomicsDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g4.png" alt="Pharmacogenomics Background" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Drug Genes-Common Pharmacogenomics (PGx) Package</h2>
          <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
            <li>• To assess risks of drug hypersensitivity reactions</li>
            <li>• To evaluate the personalized drug response</li>
            <li>• To select the effective drug precisely</li>
            <li>• To minimize adverse drug reactions</li>
          </ul>
          <p className="text-xs text-gray-800 dark:text-gray-300 mt-2">
            This test analyzes sequence of 13 genes related to drug hypersensitivity and drug metabolism
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Drug Genes-Common Pharmacogenomics (PGx) Package</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">
              Common Pharmacogenomics Package analyzes 13 genes that include 2 genes-related to drug hypersensitivity and 11 genes-related to drug-metabolism
              covering more than 147 prescribed drugs.
            </p>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Drug Genes-Common Pharmacogenomics (PGx) Package</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB12</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions by Hospital / Clinic / Services Provider</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• The price includes pharmacist's fee</li>
              <li>• Laboratory test</li>
              <li>• HLA-A gene</li>
              <li>• HLA-B gene</li>
              <li>• CYP2D6 gene</li>
              <li>• CYP2C19 gene</li>
              <li>• CYP2C9 gene</li>
              <li>• VKORC1 gene</li>
              <li>• CYP1A2 gene</li>
              <li>• CYP3A4 gene</li>
              <li>• CYP3A5 gene</li>
              <li>• SLCO1B1 gene</li>
              <li>• OPRM1 gene</li>
              <li>• CYP2B6 gene</li>
              <li>• ABCG2 gene</li>
            </ul>
          </div>

          {/* Comprehensive Drug Sensitivity and Risk Test */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Comprehensive Drug Sensitivity and Risk Test</h3>
            <div className="space-y-4">
              <div>
                <p className="text-xs text-gray-900 dark:text-white mb-2">Adverse Reactions (2 Genes):</p>
                <p className="text-xs text-gray-600 dark:text-gray-300">• Drugs: Abacavir, Nevirapine, Allopurinol, Carbamazepine, Dapsone</p>
              </div>
              <div>
                <p className="text-xs text-gray-900 dark:text-white mb-2">Actionable Insights (15 Genes):</p>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  • Categories: Painkillers, Blood Thinners, Lipid-Lowering, Anticonvulsant, Heart Diseases, Impressiveness, Infectious Diseases, Cancer,
                  Psychiatry, Sleep Medicine, Nausea, Peptic Ulcer and Blood thinning.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default CommonPharmacogenomicsDetails;
