import React from "react";
import { But<PERSON> } from "@heroui/react";

interface AdvancedGeneticCarrierDetailsProps {
  onBuyPackage: () => void;
}

const AdvancedGeneticCarrierDetails = ({ onBuyPackage }: AdvancedGeneticCarrierDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g1.png" alt="Genetic Background" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Advanced Genetic Carrier Screening</h2>
          <p className="text-xs text-gray-800 dark:text-gray-300 text-justify">
            This test can detect coverage of 613 genes, including Cystic Fibrosis, Fragile X, Spinal Muscular Atrophy, Duchenne Muscular Dystrophy, Joubert
            Syndrome, Niemann-Pick Disease, and diseases in the Hemoglobinopathies group (Alpha Thalassemia, Beta Thalassemia and Sickle Cell).This test is
            suitable for a broader population, including both Thai nationals and individuals of diverse ethnic backgrounds.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Advanced Genetic Carrier Screening</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">
              Complete Pharmacogenomics Package analyzes 17 genes that include 2 genes-related to drug hypersensitivity, 11 genes-related to drug-metabolism,
              and additional 4 genes-related to drug-metabolism in cancer covering more than 150 prescribed drugs.
            </p>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Advance Genetic Carrier Screening</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB54</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions by Hospital / Clinic / Services Provider</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• Doctor's post-consultation fee</li>
              <li>• Laboratory test</li>
              <li>• Hospital services and nursing services</li>
            </ul>
          </div>

          {/* Not Suitable for */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Not Suitable for</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 text-justify">
              <li>• Individuals who have a blood transfusion less than two weeks prior to specimen collection.</li>
              <li>• Individuals who have had an allogenic (non-self donor) bone marrow or stem cell transplant.</li>
              <li>• For individuals who has a current or history of a hematological malignancy.</li>
            </ul>
          </div>

          {/* Instructions */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Instructions</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 text-justify">
              <li>• Please bring your medicines and dietary supplements that you have taken and your past medical history on the day of appointment.</li>
              <li>• You can eat and drink as normal before sample collection.</li>
              <li>• The result will be available around 20 calendar days after the sample is received.</li>
            </ul>
          </div>
        </div>
      </div>
      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default AdvancedGeneticCarrierDetails;
