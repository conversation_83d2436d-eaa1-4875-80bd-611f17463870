import React from "react";
import { But<PERSON> } from "@heroui/react";

interface DrugGenesMedGeneDetailsProps {
  onBuyPackage: () => void;
}

const DrugGenesMedGeneDetails = ({ onBuyPackage }: DrugGenesMedGeneDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g2.png" alt="Drug Genes Background" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Drug Genes BH MedGene Package</h2>
          <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
            <li>• To evaluate the personalized drug response</li>
            <li>• To select the effective drug precisely</li>
            <li>• To minimize adverse drug reactions</li>
          </ul>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl  dark:bg-gray-900/50 bg-white/90 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Drug Genes BH MedGene Package</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">
              Drug genes test will identify the variation of genes that affect medication response. The test helps predict adverse reaction and efficacy to
              particular medicines, improving drug safety, reducing the time of trial-and-error, and reducing drugs' adverse effects. Clinicians can select
              drugs precisely based on the test results.
            </p>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Drug Genes BH MedGene Package</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB27</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl  dark:bg-gray-900/50 bg-white/90 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl dark:bg-gray-900/50 bg-white/90 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• The price includes pharmacist's fee</li>
              <li>• Laboratory test</li>
              <li>• CYP2D6 gene</li>
              <li>• CYP2C19 gene</li>
              <li>• CYP2C9 gene</li>
              <li>• VKORC1 gene</li>
              <li>• CYP1A2 gene</li>
              <li>• CYP3A4 gene</li>
              <li>• CYP3A5 gene</li>
              <li>• SLCO1B1 gene</li>
              <li>• OPRM1 gene</li>
              <li>• CYP2B6 gene</li>
              <li>• ABCG2 gene</li>
            </ul>
          </div>

          {/* Detection Section */}
          <div className="rounded-xl dark:bg-gray-900/50 bg-white/90 p-6 shadow-md">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Detection</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• Anticonvulsant: Phenytoin</li>
              <li>• Pain killer: Tramadol, Codeine, Ibuprofen</li>
              <li>• Blood-thinning: Clopidogrel, Warfarin</li>
              <li>• Lipid-lowering: Simvastatin, Rosuvastatin</li>
              <li>• Heart diseases: Metoprolol, Flecainide</li>
              <li>• Immunosuppressive: Tacrolimus</li>
              <li>• Infectious diseases: Efavirenz</li>
              <li>• Cancer: Tamoxifen</li>
              <li>• Peptic ulcer: Omeprazole, Lansoprazole</li>
              <li>• Depression/ Psychiatry: Amitriptyline, Sertraline, Escitalopram</li>
              <li>• Sleep medicine: Melatonin</li>
              <li>• Nausea/ Vomiting: Ondansetron</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default DrugGenesMedGeneDetails;
