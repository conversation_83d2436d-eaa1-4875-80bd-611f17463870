import React from "react";
import { But<PERSON> } from "@heroui/react";

interface WholeExomeDetailsProps {
  onBuyPackage: () => void;
}

const WholeExomeDetails = ({ onBuyPackage }: WholeExomeDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g9.png" alt="Whole Exome Sequencing" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Whole Exome Sequencing (WES) select 1 panel</h2>
          <p className="text-xs text-gray-600 dark:text-gray-300 text-justify mb-4">
            This test can help you detect diseases early and manage your risk of developing hereditary conditions, which is beneficial for medical intervention
            and prevention. Additional, Bumrungrad hospital will store your genetic data (DNA saving) in a secure platform and you can order a new gene panel as
            per your request.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Whole Exome Sequencing (WES) select 1 panel</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">You can choose 1 hereditary panel from the following options:</p>
            <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-2 mb-4">
              <li>• cancer screen</li>
              <li>• heart screen</li>
              <li>• kidney disease</li>
              <li>• Myelodysplastic syndromes (MDS)/Acute myeloid leukemia (AML)</li>
              <li>• Alzheimer's</li>
            </ul>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Whole Exome Sequencing (WES) select 1 panel</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB12</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions by Hospital / Clinic / Services Provider</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• Apolipoprotein A1</li>
              <li>• Apolipoprotein B</li>
              <li>• Lipoprotein (a)</li>
              <li>• C-Reactive Protein (CRP)</li>
              <li>• Small Dense LDL (sLDL)</li>
              <li>• APOE (for Cardiovascular Disease)</li>
              <li>• Doctor Fee</li>
            </ul>
          </div>

          {/* Suitable For */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Suitable For</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 text-justify">
              <li>
                • Healthy individuals interested in understanding their bodies' predisposition to certain types of related conditions, as mentioned above.
              </li>
              <li>• Individuals with a family history of related conditions, as mentioned above.</li>
            </ul>
          </div>

          {/* Not Suitable For */}
          <div className="rounded-xl bg-white/90 dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Not Suitable For</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 text-justify">
              <li>• Individuals who have had a blood transfusion within two weeks before specimen collection.</li>
              <li>• Individuals who have undergone an allogeneic (non-self donor) bone marrow or stem cell transplant.</li>
              <li>• Individuals with a current or past history of hematological malignancy.</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default WholeExomeDetails;
