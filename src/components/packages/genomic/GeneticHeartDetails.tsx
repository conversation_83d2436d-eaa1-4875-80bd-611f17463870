import React from "react";
import { But<PERSON> } from "@heroui/react";

interface GeneticHeartDetailsProps {
  onBuyPackage: () => void;
}

const GeneticHeartDetails = ({ onBuyPackage }: GeneticHeartDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g6.png" alt="Genetic Heart Screening" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Genetic Heart Screening</h2>
          <p className="text-xs text-gray-800 dark:text-gray-300 text-justify mb-2">
            Genetic testing predicts the risk of inherited heart conditions for early intervention and prevention, potentially saving lives.
          </p>
          <p className="text-xs text-gray-800 dark:text-gray-300 text-justify mb-2">Gene Analysis: Screens 83 genes linked to heart diseases, including:</p>
          <ul className="text-xs text-gray-800 dark:text-gray-300 space-y-1">
            <li>• Inherited Aortopathies</li>
            <li>• Inherited Arrhythmias</li>
            <li>• Inherited Cardiomyopathies</li>
            <li>• Thrombophilia</li>
            <li>• Severe genetic high cholesterol</li>
          </ul>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Genetic Heart Screening</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">
              Complete Pharmacogenomics Package analyzes 17 genes that include 2 genes-related to drug hypersensitivity, 11 genes-related to drug-metabolism,
              and additional 4 genes-related to drug-metabolism in cancer covering more than 150 prescribed drugs.
            </p>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Genetic Heart Screening</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB12</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions by Hospital / Clinic / Services Provider</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• Doctor's post-consultation fee</li>
              <li>• Laboratory test</li>
              <li>• Hospital services and nursing services</li>
            </ul>
          </div>

          {/* Suitable For */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Suitable For:</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">1. Individuals have a family or personal history as follows:</p>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 ml-4">
              <li>• Sudden Cardiac Arrest</li>
              <li>• Exercise-induced Cardiac Arrhythmia</li>
              <li>• Heart disease at the young age</li>
              <li>• Unexplained sudden death</li>
              <li>• Aortic Aneurysm or Aortic Dissection</li>
            </ul>
            <p className="text-xs text-gray-600 dark:text-gray-300 mt-4">
              2. For healthy adults without a personal or family history of heart diseases who are interested to know their personal risk for having genetic
              heart disease.
            </p>
          </div>

          {/* Not Suitable For */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Not Suitable For</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 text-justify">
              <li>• Individuals who have a blood transfusion less than two weeks prior to specimen collection.</li>
              <li>• Individuals who have had an allogenic (non-self donor) bone marrow or stem cell transplant.</li>
              <li>• For individuals who has a current or history of a hematological malignancy.</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default GeneticHeartDetails;
