import React, { useState } from "react";
import Image from "next/image";
import { Button } from "@heroui/react";
import AdvancedGeneticCarrierDetails from "./AdvancedGeneticCarrierDetails";
import CommonPharmacogenomicsDetails from "./CommonPharmacogenomicsDetails";
import DrugGenesMedGeneDetails from "./DrugGenesMedGeneDetails";
import AdvancedGeneticHealthDetails from "./AdvancedGeneticHealthDetails";
import GeneticHeartDetails from "./GeneticHeartDetails";
import GeneticCancerDetails from "./GeneticCancerDetails";
import AdvancedLipidDetails from "./AdvancedLipidDetails";
import WholeExomeDetails from "./WholeExomeDetails";
import EssentialFollowUpDetails from "./EssentialFollowUpDetails";
import AdvancedFollowUpDetails from "./AdvancedFollowUpDetails";

interface GenomicPackagesProps {
  onSelectPackage?: (packageType: string) => void;
  showPackageDetails?: boolean;
}

const GenomicPackages = ({ onSelectPackage, showPackageDetails }: GenomicPackagesProps) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);

  const handleViewDetails = (packageName: string) => {
    setSelectedPackage(packageName);
    onSelectPackage?.(packageName);
  };

  const handleBuyPackage = () => {
    // Handle buy package logic
    console.log("Buying package:", selectedPackage);
  };

  // Add this effect to reset the selected package when showPackageDetails changes
  React.useEffect(() => {
    if (!showPackageDetails) {
      setSelectedPackage(null);
    }
  }, [showPackageDetails]);

  if (selectedPackage === "advanced-genetic-carrier") {
    return <AdvancedGeneticCarrierDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "common-pharmacogenomics") {
    return <CommonPharmacogenomicsDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "drug-genes-medgene") {
    return <DrugGenesMedGeneDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "advanced-genetic-health") {
    return <AdvancedGeneticHealthDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "genetic-heart") {
    return <GeneticHeartDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "genetic-cancer") {
    return <GeneticCancerDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "lipid-profile") {
    return <AdvancedLipidDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "wes-panel") {
    return <WholeExomeDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "essential-followup") {
    return <EssentialFollowUpDetails onBuyPackage={handleBuyPackage} />;
  } else if (selectedPackage === "advanced-followup") {
    return <AdvancedFollowUpDetails onBuyPackage={handleBuyPackage} />;
  }

  return (
    <div className="h-[calc(100vh-200px)] overflow-y-auto pr-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Advance Genetic Carrier Screening */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g1.png" alt="Genetic Carrier Screening" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Advance Genetic Carrier Screening</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 text-justify tracking-tight flex-grow">
              Carrier screening helps couples determine the risk of passing on serious genetic conditions to their child. It can be performed either before or
              during pregnancy.
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("advanced-genetic-carrier")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Common Pharmacogenomics Package */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g4.png" alt="Pharmacogenomics Package" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Common Pharmacogenomics Package</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-6 text-justify tracking-tight flex-grow">
              Common Pharmacogenomics Package analyzes 13 genes that include 2 genes-related to drug hypersensitivity and 11 genes-related to drug-metabolism
              covering more than 147 prescribed drugs.
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("common-pharmacogenomics")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Drug Genes BH MedGene Package */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g2.png" alt="Drug Genes Package" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Drug Genes BH MedGene Package</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-6 text-justify tracking-tight flex-grow">
              BH MedGene Package analyzes 11 genes-related to drug-metabolism covering more than 140 prescribed drugs. (DNA Testing for Medication Response)
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("drug-genes-medgene")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Advanced genetic health screen */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g3.png" alt="Advanced Genetic Health Screen" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Advanced genetic health screen (Whole Exome sequencing) plus DNA saving</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-6 text-justify tracking-tight flex-grow">
              Advanced genetic health screen using Whole Exome Sequencing (WES) is the proactive genetic testing that predicts the risk of hereditary cancer,
              cardio, and other associated conditions
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("advanced-genetic-health")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Genetic Heart Screening */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g6.png" alt="Genetic Heart Screening" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Genetic Heart Screening</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 text-justify tracking-tight flex-grow">
              Genetic testing for inherited heart condition is a proactive health check up that predicts the risk for certain heart diseases. If detected early,
              may have effective medical interventions and preventive measures may be recommended and may even save individual's life from being afflicted by
              heart diseases
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("genetic-heart")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Genetic Cancer Screening */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g7.png" alt="Genetic Cancer Screening" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Genetic Cancer Screening</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 text-justify tracking-tight flex-grow">
              Genetic testing for hereditary diseases is a proactive health check up that predict the risk of cancers
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("genetic-cancer")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Package Advances Lipid Profile II */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g8.png" alt="Lipid Profile" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Package Advances Lipid Profile II</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 text-justify tracking-tight flex-grow">
              Standard cholesterol tests may not completely represent cholesterol-related risk for heart attacks and strokes. Advanced Lipid Profile is better
              blood test for cardiac risk assessment.
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("lipid-profile")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Whole Exome Sequencing (WES) select 1 panel */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g9.png" alt="WES Panel" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Whole Exome Sequencing (WES) select 1 panel</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 text-justify tracking-tight flex-grow">
              Assess your risk of developing genetic diseases in your DNA (by selecting one panel)
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("wes-panel")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Essential genetic follow-up */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g10.png" alt="Essential Follow-up" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Essential genetic follow-up</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 text-justify tracking-tight flex-grow">
              Essential genetic follow-up for individuals who have already undergone whole exome sequencing (WES)
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("essential-followup")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Advanced genetic follow-up */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
          <div className="aspect-[16/8] relative">
            <Image src="/images/g11.png" alt="Advanced Follow-up" fill className="object-cover" />
          </div>
          <div className="p-6 flex flex-col flex-grow">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Advanced genetic follow-up</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 text-justify tracking-tight flex-grow">
              Advanced genetic follow-up for individuals who have already undergone whole exome sequencing (WES)
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("advanced-followup")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleBuyPackage}>
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenomicPackages;
