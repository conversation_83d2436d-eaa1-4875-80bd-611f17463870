import React from "react";
import { Button } from "@heroui/react";

interface AdvancedLipidDetailsProps {
  onBuyPackage: () => void;
}

const AdvancedLipidDetails = ({ onBuyPackage }: AdvancedLipidDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g8.png" alt="Advanced Lipid Profile" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Advanced Lipid Profile II</h2>
          <p className="text-xs text-gray-600 dark:text-gray-300 mb-4">Why is pharmacogenomic testing important?</p>
          <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
            <li>• To assess risks of drug hypersensitivity reactions</li>
            <li>• To adjust the dose and reduce adverse drug reactions from anticancer and immunosuppressant</li>
            <li>• To evaluate the personalized drug response</li>
            <li>• To select the effective drug precisely</li>
            <li>• To minimize adverse drug reactions</li>
          </ul>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Advanced Lipid Profile II</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">
              To evaluate whether a person has high levels of lipoprotein (a) that can contribute to cardiovascular diseases like heart attack and stroke.
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">To identify health problems before they cause signs or symptoms</p>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Package Advanced Lipid Profile II</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB12</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions by Hospital / Clinic / Services Provider</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• Apolipoprotein A1</li>
              <li>• Apolipoprotein B</li>
              <li>• Lipoprotein (a)</li>
              <li>• C-Reactive Protein (CRP)</li>
              <li>• Small Dense LDL (sLDL)</li>
              <li>• APOE (for Cardiovascular Disease)</li>
              <li>• Doctor Fee</li>
            </ul>
          </div>

          {/* Contents in Test Package */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 p-6 shadow-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Contents in Test Package</h3>
            <ol className="space-y-4 text-xs text-gray-600 dark:text-gray-300">
              <li>
                1. Apolipoprotein A1: Levels of apo-A1 tend to rise and fall with HDL-c levels, and deficiencies in apo-A1 correlate with an increased risk of
                developing cardiovascular disease (CVD).
              </li>
              <li>
                2. Apolipoprotein B: To be a better indicator of risk of cardiovascular disease (CVD) than LDL-C and to help with risk prediction when a person
                has multiple risk factors.
              </li>
              <li>
                3. Lipoprotein (a): To be early detection of potential cardiovascular problems. Do not respond to typical strategies to lower LDL-C such as
                diet, exercise, or most lipid-lowering drugs, the presence of a high level of Lp(a) may be used to identify individuals who might benefit from
                more aggressive treatment of other risk factors.
              </li>
              <li>
                4. hs-CRP (High Sensitivity C-Reactive Protein): To be predictive of the future risk of heart attack, stroke, sudden cardiac death, and
                peripheral arterial disease, even when lipid levels are within acceptable ranges.
              </li>
              <li>
                5. APOE (for Cardiovascular Disease): To help diagnose type III hyperlipoproteinemia or familial dysbetalipoproteinemia and has potential to
                help guide lipid treatment.
              </li>
            </ol>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default AdvancedLipidDetails;
