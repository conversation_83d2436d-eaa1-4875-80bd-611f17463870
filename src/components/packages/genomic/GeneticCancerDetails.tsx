import React from "react";
import { But<PERSON> } from "@heroui/react";

interface GeneticCancerDetailsProps {
  onBuyPackage: () => void;
}

const GeneticCancerDetails = ({ onBuyPackage }: GeneticCancerDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g7.png" alt="Genetic Cancer Screening" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Genetic Cancer Screening</h2>
          <p className="text-xs text-gray-800 dark:text-gray-300 text-justify mb-2">
            This test can predict the risk for 10 different inherited cancers as below
          </p>
          <ul className="text-xs text-gray-800 dark:text-gray-300 space-y-1">
            <li>• Breast cancer</li>
            <li>• Colorectal cancer</li>
            <li>• Cutaneous melanoma</li>
            <li>• Gastric cancer</li>
            <li>• Ovarian cancer</li>
            <li>• Pancreatic cancer</li>
            <li>• Prostate cancer</li>
          </ul>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white/5 dark:bg-gray-900/50 border border-gray-200/20 p-6 backdrop-blur-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Genetic Cancer Screening</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">
              Complete Pharmacogenomics Package analyzes 17 genes that include 2 genes-related to drug hypersensitivity, 11 genes-related to drug-metabolism,
              and additional 4 genes-related to drug-metabolism in cancer covering more than 150 prescribed drugs.
            </p>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Genetic Cancer Screening</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB12</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white/5 dark:bg-gray-900/50 border border-gray-200/20 p-6 backdrop-blur-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions by Hospital / Clinic / Services Provider</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white/5 dark:bg-gray-900/50 border border-gray-200/20 p-6 backdrop-blur-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• Doctor's post-consultation fee</li>
              <li>• Laboratory test</li>
              <li>• Hospital services and nursing services</li>
            </ul>
          </div>

          {/* Suitable For */}
          <div className="rounded-xl bg-white/5 dark:bg-gray-900/50 border border-gray-200/20 p-6 backdrop-blur-sm">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Suitable For</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">1. Individuals have a family or personal history as follows:</p>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 ml-4">
              <li>• Cancer at age younger than 50 years old</li>
              <li>• Multiple cancers in one person</li>
              <li>• Family member with cancer one or more relatives with these 10 cancers</li>
              <li>• Breast Cancer in Male</li>
              <li>• Confirmed inherited cancer in your family</li>
            </ul>
            <p className="text-xs text-gray-600 dark:text-gray-300 mt-4">
              2. For healthy adults without a personal or family history of cancer who are interested to know their personal risk for having genetic cancer.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default GeneticCancerDetails;
