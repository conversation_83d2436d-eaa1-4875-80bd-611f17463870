import React from "react";
import { But<PERSON> } from "@heroui/react";

interface AdvancedGeneticHealthDetailsProps {
  onBuyPackage: () => void;
}

const AdvancedGeneticHealthDetails = ({ onBuyPackage }: AdvancedGeneticHealthDetailsProps) => {
  return (
    <div className="space-y-2">
      {/* Hero Section */}
      <div className="rounded-xl p-6 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src="/images/g3.png" alt="Advanced Genetic Health Screen" className="w-full h-full object-cover opacity-20" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-blue-500/40" />
        </div>
        <div className="relative z-10">
          <h2 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Advanced genetic health screen (Whole Exome sequencing) plus DNA saving</h2>
          <p className="text-xs text-gray-700 dark:text-gray-200 text-justify">
            The package includes 4 reports: cancer screen, cardio screen, additional findings, and Pharmacogenomics (203 key genes). It helps detect risks for
            cancer, cardio, and related conditions for early intervention. Bumrungrad Hospital securely stores your DNA data, allowing future gene panel
            requests (extra charge).
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Package Details */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">
              Advanced genetic health screen (Whole Exome sequencing) plus DNA saving
            </h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-4 text-justify">
              Advanced genetic health screen using Whole Exome Sequencing (WES) is the proactive genetic testing that predicts the risk of hereditary cancer,
              cardio, and other associated conditions
            </p>
            <div className="space-y-1">
              <p className="text-xs text-gray-600 dark:text-gray-300">Package Name: Advanced genetic health screen (Whole Exome sequencing) plus DNA saving</p>
              <p className="text-xs text-gray-600 dark:text-gray-300">Package ID: LAB58</p>
              <p className="text-xs text-green-600 dark:text-green-500">Price: XYZ</p>
            </div>
            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-4" onClick={onBuyPackage}>
              Buy Package Now
            </Button>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Terms & Conditions by Hospital / Clinic / Services Provider</h3>
            <div className="border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Hospital / Clinic / Services Provider - insert your Terms & Conditions here
              </p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Package Inclusion */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Package Inclusion</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300">
              <li>• Laboratory tests</li>
              <li>• Doctor's post-consultation fee</li>
              <li>• Hospital and nursing services</li>
              <li>• Secure DNA data storage at Bumrungrad Hospital for 5 years</li>
            </ul>
          </div>

          {/* Suitable For */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Suitable For</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 text-justify">
              <li>• Individuals who have a family or personal history of cancer, cardio, or other associated conditions.</li>
              <li>
                • Healthy people who are interested to know about the risk of your body's predisposition for developing a certain type of cancer, cardio and
                other associated conditions as indicated above.
              </li>
            </ul>
          </div>

          {/* Not Suitable For */}
          <div className="rounded-xl bg-white dark:bg-gray-900/50 shadow-md p-6">
            <h3 className="text-xs font-semibold text-gray-900 dark:text-white mb-4">Not Suitable For</h3>
            <ul className="space-y-2 text-xs text-gray-600 dark:text-gray-300 text-justify">
              <li>• Individuals who have a blood transfusion less than two weeks prior to specimen collection.</li>
              <li>• Individuals who have had an allogenic (non-self donor) bone marrow or stem cell transplant.</li>
              <li>• For individuals who has a current or history of a hematological malignancy.</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button size="sm" className="w-1/2 bg-blue-600 hover:bg-blue-700 text-white" onClick={onBuyPackage}>
          Buy Package Now
        </Button>
      </div>
    </div>
  );
};

export default AdvancedGeneticHealthDetails;
