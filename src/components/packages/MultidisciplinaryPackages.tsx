import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@heroui/react";
import { ChevronRight } from "lucide-react";
import KidneyScreeningDetails from "./KidneyScreeningDetails";
import WomenMultidisciplinaryDetails from "./WomenMultidisciplinaryDetails";
import MenMultidisciplinaryDetails from "./MenMultidisciplinaryDetails";

interface MultidisciplinaryPackagesProps {
  onChoosePackage: () => void;
  onSelectPackage: (packageType: string) => void;
  showPackageDetails: boolean;
}

const MultidisciplinaryPackages: React.FC<MultidisciplinaryPackagesProps> = ({ onChoosePackage, onSelectPackage, showPackageDetails }) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);

  useEffect(() => {
    if (!showPackageDetails) {
      setSelectedPackage(null);
    }
  }, [showPackageDetails]);

  const handleViewDetails = (packageType: string) => {
    setSelectedPackage(packageType);
    onSelectPackage(packageType);
  };

  if (showPackageDetails) {
    if (selectedPackage === "Kidney Screening Package") {
      return (
        <div className="space-y-4">
          <KidneyScreeningDetails />
        </div>
      );
    }
    if (selectedPackage === "Personalized Multidisciplinary Health Checkup for Women") {
      return (
        <div className="space-y-4">
          <WomenMultidisciplinaryDetails />
        </div>
      );
    }
    if (selectedPackage === "Personalized Multidisciplinary Health Checkup for Men") {
      return (
        <div className="space-y-4">
          <MenMultidisciplinaryDetails />
        </div>
      );
    }
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Women's Package */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
          <div className="aspect-[16/8] relative">
            <Image src="/images/d-1.png" alt="Women's Health" fill className="object-cover" />
          </div>
          <div className="p-4">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Personalized Multidisciplinary Health Checkup for Women</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-6 text-justify tracking-tight">
              Our Women's Wellness Package offers personalized care with yoga, Ayurveda, and relaxation therapies. Focused on hormonal balance, stress relief,
              and vitality, it includes tailored nutrition plans, herbal remedies, and rejuvenating massages. Empower your health and embrace well-being through
              this holistic journey designed just for women.
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("Personalized Multidisciplinary Health Checkup for Women")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Men's Package */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
          <div className="aspect-[16/8] relative">
            <Image src="/images/d-2.png" alt="Men's Health" fill className="object-cover" />
          </div>
          <div className="p-4">
            <h3 className="text-xs font-medium mb-4 text-gray-900 dark:text-white">Personalized Multidisciplinary Health Checkup for Men</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-6 text-justify tracking-tight">
              Our Men's Wellness Package provides personalized care with fitness, Ayurveda, and relaxation therapies. Focused on boosting energy, reducing
              stress, and enhancing vitality, it includes customized nutrition plans, herbal remedies, and invigorating massages. Take control of your health
              and embrace well-being through this holistic journey designed specifically for men.
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("Personalized Multidisciplinary Health Checkup for Men")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>

        {/* Kidney Screening */}
        <div className="rounded-2xl bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
          <div className="aspect-[16/8] relative">
            <Image src="/images/d-3.png" alt="Kidney Screening" fill className="object-cover" />
          </div>
          <div className="p-6">
            <h3 className="text-sm font-medium mb-4 text-gray-900 dark:text-white">Kidney Screening Package</h3>
            <p className="text-xs text-gray-600 dark:text-gray-300 mb-6 text-justify">
              Comprehensive kidney health assessment including blood tests, urine analysis, and imaging studies. Our specialized screening helps detect early
              signs of kidney disease, evaluate kidney function, and provide personalized recommendations for kidney health maintenance. Includes consultation
              with nephrology specialists and detailed health reports.
            </p>
            <div className="space-y-1 mt-auto">
              <Button
                size="sm"
                variant="light"
                className="w-full text-xs text-blue-600 dark:text-blue-400 hover:underline"
                onClick={() => handleViewDetails("Kidney Screening Package")}
              >
                View Full Package Details
              </Button>
              <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                Buy Package Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultidisciplinaryPackages;
