"use client";
import React, { useEffect, useRef } from "react";
import QRCodeStyling from "qr-code-styling";

const TestQRCode = () => {
  const qrRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (qrRef.current) {
      const qrCode = new QRCodeStyling({
        width: 300,
        height: 300,
        type: "canvas",
        data: "https://example.com",
        dotsOptions: {
          color: "#000000",
          type: "rounded",
        },
        backgroundOptions: {
          color: "#ffffff",
        },
      });

      qrRef.current.innerHTML = "";
      qrCode.append(qrRef.current);
    }
  }, []);

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Test QR Code</h2>
      <div
        ref={qrRef}
        className="border border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-4 min-h-[320px] flex items-center justify-center"
      ></div>
    </div>
  );
};

export default TestQRCode;
