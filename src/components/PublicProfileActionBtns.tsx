import { But<PERSON> } from "@heroui/button";
import { Input, Textarea } from "@heroui/input";
import { CardBody, Card, Modal, ModalBody, <PERSON>dal<PERSON>ontent, ModalFooter, ModalHeader, Checkbox } from "@heroui/react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import CustomerAppointment from "@/components/Forms/PublicProfile/CustomerAppointment";
import { useMessageMe } from "@/hooks/public-profile/useProfileCategory";
import { useRouter } from "next/navigation";

interface PublicProfileActionBtnsProps {
  publicProfile: any;
  user: any;
  isCurrentUserProfile?: boolean;
}

const PublicProfileActionBtns = ({ publicProfile, user, isCurrentUserProfile = false }: PublicProfileActionBtnsProps) => {
  const { t } = useTranslation();
  const { mutate: messageMe } = useMessageMe();
  const router = useRouter();
  const [showMessageDialog, setShowMessageDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [subject, setSubject] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);
  const [donateModal, setDonateModal] = useState(false);
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState<string>("");
  const [isMonthly, setIsMonthly] = useState(false);
  const [isHonorMemory, setIsHonorMemory] = useState(false);
  const [honoreeName, setHonoreeName] = useState("");
  const [donationStep, setDonationStep] = useState(1);
  const [isAnonymous, setIsAnonymous] = useState(false);

  // Donor information
  const [firstName, setFirstName] = useState("");
  const [middleName, setMiddleName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [contactNumber, setContactNumber] = useState("");
  const [streetAddress, setStreetAddress] = useState("");
  const [country, setCountry] = useState("");
  const [postalCode, setPostalCode] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");

  const handleMessageClick = () => {
    if (!user) {
      toast.error("Please login to send a message");
      return;
    }

    if (isCurrentUserProfile) {
      router.push(`/my/${user?.user_id}/message`);
    } else {
      setShowMessageDialog(true);
    }
  };

  const handleAppointmentClick = () => {
    if (isCurrentUserProfile) {
      router.push(`/my/${user?.user_id}/appointments?tab=appointmentSchedule`);
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    try {
      setSendingMessage(true);
      await messageMe({ subject, content: message, userId: publicProfile?.user_id });
      setShowMessageDialog(false);
      setMessage("");
      setSubject("");
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleDonation = () => {
    const amount = selectedAmount || (customAmount ? parseFloat(customAmount) : 0);
    if (!amount || amount <= 0) {
      toast.error("Please select or enter a valid donation amount");
      return;
    }

    toast.success(`Donation of $${amount} ${isMonthly ? "monthly" : "one-time"} processed successfully!`);
    setDonateModal(false);
    resetDonationForm();
  };

  const resetDonationForm = () => {
    setSelectedAmount(null);
    setCustomAmount("");
    setIsMonthly(false);
    setIsHonorMemory(false);
    setHonoreeName("");
    setDonationStep(1);
    setIsAnonymous(false);
    setFirstName("");
    setMiddleName("");
    setLastName("");
    setEmail("");
    setContactNumber("");
    setStreetAddress("");
    setCountry("");
    setPostalCode("");
    setCity("");
    setState("");
  };

  const nextStep = () => {
    const amount = selectedAmount || (customAmount ? parseFloat(customAmount) : 0);
    if (donationStep === 1 && (!amount || amount <= 0)) {
      toast.error("Please select or enter a valid donation amount");
      return;
    }

    if (donationStep < 2) {
      setDonationStep(donationStep + 1);
    } else {
      handleDonation();
    }
  };

  const prevStep = () => {
    if (donationStep > 1) {
      setDonationStep(donationStep - 1);
    }
  };

  return (
    <>
      <div className="grid md:grid-cols-2 gap-2 w-full mt-2">
        {/* Appointment Button */}
        {isCurrentUserProfile ? (
          <Button
            size="sm"
            variant="flat"
            color="primary"
            className="w-full rounded-lg px-2 py-1 text-xs bg-blue-700 hover:bg-blue-800 text-white border-none"
            onPress={handleAppointmentClick}
          >
            {t("ravid.publicProfile.appointments")}
          </Button>
        ) : (
          <CustomerAppointment publicProfile={publicProfile} user={user} />
        )}
        <Button
          size="sm"
          variant="flat"
          color="primary"
          className="rounded-lg w-full px-2 py-1 text-xs bg-blue-700 hover:bg-blue-800 text-white border-none"
          onPress={handleMessageClick}
        >
          {t("ravid.publicProfile.message")}
        </Button>

        {isCurrentUserProfile && (
          <>
            <Button
              size="sm"
              variant="flat"
              color="primary"
              className="rounded-lg w-full px-2 py-1 text-xs bg-blue-700 hover:bg-blue-800 text-white border-none"
              onPress={() => router.push(`/my/${user?.user_id}/notifications`)}
            >
              {t("ravid.publicProfile.notifications")}
            </Button>

            <Button
              variant="flat"
              size="sm"
              color="primary"
              className="rounded-lg w-full px-2 py-1 text-xs bg-blue-700 hover:bg-blue-800 text-white border-none"
              onPress={() => router.push(`/my/${user?.user_id}/payments`)}
            >
              {t("ravid.publicProfile.payments")}
            </Button>
          </>
        )}
        <Button size="sm" variant="flat" className="rounded-lg px-2 py-1 text-xs " onPress={() => setDonateModal(true)}>
          {t("ravid.publicProfile.pay&donate")}
        </Button>

        <Button size="sm" variant="flat" className="rounded-lg px-2 py-1 text-xs" disabled={true}>
          {t("ravid.publicProfile.telemedicine")}
        </Button>
      </div>

      {/* Message Dialog - Only shown for non-current user profiles */}
      {!isCurrentUserProfile && (
        <Modal className="bg-slate-950" isOpen={showMessageDialog} onOpenChange={setShowMessageDialog}>
          <ModalContent className="sm:max-w-[425px]">
            <ModalHeader className="flex flex-col gap-2">
              <h2>Send a Message</h2>
              <p>Write your message to {publicProfile?.first_name || publicProfile?.username}</p>
            </ModalHeader>
            <ModalBody>
              <Input
                classNames={{
                  input: "placeholder:text-xs",
                }}
                placeholder="Subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
              />
              <Textarea
                placeholder="Type your message here..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                classNames={{
                  input: "placeholder:text-xs",
                }}
              />
            </ModalBody>
            <ModalFooter>
              <Button type="button" size="sm" color="danger" onPress={() => setShowMessageDialog(false)} disabled={sendingMessage}>
                Cancel
              </Button>
              <Button type="button" size="sm" color="primary" onPress={handleSendMessage} disabled={sendingMessage}>
                {sendingMessage ? "Sending..." : "Send Message"}
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {donateModal && (
        <Modal size="3xl" className="bg-slate-950" isOpen={donateModal} onOpenChange={setDonateModal}>
          <ModalContent>
            <ModalHeader>
              <h1>Donate</h1>
              {/* Progress Stepper */}
              <div className="flex items-center w-full mt-4 mb-2">
                <div className="flex flex-col items-center w-1/2">
                  <div
                    className={`rounded-full h-8 w-8 flex items-center justify-center border-2 ${
                      donationStep >= 1 ? " border-blue-700 text-blue-300" : "bg-gray-200 border-gray-300 text-gray-600"
                    }`}
                  >
                    1
                  </div>
                  <span className={`text-xs mt-1 ${donationStep >= 1 ? "text-blue-500" : "text-gray-500"}`}>Choose Amount</span>
                </div>
                <div className={`h-0.5 w-full ${donationStep >= 2 ? "bg-blue-600" : "bg-gray-300"}`}></div>
                <div className="flex flex-col items-center w-1/2">
                  <div
                    className={`rounded-full h-8 w-8 flex items-center justify-center border-2 ${
                      donationStep >= 2 ? "bg-blue-600 border-blue-700 text-white" : "bg-gray-200 border-gray-300 text-gray-600"
                    }`}
                  >
                    2
                  </div>
                  <span className={`text-xs mt-1 ${donationStep >= 2 ? "text-blue-500" : "text-gray-500"}`}>Donor's Information</span>
                </div>
              </div>
            </ModalHeader>
            <ModalBody>
              <Card className="p-4 bg-slate-950 border border-slate-800">
                <CardBody>
                  {donationStep === 1 && (
                    <>
                      <div className="flex flex-wrap gap-2 mb-4">
                        <Button
                          size="sm"
                          variant="flat"
                          className={selectedAmount === 1000 ? "bg-green-600 hover:bg-green-700" : "bg-slate-800 hover:bg-slate-700"}
                          onPress={() => setSelectedAmount(1000)}
                        >
                          $1000
                        </Button>
                        <Button
                          size="sm"
                          variant="flat"
                          className={selectedAmount === 500 ? "bg-green-600 hover:bg-green-700" : "bg-slate-800 hover:bg-slate-700"}
                          onPress={() => setSelectedAmount(500)}
                        >
                          $500
                        </Button>
                        <Button
                          size="sm"
                          variant="flat"
                          className={selectedAmount === 200 ? "bg-green-600 hover:bg-green-700" : "bg-slate-800 hover:bg-slate-700"}
                          onPress={() => setSelectedAmount(200)}
                        >
                          $200
                        </Button>
                        <Button
                          size="sm"
                          variant="flat"
                          className={selectedAmount === 100 ? "bg-green-600 hover:bg-green-700" : "bg-slate-800 hover:bg-slate-700"}
                          onPress={() => setSelectedAmount(100)}
                        >
                          $100
                        </Button>
                        <Button
                          size="sm"
                          variant="flat"
                          className={selectedAmount === 50 ? "bg-green-600 hover:bg-green-700" : "bg-slate-800 hover:bg-slate-700"}
                          onPress={() => setSelectedAmount(50)}
                        >
                          $50
                        </Button>
                      </div>

                      <Input
                        className="mb-4"
                        classNames={{
                          input: "text-xs",
                          label: "text-xs text-gray-500 ",
                          inputWrapper: "dark:bg-slate-900",
                        }}
                        type="number"
                        placeholder="Add custom amount"
                        value={customAmount}
                        onChange={(e) => {
                          setSelectedAmount(null);
                          setCustomAmount(e.target.value);
                        }}
                      />

                      <div className="space-y-4">
                        <div className="flex items-center gap-2">
                          <Checkbox checked={isMonthly} onChange={(e) => setIsMonthly(e.target.checked)} />
                          <span className="text-xs">Make this a monthly donation</span>
                        </div>

                        <div className="flex items-center gap-2 mb-2">
                          <Checkbox checked={isHonorMemory} onChange={(e) => setIsHonorMemory(e.target.checked)} />
                          <span className="text-xs">I'd like to make this contribution in honor or in memory of someone</span>
                        </div>

                        <Input
                          classNames={{
                            input: "text-xs",
                            label: "text-xs text-gray-500 ",
                            inputWrapper: "dark:bg-slate-900",
                          }}
                          placeholder="Name of the honoree"
                          value={honoreeName}
                          onChange={(e) => setHonoreeName(e.target.value)}
                          disabled={!isHonorMemory}
                        />
                      </div>
                    </>
                  )}

                  {donationStep === 2 && (
                    <>
                      <div className="flex items-center gap-2 mb-4 rounded-lg">
                        <Checkbox size="sm" checked={isAnonymous} onChange={(e) => setIsAnonymous(e.target.checked)} className="ml-2">
                          <h2 className="text-xs">Make an Anonymous Donation</h2>
                        </Checkbox>
                      </div>

                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <Input
                          classNames={{
                            input: "text-xs",
                            label: "text-xs text-gray-500",
                            inputWrapper: "dark:bg-slate-900",
                          }}
                          placeholder="First Name"
                          value={firstName}
                          onChange={(e) => setFirstName(e.target.value)}
                          disabled={isAnonymous}
                        />
                        <Input
                          classNames={{
                            input: "text-xs",
                            label: "text-xs text-gray-500",
                            inputWrapper: "dark:bg-slate-900",
                          }}
                          placeholder="Middle Name"
                          value={middleName}
                          onChange={(e) => setMiddleName(e.target.value)}
                          disabled={isAnonymous}
                        />
                        <Input
                          classNames={{
                            input: "text-xs",
                            label: "text-xs text-gray-500",
                            inputWrapper: "dark:bg-slate-900",
                          }}
                          placeholder="Last Name"
                          value={lastName}
                          onChange={(e) => setLastName(e.target.value)}
                          disabled={isAnonymous}
                        />
                      </div>

                      <Input
                        className="mb-4"
                        classNames={{
                          input: "text-xs",
                          label: "text-xs text-gray-500",
                          inputWrapper: "dark:bg-slate-900",
                        }}
                        placeholder="Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={isAnonymous}
                      />

                      <Input
                        className="mb-4"
                        classNames={{
                          input: "text-xs",
                          label: "text-xs text-gray-500",
                          inputWrapper: "dark:bg-slate-900",
                        }}
                        placeholder="Contact Number"
                        value={contactNumber}
                        onChange={(e) => setContactNumber(e.target.value)}
                        disabled={isAnonymous}
                      />

                      <Input
                        className="mb-4"
                        classNames={{
                          input: "text-xs",
                          label: "text-xs text-gray-500",
                          inputWrapper: "dark:bg-slate-900",
                        }}
                        placeholder="Street Address"
                        value={streetAddress}
                        onChange={(e) => setStreetAddress(e.target.value)}
                        disabled={isAnonymous}
                      />

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <Input
                            className="mb-4"
                            classNames={{
                              input: "text-xs",
                              label: "text-xs text-gray-500",
                              inputWrapper: "dark:bg-slate-900",
                            }}
                            placeholder="Country"
                            value={country}
                            onChange={(e) => setCountry(e.target.value)}
                            disabled={isAnonymous}
                          />
                        </div>
                        <div>
                          <Input
                            className="mb-4"
                            classNames={{
                              input: "text-xs",
                              label: "text-xs text-gray-500",
                              inputWrapper: "dark:bg-slate-900",
                            }}
                            placeholder="Postal Code"
                            value={postalCode}
                            onChange={(e) => setPostalCode(e.target.value)}
                            disabled={isAnonymous}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Input
                            classNames={{
                              input: "text-xs",
                              label: "text-xs text-gray-500",
                              inputWrapper: "dark:bg-slate-900",
                            }}
                            placeholder="City"
                            value={city}
                            onChange={(e) => setCity(e.target.value)}
                            disabled={isAnonymous}
                          />
                        </div>
                        <div>
                          <Input
                            classNames={{
                              input: "text-xs",
                              label: "text-xs text-gray-500",
                              inputWrapper: "dark:bg-slate-900",
                            }}
                            placeholder="State"
                            value={state}
                            onChange={(e) => setState(e.target.value)}
                            disabled={isAnonymous}
                          />
                        </div>
                      </div>

                      {isAnonymous && <p className="text-xs mt-4 text-gray-400 italic">**This information would be shared with the donation drive creator</p>}
                    </>
                  )}

                  {!isAnonymous && donationStep === 2 && (
                    <div className="mt-4 mb-2 text-xs text-gray-400">
                      <p>
                        Amount: ${selectedAmount || customAmount} {isMonthly ? "(Monthly)" : "(One-time)"}
                      </p>
                      {isHonorMemory && honoreeName && <p>In honor of: {honoreeName}</p>}
                    </div>
                  )}
                </CardBody>
              </Card>
            </ModalBody>
            <ModalFooter>
              <Button
                type="button"
                size="sm"
                color="danger"
                onPress={() => {
                  if (donationStep > 1) {
                    prevStep();
                  } else {
                    setDonateModal(false);
                    resetDonationForm();
                  }
                }}
              >
                {donationStep > 1 ? "Back" : "Cancel"}
              </Button>
              <Button type="button" size="sm" color="primary" onPress={nextStep}>
                {donationStep === 2 ? "Complete Donation" : "Continue"}
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default PublicProfileActionBtns;
