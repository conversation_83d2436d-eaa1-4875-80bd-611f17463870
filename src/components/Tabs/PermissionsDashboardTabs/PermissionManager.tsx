"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@heroui/button";
import { Switch } from "@heroui/switch";
import { Card, CardBody } from "@heroui/card";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import TermsOfUse from "@/components/Disclaimers/TermsOfUse";
import AgeDeclaration from "@/components/AgeDeclaration";
import MedicalProfessional from "@/components/MedicalProfessional";

export const PermissionManager = () => {
  const [showFullDisclaimer, setShowFullDisclaimer] = useState(false);
  const [permissions, setPermissions] = useState({
    accountAccess: false,
    emergencyAccess: false,
    prescriptionAI: false,
    diagnosisAI: false,
    dnaAccess: false,
    aiDnaAnalysis: false,
  });

  const handlePermissionChange = (permissionId: string) => {
    setPermissions((prev) => ({
      ...prev,
      [permissionId]: !prev[permissionId as keyof typeof permissions],
    }));
  };

  return (
    <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
      <CardBody>
        <div className="max-w-6xl mx-auto space-y-4">
          <h2 className="mb-6">R.A.V.I.D. Permissions Management</h2>

          <ImportantNoticeDropdown />
          <TermsOfUse />
          <AgeDeclaration />
          <MedicalProfessional />

          <section className="border border-gray-200 dark:border-gray-800 rounded-lg p-2">
            <div className="grid gap-2 md:grid-cols-3">
              {/* AI DNA Analysis */}
              <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
                <CardBody className="p-3">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h2>AI DNA Analysis</h2>
                      <span className="text-xs muted">Allow AI algorithms to analyze DNA data</span>
                    </div>
                    <div className="flex justify-between items-center mt-auto">
                      <Switch
                        color="success"
                        size="sm"
                        isSelected={permissions.aiDnaAnalysis}
                        onValueChange={() => handlePermissionChange("aiDnaAnalysis")}
                        className="mr-2"
                      />
                      <Button size="sm" variant="bordered">
                        Settings
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* AI Diagnosis Analysis */}
              <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
                <CardBody className="p-4">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h2>AI Diagnosis Analysis</h2>
                      <span className="text-xs muted">Share genomic & medical data during emergencies</span>
                    </div>
                    <div className="flex justify-between items-center mt-auto">
                      <Switch
                        color="success"
                        size="sm"
                        isSelected={permissions.diagnosisAI}
                        onValueChange={() => handlePermissionChange("diagnosisAI")}
                        className="mr-2"
                      />
                      <Button size="sm" variant="bordered">
                        Settings
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* AI Prescription Analysis */}
              <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
                <CardBody className="p-4">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h2>AI Prescription Analysis</h2>
                      <span className="text-xs muted">Allow AI algorithms to analyze prescription history</span>
                    </div>
                    <div className="flex justify-between items-center mt-auto">
                      <Switch
                        color="success"
                        size="sm"
                        isSelected={permissions.prescriptionAI}
                        onValueChange={() => handlePermissionChange("prescriptionAI")}
                        className="mr-2"
                      />
                      <Button size="sm" variant="bordered">
                        Settings
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Account Sharing */}
              <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
                <CardBody className="p-4">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h2>Account Sharing</h2>
                      <span className="text-xs muted">Allow sharing of account access with loved ones</span>
                    </div>
                    <div className="flex justify-between items-center mt-auto">
                      <Switch
                        color="success"
                        size="sm"
                        isSelected={permissions.accountAccess}
                        onValueChange={() => handlePermissionChange("accountAccess")}
                        className="mr-2"
                      />
                      <Button size="sm" variant="bordered">
                        Settings
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* DNA File Access */}
              <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
                <CardBody className="p-4">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h2>DNA File Access</h2>
                      <span className="text-xs muted">Grant DNA file access to research institutions</span>
                    </div>
                    <div className="flex justify-between items-center mt-auto">
                      <Switch
                        color="success"
                        size="sm"
                        isSelected={permissions.dnaAccess}
                        onValueChange={() => handlePermissionChange("dnaAccess")}
                        className="mr-2"
                      />
                      <Button size="sm" variant="bordered">
                        Settings
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Emergency & Medical Access */}
              <Card className="dark:bg-slate-950 border rounded-xl dark:border-slate-900">
                <CardBody className="p-4">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h2>Emergency & Medical Access</h2>
                      <span className="text-xs muted">Share genomic & medical data during emergencies</span>
                    </div>
                    <div className="flex justify-between items-center mt-auto">
                      <Switch
                        size="sm"
                        color="success"
                        isSelected={permissions.emergencyAccess}
                        onValueChange={() => handlePermissionChange("emergencyAccess")}
                        className="mr-2"
                      />
                      <Button size="sm" variant="bordered">
                        Settings
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>

            <div className="flex flex-row-reverse gap-2 justify-between mt-8">
              <Button color="primary" size="sm">
                Save Changes
              </Button>
            </div>
          </section>

          <div className="flex flex-row-reverse gap-2 justify-between mt-8">
            <div>
              <div className="md:block">
                <span className={` muted ${showFullDisclaimer ? "" : "line-clamp-3 md:line-clamp-none"}`}>
                  Disclaimer: Please note that all the information you input, store and generate using any AI enabled or algorithmic functions & if successfully
                  saved from within various sections of your R.A.V.I.D. account will assume you have NOT given us Permission to share with various third parties
                  or care providers. You can however change, enable or thereafter deny all Permission privileges within thisPermission Management section. The
                  saved changes cannot undo previous Permission settings and your last saved changes will also be subject to time delay for the acceptance of
                  your instructions within various databases, so you are hereby assuming all liability for any loss of receipt of the Permissions during the
                  transmission process. Additionally, please note that the "My R.A.V.I.D. Settings" to include the "Personal, Emergency, Medical Team and
                  Insurance" sections are hard coded features of your R.A.V.I.D. account and your permission is explicitly needed to deploy various QR codes and
                  also needed to ensure our legal compliance of knowing who are the users of R.A.V.I.D. platform. Additionally by deploying the QR code you
                  assume all liability of disclosing your confidential information.
                </span>
                <button onClick={() => setShowFullDisclaimer(!showFullDisclaimer)} className="text-blue-500 text-xs mt-1 md:hidden">
                  {showFullDisclaimer ? "Show Less" : "Show More"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};
