import { Table, TableHeader, TableBody, TableColumn, TableRow, TableCell, cn } from "@heroui/react";

const billingData = [
  {
    date: "2025-02-20",
    license: "100 Seat",
    service: "AI Analysis",
    skuId: "AIA-01-01",
    usage: "89%",
    cost: "$20",
    costDuration: "Monthly",
    discount: "0",
    promotions: "Trial Period",
  },
  {
    date: "2025-02-20",
    license: "Enterprise",
    service: "Setup",
    skuId: "SETUP",
    usage: "100%",
    cost: "$100",
    costDuration: "One Time",
    discount: "0",
    promotions: "Trial Period",
  },
  // Add more dummy data as needed
];

export const BillingTab = () => {
  return (
    <div className="w-full h-[300px] p-2 ">
      <Table
        classNames={{
          th: "dark:bg-slate-900",
        }}
        removeWrapper
        className="border dark:border-gray-800 rounded-lg p-1"
        aria-label="Billing information table"
      >
        <TableHeader>
          <TableColumn><h2>Date</h2></TableColumn>
          <TableColumn><h2>License</h2></TableColumn>
          <TableColumn><h2>Service</h2></TableColumn>
          <TableColumn><h2>SKU ID</h2></TableColumn>
          <TableColumn><h2>Usage</h2></TableColumn>
          <TableColumn><h2>Cost</h2></TableColumn>
          <TableColumn><h2>Cost Duration</h2></TableColumn>
          <TableColumn><h2>Discount</h2></TableColumn>
          <TableColumn><h2>Promotions</h2></TableColumn>
        </TableHeader>
        <TableBody>
          {billingData.map((item, index) => (
            <TableRow
              className={cn("border-b border-gray-200 dark:border-gray-700", { "border-b-0": item === billingData[billingData.length - 1] })}
            key={index}>
              <TableCell><p>{item.date}</p></TableCell>
              <TableCell><p>{item.license}</p></TableCell>
              <TableCell><p>{item.service}</p></TableCell>
              <TableCell><p>{item.skuId}</p></TableCell>
              <TableCell><p>{item.usage}</p></TableCell>
              <TableCell><p>{item.cost}</p></TableCell>
              <TableCell><p>{item.costDuration}</p></TableCell>
              <TableCell><p>{item.discount}</p></TableCell>
              <TableCell><p>{item.promotions}</p></TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
