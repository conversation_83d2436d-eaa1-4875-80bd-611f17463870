import React from "react";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@heroui/react";


export const ESSubscriptionTab = () => {
  return (
    <div className="space-y-6 p-2">
      {/* Active Features Section */}
      <div>
        <h1 className="mb-4">Active Features:</h1>
        <div className="w-full overflow-x-auto">
          <div className="min-w-[800px]">
            <Table
              aria-label="Active features table"
              removeWrapper
              className="border rounded-lg p-1 dark:border-slate-800 border-slate-300"
              classNames={{
                th: "dark:bg-slate-900",
              }}
            >
              <TableHeader>
                <TableColumn>
                  <h2>Feature Name</h2>
                </TableColumn>
                <TableColumn>
                  <h2>Date</h2>
                </TableColumn>
                <TableColumn>
                  <h2>Amount</h2>
                </TableColumn>
                <TableColumn>
                  <h2>Transaction ID</h2>
                </TableColumn>
                <TableColumn>
                  <h2>Action</h2>
                </TableColumn>
              </TableHeader>
              <TableBody>
                <TableRow key="1" className="border-b dark:border-slate-800">
                  <TableCell>
                    <p>AI Monthly Subscription</p>
                  </TableCell>
                  <TableCell>
                    <p>2025-01-15</p>
                  </TableCell>
                  <TableCell>
                    <p>NA</p>
                  </TableCell>
                  <TableCell>
                    <p>1234567890</p>
                  </TableCell>
                  <TableCell>
                    <span className="text-xs text-green-500">Subscribed to ES</span>
                  </TableCell>
                </TableRow>
                <TableRow key="2" className="border-b dark:border-slate-800">
                  <TableCell>
                    <p>Data Storage</p>
                  </TableCell>
                  <TableCell>
                    <p>2025-01-15</p>
                  </TableCell>
                  <TableCell>
                    <p>NA</p>
                  </TableCell>
                  <TableCell>
                    <p>1234567890</p>
                  </TableCell>
                  <TableCell>
                    <span className="text-xs text-green-500">Subscribed to ES</span>
                  </TableCell>
                </TableRow>
                <TableRow key="3">
                  <TableCell>
                    <p>Verification - with Enterprise</p>
                  </TableCell>
                  <TableCell>
                    <p>2025-01-15</p>
                  </TableCell>
                  <TableCell>
                    <p>NA</p>
                  </TableCell>
                  <TableCell>
                    <p>1234567890</p>
                  </TableCell>
                  <TableCell>
                    <span className="text-xs text-green-500">Subscribed to ES</span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Premium Features Section */}

      {/* Footer Notes */}
      <div className="space-y-2 text-xs text-gray-400">
        <p>
          * All subscriptions are billed monthly and can be canceled at any time. Your subscription will help support the platform and unlock premium features.
        </p>
        <p>
          ** DNA Analysis is based on a Pay for Service model, i.e. you pay for every time you request an analysis. As computational biology evolves so will be
          the understanding and the results of the analytical services.
        </p>
      </div>
    </div>
  );
};
