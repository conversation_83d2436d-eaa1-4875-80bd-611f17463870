import { But<PERSON>, Card, Input } from "@heroui/react";
import React from "react";

export const MessageNotificationsSettings = () => {
  return (
    <Card className="p-2 bg-slate-950">
      <p className="muted mb-6">Enter the mail on which you wish to receive updates on regarding messages from people on R.A.V.I.D.</p>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex justify-around sm:w-1/3 w-full">
          <Input
            size="sm"
            placeholder="Enter primary mail here"
            classNames={{
              input: "text-xs ml-2",
              inputWrapper: "!px-0 !mx-0",
            }}
            className="w-full "
            endContent={
              <Button size="sm" color="primary">
                Save
              </Button>
            }
          />
        </div>

        <div className="flex justify-around sm:w-1/3 w-full">
          <Input
            size="sm"
            placeholder="Enter secondary mail here"
            className="w-full"
            classNames={{
              input: "text-xs ml-2",
              inputWrapper: "!px-0 !mx-0",
            }}
            endContent={
              <Button size="sm" color="primary">
                Save
              </Button>
            }
          />
        </div>
      </div>
    </Card>
  );
};
