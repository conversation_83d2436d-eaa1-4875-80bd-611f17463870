import { DAYS } from "@/config/constants";
import { AppointmentAvailability, useAppointmentAvailability } from "@/hooks/use-appointments";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import FullCalendar from "@fullcalendar/react";
import { <PERSON>ton, Card, CardBody, Modal, ModalBody, ModalContent, ModalFooter, TimeInput, Switch } from "@heroui/react";
import { Time } from "@internationalized/date";
import { PlusIcon, X } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

// Helper function to parse time string HH:mm or HH:mm:ss to Time object
const parseTimeString = (timeString: string | undefined): Time | null => {
  if (!timeString) return null;
  const parts = timeString.split(":").map(Number);
  // Ensure at least hour and minute are present and valid numbers
  if (parts.length >= 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
    // Handle potential out-of-bounds values gracefully if needed, though Time constructor might handle basic validation
    const hour = Math.max(0, Math.min(23, parts[0]));
    const minute = Math.max(0, Math.min(59, parts[1]));
    return new Time(hour, minute);
  }
  return null;
};

// Helper function to format a time object with hour/minute properties to HH:mm string
const formatTimeObject = (timeValue: { hour: number; minute: number } | null): string => {
  if (!timeValue || typeof timeValue.hour !== "number" || typeof timeValue.minute !== "number") return "";
  return `${String(timeValue.hour).padStart(2, "0")}:${String(timeValue.minute).padStart(2, "0")}`;
};

// Multiple days availability form interface
interface MultiDayAvailabilityForm {
  title: string;
  days: {
    date: string;
    times: Array<{
      from: string;
      to: string;
      mode: string;
    }>;
  }[];
}

interface WorkingHoursForm {
  applyToAll: boolean;
  globalFrom: string;
  globalTo: string;
  days: {
    [key: string]: {
      id?: string;
      enabled: boolean;
      times: Array<{
        id?: string;
        from: string;
        to: string;
      }>;
    };
  };
  slotDuration: string;
  breakTime: {
    from: string;
    to: string;
  };
}

// Default form values
const getDefaultWorkingHoursForm = () => ({
  applyToAll: false,
  globalFrom: "",
  globalTo: "",
  days: DAYS.reduce(
    (acc, day) => ({
      ...acc,
      [day.key]: {
        id: undefined,
        enabled: !["SAT", "SUN"].includes(day.key),
        times: [{ id: undefined, from: "00:00", to: "23:59", mode: "in_person" }],
      },
    }),
    {}
  ),
  slotDuration: "30",
  breakTime: {
    from: "",
    to: "",
  },
});

export const AppointmentsManager = () => {
  const dataLoadedRef = useRef(false);
  const [selectedDates, setSelectedDates] = useState<string[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AppointmentAvailability | null>(null);
  const [isEventModalOpen, setIsEventModalOpen] = useState(false);
  const {
    availabilities,
    appointments,
    createAppointmentAvailability,
    isCreating,
    deleteAppointmentAvailability,
    updateSingleAppointmentAvailability,
    isDeleting,
    isUpdating,
    myAvailableSlots,
  } = useAppointmentAvailability();

  // Form for multiple days availability
  const { control: multiDayControl, handleSubmit: handleMultiDaySubmit, reset: resetMultiDay } = useForm<MultiDayAvailabilityForm>({
    defaultValues: {
      title: "Availability",
      days: [],
    },
  });

  // Form for advanced settings
  const { reset } = useForm<WorkingHoursForm>({
    defaultValues: getDefaultWorkingHoursForm(),
  });

  // Helper function to check if an availability is confirmed in myAvailableSlots
  const isAvailabilityConfirmed = (availabilityId: string | undefined) => {
    if (!myAvailableSlots || !availabilityId) return false;

    // Look through all schedules and slots to find matching availability
    for (const day of myAvailableSlots.schedule) {
      for (const slot of day.slots) {
        if (slot.availability_id === availabilityId && slot.booking_info && slot.booking_info.status === "confirmed") {
          return true;
        }
      }
    }
    return false;
  };

  // Helper function to get booking details for an availability
  const getBookingDetails = (availabilityId: string | undefined) => {
    if (!myAvailableSlots || !availabilityId) return null;

    // Find the slot with booking info
    for (const day of myAvailableSlots.schedule) {
      for (const slot of day.slots) {
        if (slot.availability_id === availabilityId && slot.booking_info && slot.booking_info.status === "confirmed") {
          return slot.booking_info;
        }
      }
    }
    return null;
  };

  // Format events for the calendar view with memoization for performance
  const calendarEvents = useMemo(
    () =>
      availabilities?.map((availability) => {
        // Check if this availability is confirmed in myAvailableSlots
        const isConfirmed = availability.status === "confirmed" || isAvailabilityConfirmed(availability.id);

        return {
          id: availability.id,
          title: availability.title || "Available",
          start: `${availability.start_date}T${availability.start_time}`,
          end: `${availability.end_date}T${availability.end_time}`,
          allDay: false,
          extendedProps: {
            formattedTimeRange: `${availability.start_time.substring(0, 5)} - ${availability.end_time.substring(0, 5)}`,
            // Set background color based on status: green for confirmed, purple for everything else
            backgroundColor: isConfirmed ? "bg-[#49195E]" : "bg-[#4f46e5]",
            isConfirmed,
          },
        };
      }) || [],
    [availabilities, myAvailableSlots]
  );

  // Handle form resets and initialization with React Hook Form
  const resetForms = () => {
    // Reset advanced settings form
    reset(getDefaultWorkingHoursForm());

    // Clear multi-day form when needed
    if (selectedDates.length === 0) {
      resetMultiDay({
        title: "Availability",
        days: [],
      });
    }
  };

  // Use callback for data updates instead of effects
  useEffect(() => {
    if (availabilities?.length && !dataLoadedRef.current) {
      dataLoadedRef.current = true;
      resetForms();
    } else if (!availabilities?.length) {
      dataLoadedRef.current = false;
    }
  }, [availabilities, resetForms, selectedDates]);

  // Handle calendar selection
  const handleSelect = (selectInfo: any) => {
    const startDate = new Date(selectInfo.startStr);
    const endDate = new Date(selectInfo.endStr);
    const dates: string[] = [];
    const currentDate = new Date(startDate);

    // Generate array of dates between start and end
    while (currentDate < endDate) {
      dates.push(currentDate.toISOString().split("T")[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Automatically set title based on selection
    const autoTitle = dates.length === 1 ? startDate.toLocaleDateString("en-US", { weekday: "long" }) : "Weekly Schedule";

    setSelectedDates(dates);

    // Reset form with form hook's reset method
    resetMultiDay({
      title: autoTitle,
      days: dates.map((date) => ({
        date,
        times: [{ from: "00:00", to: "23:59", mode: "in_person" }],
      })),
    });
    setIsModalOpen(true);
  };

  // Submit handler for multiple days availability
  const onMultiDaySubmit = async (data: MultiDayAvailabilityForm) => {
    const availabilityPayloads = data.days.flatMap((day) =>
      day.times.map((time) => ({
        title: data.title,
        start_date: day.date,
        end_date: day.date,
        start_time: time.from.includes(":") ? `${time.from}:00` : "00:00:00",
        end_time: time.to.includes(":") ? `${time.to}:00` : "00:00:00",
        is_active: true,
        recurrence_type: "none" as const,
        recurrence_interval: 1,
        recurrence_days: "",
        recurrence_month_day: null,
        recurrence_end_date: null,
        recurrence_count: null,
        status: "pending",
        mode: time.mode,
      }))
    );

    if (availabilityPayloads.length > 0) {
      // Show a single loading toast when creating multiple availabilities
      if (availabilityPayloads.length > 1) {
        toast.loading(`Creating ${availabilityPayloads.length} availabilities...`, { id: "creating-availabilities" });
      }

      // Send all availabilities in a single API call instead of multiple parallel calls
      createAppointmentAvailability(availabilityPayloads as AppointmentAvailability[]);
      closeAvailabilityModal();
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Handle event actions
  const handleEventClick = (clickInfo: any) => {
    const eventId = clickInfo.event.id;
    const availability = availabilities?.find((a) => a.id === eventId);
    if (availability) {
      setSelectedEvent(availability);
      setIsEventModalOpen(true);
    }
  };

  const handleEventUpdate = () => {
    if (!selectedEvent?.id) return;
    updateSingleAppointmentAvailability({
      id: selectedEvent.id,
      data: selectedEvent,
    });
    closeEventModal();
  };

  const handleEventDelete = () => {
    if (!selectedEvent?.id) return;
    deleteAppointmentAvailability(selectedEvent.id);
    closeEventModal();
  };

  const closeEventModal = () => {
    setIsEventModalOpen(false);
    setSelectedEvent(null);
  };

  const closeAvailabilityModal = () => {
    setIsModalOpen(false);
    setSelectedDates([]);
    // Reset the multi-day form on modal close
    resetMultiDay({
      title: "Availability",
      days: [],
    });
  };

  interface TimeSlotSelectorProps {
    field: {
      value: Array<{ from: string; to: string; mode: string }>;
      onChange: (value: Array<{ from: string; to: string; mode: string }>) => void;
    };
    timeIndex: number;
    onAdd: () => void;
    onRemove: () => void;
    isFirst?: boolean;
  }

  const TimeSlotSelector = ({ field, timeIndex, onAdd, onRemove, isFirst = false }: TimeSlotSelectorProps) => {
    return (
      <div className="flex items-center gap-4">
        <div className="flex-1 flex items-center gap-2">
          <span className="text-xs">From</span>
          <TimeInput
            size="sm"
            aria-label={`From time ${timeIndex + 1}`}
            value={parseTimeString(field.value[timeIndex].from) as any}
            onChange={(timeValue) => {
              const newTimes = [...field.value];
              newTimes[timeIndex].from = formatTimeObject(timeValue);
              field.onChange(newTimes);
            }}
            granularity="minute"
            hideTimeZone
            className="flex-1"
          />
          <span className="text-xs">To</span>
          <TimeInput
            size="sm"
            aria-label={`To time ${timeIndex + 1}`}
            value={parseTimeString(field.value[timeIndex].to) as any}
            onChange={(timeValue) => {
              const newTimes = [...field.value];
              newTimes[timeIndex].to = formatTimeObject(timeValue);
              field.onChange(newTimes);
            }}
            granularity="minute"
            hideTimeZone
            className="flex-1"
          />
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-600 dark:text-gray-400">Video Call</span>
            <Switch
              size="sm"
              aria-label={`Video call mode ${timeIndex + 1}`}
              isSelected={field.value[timeIndex].mode.includes("video_call")}
              onValueChange={(isSelected) => {
                const newTimes = [...field.value];
                if (isSelected) {
                  newTimes[timeIndex].mode = "in_person,video_call";
                } else {
                  newTimes[timeIndex].mode = "in_person";
                }
                field.onChange(newTimes);
              }}
              color="primary"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          {isFirst ? (
            <Button size="sm" isIconOnly variant="flat" color="primary" onClick={onAdd}>
              <PlusIcon className="w-4 h-4" />
            </Button>
          ) : (
            <Button size="sm" isIconOnly variant="flat" color="danger" onClick={onRemove}>
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    );
  };

  // Event content renderer
  const renderEventContent = (eventInfo: any) => {
    const currentBackgroundColor = eventInfo.event.extendedProps.backgroundColor;
    const isConfirmed = eventInfo.event.extendedProps.isConfirmed;
    return (
      <div className={`flex flex-col text-xs rounded-md p-1 ${currentBackgroundColor}`}>
        <div className="font-semibold text-white">{eventInfo.event.extendedProps.formattedTimeRange}</div>
        {isConfirmed && <div className="text-white overflow-hidden text-ellipsis">Confirmed</div>}
      </div>
    );
  };

  return (
    <div className="flex flex-col p-3 relative h-[calc(100vh-160px)] overflow-y-auto">
      <div className="flex flex-col gap-2 mb-2">
        <span className="muted text-xs">
          Manage your working hours and availability for appointments. This will be used to calculate your utilization rate and availability.
        </span>
      </div>

      {/* Calendar View */}
      <div className="mb-4">
        <Card className="dark:bg-slate-950 border dark:border-gray-800 border-gray-300 rounded-lg w-full overflow-hidden">
          <CardBody>
            <div className="calendar-container" style={{ maxHeight: "calc(100vh - 220px)" }}>
              <FullCalendar
                plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                initialView="dayGridMonth"
                selectable={true}
                select={handleSelect}
                eventClick={handleEventClick}
                events={calendarEvents}
                height="auto"
                headerToolbar={{
                  left: "prev,next today",
                  center: "title",
                  right: "dayGridMonth,timeGridWeek,timeGridDay",
                }}
                selectConstraint={{
                  start: new Date().toISOString().split("T")[0], // Today
                  end: "2025-12-31", // Far future date
                }}
                selectMirror={true}
                dayMaxEvents={true}
                unselectAuto={false}
                eventContent={renderEventContent}
                longPressDelay={0}
              />
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Multiple Days Availability Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeAvailabilityModal}
        classNames={{
          base: "dark:bg-slate-950 rounded-xl",
          backdrop: "bg-black/50 backdrop-blur-sm",
        }}
        size="xl"
      >
        <ModalContent>
          <form onSubmit={handleMultiDaySubmit(onMultiDaySubmit)}>
            <div className="p-4 border-b dark:border-gray-800 border-gray-300">
              <h2>{selectedDates.length > 1 ? `Set Availability for ${selectedDates.length} Days` : "Set Availability"}</h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">Select time slots for each selected date</p>
            </div>
            <ModalBody className="py-4 space-y-4 max-h-[70vh] overflow-y-auto">
              {selectedDates?.map((date, dateIndex) => (
                <div key={date} className="rounded-lg p-4 border dark:border-gray-800 border-gray-300">
                  <h2 className="mb-3 font-semibold">{formatDate(date)}</h2>
                  <Controller
                    name={`days.${dateIndex}.times`}
                    control={multiDayControl}
                    render={({ field }) => (
                      <div className="space-y-3">
                        {field.value?.map((_, timeIndex) => (
                          <TimeSlotSelector
                            key={timeIndex}
                            field={field}
                            timeIndex={timeIndex}
                            isFirst={timeIndex === 0}
                            onAdd={() => {
                              // Calculate new start time based on previous end time
                              const prevEndTime = field.value[timeIndex].to;
                              // Parse hours and minutes
                              const [hours, minutes] = prevEndTime.split(":").map(Number);
                              // Add 1 minute
                              let newMinutes = minutes + 1;
                              let newHours = hours;

                              // Handle minute overflow
                              if (newMinutes >= 60) {
                                newHours = (newHours + 1) % 24;
                                newMinutes = newMinutes % 60;
                              }

                              // Format new start time
                              const newStartTime = `${String(newHours).padStart(2, "0")}:${String(newMinutes).padStart(2, "0")}`;

                              // Set end time 1 hour after start time
                              let endHours = newHours + 1;
                              if (endHours >= 24) endHours = 23;
                              const newEndTime = `${String(endHours).padStart(2, "0")}:${String(newMinutes).padStart(2, "0")}`;

                              const newTimes = [...field.value, { from: newStartTime, to: newEndTime, mode: "in_person" }];
                              field.onChange(newTimes);
                            }}
                            onRemove={() => {
                              const newTimes = field.value.filter((_, i) => i !== timeIndex);
                              field.onChange(newTimes);
                            }}
                          />
                        ))}
                      </div>
                    )}
                  />
                </div>
              ))}
            </ModalBody>
            <ModalFooter>
              <Button size="sm" variant="flat" color="danger" onPress={closeAvailabilityModal}>
                Cancel
              </Button>
              <Button size="sm" color="primary" type="submit" isLoading={isCreating}>
                Save
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>

      {/* Event Edit/Delete Modal */}
      <Modal
        isOpen={isEventModalOpen}
        onClose={closeEventModal}
        classNames={{
          base: "dark:bg-slate-950 rounded-xl",
          backdrop: "bg-black/50 backdrop-blur-sm",
        }}
        size="md"
      >
        <ModalContent>
          <ModalBody className="py-4 space-y-4">
            {selectedEvent && (
              <div className="space-y-4 p-3 rounded-lg">
                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <h2>Start Time:</h2>
                        <TimeInput
                          size="sm"
                          aria-label="Start time"
                          value={parseTimeString(selectedEvent.start_time) as any}
                          onChange={(timeValue) => {
                            setSelectedEvent((prev) =>
                              prev ? { ...prev, start_time: timeValue ? `${formatTimeObject(timeValue)}:00` : prev.start_time } : null
                            );
                          }}
                          granularity="minute"
                          hideTimeZone
                          className="flex-1 rounded-md shadow-sm"
                          isDisabled={selectedEvent.status === "confirmed" || isAvailabilityConfirmed(selectedEvent.id)}
                        />
                      </div>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <h2>End Time:</h2>
                        <TimeInput
                          size="sm"
                          aria-label="End time"
                          value={parseTimeString(selectedEvent.end_time) as any}
                          onChange={(timeValue) => {
                            setSelectedEvent((prev) => (prev ? { ...prev, end_time: timeValue ? `${formatTimeObject(timeValue)}:00` : prev.end_time } : null));
                          }}
                          granularity="minute"
                          hideTimeZone
                          className="flex-1 rounded-md shadow-sm"
                          isDisabled={selectedEvent.status === "confirmed" || isAvailabilityConfirmed(selectedEvent.id)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Booking details section */}
                  {(selectedEvent.status === "confirmed" || isAvailabilityConfirmed(selectedEvent.id)) && (
                    <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                      <h2>Appointment Booked</h2>
                      {getBookingDetails(selectedEvent.id) ? (
                        <div className="text-xs space-y-1">
                          {getBookingDetails(selectedEvent.id)?.patient_name && (
                            <p>
                              <span className="font-medium">Booked by:</span> {getBookingDetails(selectedEvent.id)?.patient_name}
                            </p>
                          )}
                          {getBookingDetails(selectedEvent.id)?.patient_id && (
                            <p>
                              <span className="font-medium">Patient ID:</span> {getBookingDetails(selectedEvent.id)?.patient_id}
                            </p>
                          )}
                          {getBookingDetails(selectedEvent.id)?.title && (
                            <p>
                              <span className="font-medium">Name:</span> {getBookingDetails(selectedEvent.id)?.title}
                            </p>
                          )}
                          {getBookingDetails(selectedEvent.id)?.start_time && (
                            <p>
                              <span className="font-medium">Time:</span> {getBookingDetails(selectedEvent.id)?.start_time} {"-"}
                              {getBookingDetails(selectedEvent.id)?.end_time}
                            </p>
                          )}
                        </div>
                      ) : (
                        <p className="text-xs">This slot has been confirmed.</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              size="sm"
              variant="flat"
              color="danger"
              onPress={handleEventDelete}
              isLoading={isDeleting}
              isDisabled={Boolean(selectedEvent?.status === "confirmed" || (selectedEvent && isAvailabilityConfirmed(selectedEvent.id)))}
            >
              Delete
            </Button>
            <Button size="sm" variant="flat" color="default" onPress={closeEventModal}>
              Cancel
            </Button>
            <Button
              size="sm"
              color="primary"
              onPress={handleEventUpdate}
              isLoading={isUpdating}
              isDisabled={Boolean(selectedEvent?.status === "confirmed" || (selectedEvent && isAvailabilityConfirmed(selectedEvent.id)))}
            >
              Update
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
