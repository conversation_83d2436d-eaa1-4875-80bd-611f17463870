import { useAppointmentAvailability } from "@/hooks/use-appointments";
import type { Appointment } from "@/hooks/use-appointments";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ner,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Textarea,
} from "@heroui/react";
import { Download, Mail, Trash2 } from "lucide-react";
import { ArrowDownToLine } from "lucide-react";
import Image from "next/image";
import { MdAddToDrive } from "react-icons/md";
import { useState } from "react";
import { toast } from "react-hot-toast";
import CustomModal from "@/components/Modals/CustomModal";

// Attachment data type
type Attachment = {
  id: string;
  file: number;
  file_url: string;
  filename: string;
  description: string;
  created_at: string;
};

// Extend the Appointment type to include our custom fields
interface ExtendedAppointment extends Appointment {
  attachments?: Attachment[];
}

type AppointmentRequest = {
  id: string;
  senderName?: string;
  requestedDate: string;
  startTime?: string;
  endTime?: string;
  requestTime: string;
  mode?: string;
  patientNote: string;
  sharedData: string;
  status: string;
  attachments: Attachment[];
};

type ModalType = "patientNote" | "sharedData" | "accept" | "reject" | null;
type ButtonColor = "primary" | "danger" | "default" | "secondary" | "success" | "warning";

interface ModalConfig {
  title: string;
  content: string;
  confirmButton: boolean;
  startTime?: string;
  endTime?: string;
  confirmText: string;
  confirmColor?: ButtonColor;
  showCancel: boolean;
  showTextarea?: boolean;
}

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  modalType: ModalType;
  request: AppointmentRequest | null;
  onConfirm?: (id: string, reason?: string) => void;
}

const AppointmentModal = ({ isOpen, onClose, modalType, request, onConfirm }: AppointmentModalProps) => {
  const [cancellationReason, setCancellationReason] = useState("");

  if (!request || !modalType) return null;

  console.log(request);

  const modalConfig: Record<string, ModalConfig> = {
    patientNote: {
      title: "Patient Note",
      startTime: request.startTime,
      endTime: request.endTime,
      content: request.patientNote,
      confirmButton: false,
      confirmText: "",
      showCancel: true,
    },
    sharedData: {
      title: "Sender's Shared Data",
      content: request.sharedData,
      confirmButton: false,
      confirmText: "",
      showCancel: true,
    },
    accept: {
      title: "Accept Appointment Request",
      content: `By accepting this appointment request, your slot on ${request.requestedDate} from ${request.startTime} to ${request.endTime} would be booked for  - ${request.senderName}.`,
      confirmButton: true,
      confirmText: "Confirm",
      confirmColor: "primary",
      showCancel: true,
    },
    reject: {
      title: "Reject Appointment Request",
      content: `Please provide a reason for rejecting this appointment request for Patient - ${request.senderName}.`,
      confirmButton: true,
      confirmText: "Confirm Rejection",
      confirmColor: "danger",
      showCancel: true,
      showTextarea: true,
    },
  };

  const config = modalConfig[modalType];

  const handleConfirm = () => {
    if (modalType === "reject" && !cancellationReason.trim()) {
      toast.error("Please provide a reason for cancellation");
      return;
    }
    onConfirm && onConfirm(request.id, cancellationReason);
    setCancellationReason("");
  };

  return (
    <Modal 
      className="dark:bg-slate-950" 
      isOpen={isOpen} 
      onOpenChange={(open) => !open && onClose()}
      size="2xl"
    >
      <ModalContent className="max-h-[90vh] overflow-y-auto">
        <ModalHeader className="border-b dark:border-gray-800 py-4">
          <h2 className="text-base font-semibold">{config.title}</h2>
        </ModalHeader>
        <ModalBody className="py-6">
          <div className="text-sm">
            <p className="text-gray-600 dark:text-gray-300">{config.content}</p>
            {config.showTextarea && (
              <div className="mt-6">
                <Textarea
                  placeholder="Enter reason for cancellation"
                  value={cancellationReason}
                  onChange={(e) => setCancellationReason(e.target.value)}
                  className="w-full text-sm min-h-[120px]"
                />
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter className="border-t dark:border-gray-800 py-4">
          <div className="flex gap-3">
            {config.confirmButton && (
              <Button className="text-sm px-6" size="md" color={config.confirmColor} onPress={handleConfirm}>
                {config.confirmText}
              </Button>
            )}
            {config.showCancel && (
              <Button className="text-sm px-6" size="md" color="danger" variant={modalType === "reject" ? "bordered" : "solid"} onPress={onClose}>
                {modalType === "patientNote" || modalType === "sharedData" ? "Close" : "Cancel"}
              </Button>
            )}
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export const AppointmentRequests = () => {
  const [modalType, setModalType] = useState<ModalType>(null);
  const [selectedRequest, setSelectedRequest] = useState<AppointmentRequest | null>(null);
  const [attachment, setAttachment] = useState(false);
  const [selectedAttachmentIndex, setSelectedAttachmentIndex] = useState(0);
  const [previewUrl, setPreviewUrl] = useState("");
  const [previewFileName, setPreviewFileName] = useState("");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const { appointments, isLoadingAppointments, refetchAppointments, updateStatus } = useAppointmentAvailability();

  // Filter appointments to only include those with appointment_type "booking"
  const appointmentRequests: AppointmentRequest[] = appointments
    ? (appointments as ExtendedAppointment[])
        .filter((appointment) => appointment.appointment_type === "booking")
        .map((appointment) => ({
          id: appointment.id,
          mode: appointment.mode,
          senderName: appointment.title,
          requestedDate: appointment.start_time.split("T")[0],
          startTime: appointment.start_time.split("T")[1].substring(0, 5),
          endTime: appointment.end_time.split("T")[1].substring(0, 5),
          requestTime: appointment.start_time.split("T")[1].substring(0, 5),
          patientNote: appointment.notes || "No patient notes provided",
          sharedData: "No shared data available",
          status: appointment.status || "pending",
          attachments: appointment.attachments || [],
        }))
    : [];

  const handleAccept = (id: string) => {
    updateStatus({ id, status: "confirmed" });
    closeModal();
  };

  const handleReject = (id: string, reason?: string) => {
    if (!reason) return;
    updateStatus({ id, status: "canceled", cancellation_reason: reason });
    closeModal();
  };

  const openModal = (type: ModalType, request: AppointmentRequest) => {
    setSelectedRequest(request);
    setModalType(type);
  };

  const closeModal = () => {
    setModalType(null);
    setSelectedRequest(null);
  };

  const handleViewAttachment = (request: AppointmentRequest) => {
    if (request.attachments.length === 0) return;
    setSelectedRequest(request);
    setSelectedAttachmentIndex(0);
    setAttachment(true);
  };

  // Function to handle pagination change
  const handlePageChange = (page: number) => {
    setSelectedAttachmentIndex(page - 1);
  };

  const formatDate = (date: string) => {
    return date.replace(/(\d{4})-(\d{2})-(\d{2})/, "$1-$2-$3");
  };

  const handlePreview = (fileUrl: string, fileName: string) => {
    if (!fileUrl) {
      console.error("File URL is missing or undefined");
      return;
    }

    setPreviewUrl(fileUrl);
    setPreviewFileName(fileName);
    setIsPreviewOpen(true);
  };

  if (isLoadingAppointments) {
    return (
      <div className="flex justify-center items-center w-full h-48">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full h-full p-6">
      <div className="flex flex-col gap-3 mb-6">
        <h1 className="text-xl font-semibold">Appointment Requests</h1>
        <p className="text-gray-600 dark:text-gray-300">Here you can accept/reject appointment requests from patients</p>
      </div>

      <div className="w-full rounded-lg border dark:border-gray-800 overflow-hidden overflow-x-auto bg-white dark:bg-slate-900/50">
        <Table
          aria-label="Appointment requests table"
          selectionMode="multiple"
          classNames={{
            base: "w-full",
            th: "text-sm dark:bg-slate-900 text-gray-600 dark:text-gray-300 py-4 px-6 border-b border-gray-800 font-semibold",
            td: "text-sm py-4 px-6",
            tr: "border-b dark:border-gray-800 hover:bg-slate-50 dark:hover:bg-slate-800/40 transition-colors",
          }}
          removeWrapper
        >
          <TableHeader>
            <TableColumn><h2 className="text-xs">Sender Information</h2></TableColumn>
            <TableColumn><h2 className="text-xs">Appointment Requested</h2></TableColumn>
            <TableColumn width="30%"><h2 className="text-xs">Type</h2></TableColumn>
            <TableColumn><h2 className="text-xs">Sender's Note</h2></TableColumn>
            <TableColumn align="center"><h2 className="text-xs">Sender's Attachment</h2></TableColumn>
            <TableColumn align="center"><h2 className="text-xs">Sender's QR Code Data</h2></TableColumn>
            <TableColumn align="center"><h2 className="text-xs">Actions</h2></TableColumn>
          </TableHeader>
          <TableBody>
            {appointmentRequests?.map((request) => (
              <TableRow key={request.id} className="border-b dark:border-gray-800 hover:bg-slate-800/40">
                <TableCell>
                  <h2>{request.senderName}</h2>
                </TableCell>

                <TableCell>
                  <h2>
                    {formatDate(request.requestedDate)}, {request.requestTime}
                  </h2>
                </TableCell>

                <TableCell >
                  <span className="badge whitespace-nowrap">{request.mode === "in_person" ? "In-Person" : "Video Call"}</span>
                </TableCell>

                <TableCell>
                  <Button size="sm" color="primary" variant="light" onPress={() => openModal("patientNote", request)}>
                    View Sender's Note
                  </Button>
                </TableCell>

                <TableCell>
                  <Button size="sm" color="primary" variant="light" onPress={() => handleViewAttachment(request)} isDisabled={!request.attachments?.length}>
                    View Attachment {request.attachments?.length ? `(${request.attachments.length})` : ""}
                  </Button>
                </TableCell>

                <TableCell>
                  <Button size="sm" color="primary" variant="light" onPress={() => openModal("sharedData", request)}>
                    Sender's Shared Data
                  </Button>
                </TableCell>

                <TableCell>
                  {request.status === "confirmed" ? (
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Confirmed
                    </div>
                  ) : request.status === "canceled" ? (
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                      Rejected
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <Button size="sm" className="text-xs bg-blue-600 hover:bg-blue-700 text-white" onPress={() => openModal("accept", request)}>
                        Accept
                      </Button>
                      <Button size="sm" color="danger" onPress={() => openModal("reject", request)}>
                        Reject
                      </Button>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <Modal 
        size="4xl" 
        className="bg-slate-950" 
        isOpen={attachment} 
        onOpenChange={setAttachment}
      >
        <ModalContent className="max-h-[90vh] overflow-y-auto">
          <ModalHeader className="flex flex-col gap-4 border-b dark:border-gray-800 py-4">
            <h2 >Attachments</h2>
            {selectedRequest?.attachments && selectedRequest.attachments.length > 0 && (
              <div className="max-h-[200px] overflow-auto">
                <Table
                  aria-label="Attachments table"
                  classNames={{
                    base: "w-full",
                    th: "text-xs dark:bg-slate-900 text-gray-400 py-3 px-4 border-b border-gray-800",
                    td: "text-xs py-3 px-4",
                    tr: "border-b dark:border-gray-800 hover:bg-slate-800/40",
                  }}
                  removeWrapper
                >
                  <TableHeader>
                    <TableColumn>File Name</TableColumn>
                    <TableColumn>Date</TableColumn>
                    <TableColumn>Actions</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {selectedRequest.attachments.map((item, idx) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Button
                            variant="light"
                            size="sm"
                            className="!bg-transparent text-xs !justify-start"
                            onPress={() => handlePreview(item.file_url, item.filename)}
                          >
                            <span className="truncate max-w-[150px] sm:max-w-none text-xs">File {idx+1}</span>
                          </Button>
                        </TableCell>
                        <TableCell>{new Date(item.created_at).toLocaleString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 sm:gap-2">
                            <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                              <Download className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                              <Mail className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                              <MdAddToDrive className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                              <Trash2 className="w-4 text-red-500 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </ModalHeader>
          <ModalBody className="py-6">
            {selectedRequest?.attachments && selectedRequest.attachments.length > 0 && (
              <div className="max-h-[600px] overflow-auto">
                {(() => {
                  const currentFile = selectedRequest.attachments[selectedAttachmentIndex];
                  const fileUrl = currentFile.file_url;
                  return (
                    <div className="flex flex-col items-center justify-center p-8 bg-gray-100 dark:bg-gray-800 rounded-lg">
                      <Image 
                        src={fileUrl} 
                        alt={currentFile.filename} 
                        width={1000} 
                        height={1000} 
                        className="w-full h-auto rounded-lg shadow-lg" 
                      />
                    </div>
                  );
                })()}
              </div>
            )}
          </ModalBody>
          <ModalFooter className="justify-center flex border-t dark:border-gray-800 py-4">
            {selectedRequest?.attachments && selectedRequest.attachments.length > 1 && (
              <Pagination
                initialPage={selectedAttachmentIndex + 1}
                page={selectedAttachmentIndex + 1}
                onChange={handlePageChange}
                showControls
                size="sm"
                total={selectedRequest.attachments.length}
                className="gap-2"
              />
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Preview Modal */}
      <CustomModal
        title={previewFileName}
        body={
          previewUrl && (
            <div className="w-full h-[70vh] relative">
              <iframe 
                src={`${previewUrl}#toolbar=0`} 
                className="w-full h-full rounded-lg shadow-lg" 
                title={previewFileName}
                onLoad={(e) => {
                  const iframe = e.target as HTMLIFrameElement;
                  if (iframe.contentWindow) {
                    const content = iframe.contentWindow.document;
                    content.body.style.margin = '0';
                    content.body.style.padding = '0';
                  }
                }}
              />
            </div>
          )
        }
        size="4xl"
        isOpen={isPreviewOpen}
        onOpenChange={setIsPreviewOpen}
        primaryButtonText="Close"
        primaryButtonColor="bg-red-700 hover:bg-red-800 text-white"
        className="max-w-[95vw] w-auto max-h-[90vh] overflow-y-auto"
      />

      <AppointmentModal
        isOpen={modalType !== null}
        onClose={closeModal}
        modalType={modalType}
        request={selectedRequest}
        onConfirm={modalType === "accept" ? handleAccept : modalType === "reject" ? handleReject : undefined}
      />
    </div>
  );
};
