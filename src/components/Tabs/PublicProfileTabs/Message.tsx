import { useGetMessages, useMarkMessageAsRead } from "@/hooks/public-profile/useProfileCategory";
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from "@heroui/react";
import { useState } from "react";
import { useStore } from "@/store/store";

export const Message = () => {
  const [selectedChat, setSelectedChat] = useState<any | null>(null);
  const { user } = useStore();
  const { data: messages } = useGetMessages(user?.user_id);
  const { mutate: markAsRead } = useMarkMessageAsRead();

  const handleMessageClick = (message: any) => {
    setSelectedChat(message);

    // Automatically mark message as read when opened
    if (message && !message.is_read) {
      markAsRead(message.id);
    }
  };

  const handleMarkAsRead = (messageId: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation(); // Prevent triggering message click
    }
    markAsRead(messageId);
  };

  return (
    <div className="flex flex-col p-2">
      <div className="flex gap-4 h-[calc(100vh-200px)]">
        {/* Left side - Messages List */}
        <Card className="w-2/5 bg-transparent border dark:border-gray-800 overflow-hidden">
          <CardHeader className="border-b dark:border-gray-800">
            <h2>Messages</h2>
          </CardHeader>
          <div className="relative">
            <div className="overflow-y-auto max-h-[calc(100vh-280px)]">
              {messages?.map((message: any) => (
                <div
                  key={message.id}
                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-900 cursor-pointer border-b dark:border-gray-950 flex items-center ${
                    selectedChat?.id === message.id ? "bg-gray-50 dark:bg-gray-800" : ""
                  }`}
                  onClick={() => handleMessageClick(message)}
                >
                  <div className="w-10 h-10 rounded-full flex items-center justify-center bg-primary-100 text-primary-600 dark:bg-primary-950 dark:text-primary-300 mr-3 flex-shrink-0">
                    <span className="text-lg font-semibold">{message.patient_name?.[0] || "U"}</span>
                  </div>
                  <div className="flex-grow truncate">
                    <div className="flex justify-between items-center">
                      <h2>{message.patient_name}</h2>
                      <span className="muted">{new Date(message.created_at).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}</span>
                    </div>
                    <p className="text-xs text-gray-700 truncate">{message.subject || message.content?.substring(0, 50) || "No message content"}</p>
                  </div>
                </div>
              ))}
              {messages?.length === 0 && (
                <div className="flex flex-col items-center justify-center h-40 p-4">
                  <h3 className="font-medium">Welcome to your Messages</h3>
                  <span className="text-sm text-gray-500">All your messages would appear here once you receive some.</span>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Right side - Selected Message */}
        <Card className="w-3/5 bg-transparent border dark:border-gray-800 overflow-hidden">
          {selectedChat ? (
            <>
              <CardHeader className="border-b dark:border-gray-800 flex items-center">
                <button className="mr-3 md:hidden" onClick={() => setSelectedChat(null)}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <div className="w-10 h-10 rounded-full flex items-center justify-center bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-300 mr-3">
                  <span className="text-lg font-semibold">{selectedChat.patient_name?.[0] || "U"}</span>
                </div>
                <div>
                  <h2>{selectedChat.patient_name}</h2>
                </div>
              </CardHeader>
              <CardBody className="p-0 overflow-y-auto max-h-[calc(100vh-280px)]">
                <div className="p-6">
                  <div className="mb-4">
                    <div className="bg-primary-50 dark:bg-gray-800 rounded-lg p-2 max-w-[80%] ml-auto">
                      <p className="whitespace-pre-wrap">{selectedChat.content}</p>
                      <div className="text-right">
                        <span className="text-xs text-gray-500">
                          {new Date(selectedChat.created_at).toLocaleString([], {
                            year: "numeric",
                            month: "numeric",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
              <div className="border-t dark:border-gray-800 p-2 text-center">
                <Button color="danger" size="sm">
                  Delete Message
                </Button>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-6">
              <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Your Messages</h3>
              <p className="text-gray-500 text-center">Select a message to view its contents here</p>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};
