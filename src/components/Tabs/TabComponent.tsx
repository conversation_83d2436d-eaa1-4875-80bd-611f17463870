import { cn } from "@/lib/utils";
import { Tabs as HeroTabs, TabsProps as HeroTabsProps, Tab } from "@heroui/react";
import { useTranslation } from "react-i18next";

export interface TabItem {
  key: string;
  title: string;
  content: React.ReactNode;
  titleClassName?: string;
  badge?: string;
  endIcon?: React.ReactNode;
}

interface TabsProps extends HeroTabsProps {
  tabs: TabItem[];
}

const Tabs = ({ tabs, classNames: customClassNames, variant = "underlined", color = "primary", radius = "lg", ...props }: TabsProps) => {
  const { t } = useTranslation();
  const defaultClassNames = {
    base: "", // The main tabs slot, it wraps the items and the panels
    tabList: "flex flex-wrap md:flex-nowrap", // The tab list slot, it wraps the tab items
    tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit", // The tab slot, it wraps the tab item
    tabContent: "text-gray-700 dark:text-white wrap", // The tab content slot, it wraps the tab content
    cursor: "bg-blue-500", // The cursor slot, it wraps the cursor. This is only visible when disableAnimation=false
    panel: "", // The panel slot, it wraps the tab panel (content)
    tabWrapper: "", // The tab wrapper slot, it wraps the tab and the tab content
  };

  return (
    <HeroTabs
      items={tabs}
      variant={variant}
      color={color}
      radius={radius}
      classNames={{
        ...defaultClassNames,
        ...customClassNames,
      }}
      {...props}
    >
      {tabs.map((tab) => (
        <Tab
          key={tab.key}
          title={
            <span className={cn("group-data-[selected=true]:!text-blue-500", tab.titleClassName)}>
              {t(tab.title)}
              {tab.badge && (
                <span className="absolute -top-2 -right-4 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {tab.badge}
                </span>
              )}
              {tab.endIcon && tab.endIcon}
            </span>
          }
        >
          {tab.content}
        </Tab>
      ))}
    </HeroTabs>
  );
};

export default Tabs;
