import { But<PERSON> } from "@heroui/button";
import { SimpleArea<PERSON>hart } from "./charts/SimpleAreaChart";
import { stressData } from "@/config/constants";
import { useGetFitbitProfile } from "@/hooks/home-dashboard/useMobileHealth";
export const DailyStressCard = () => {
  const { data: fitbitProfile } = useGetFitbitProfile();
  return (
    <div className="col-span-1 sm:col-span-6">
      <div className="w-full h-full p-2 sm:p-3 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-slate-950">
        <div className="flex items-center justify-between">
          <h3 className="text-xs sm:text-sm font-medium mb-2 text-gray-900 dark:text-gray-100">Daily Stress Tracking</h3>
          <Button
            size="sm"
            className="text-[10px] sm:text-xs gap-1 text-orange-300 bg-[#7C389B] hover:bg-[#5f2c76] dark:bg-[#7C389B]/80 dark:hover:bg-[#5f2c76]/80 px-1.5 sm:px-2 py-1"
          >
            <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M8.69544 4.49728C7.65841 3.45987 6.94861 1.6483 6.59636 0C6.24356 1.64864 5.53451 3.46059 4.49711 4.49799C3.46008 5.53468 1.64796 6.24448 0 6.59745C1.64864 6.9497 3.46042 7.65908 4.49745 8.69653C5.53447 9.73355 6.24427 11.5455 6.59707 13.1938C6.94932 11.5452 7.65892 9.73322 8.69577 8.69581C9.7328 7.65879 11.5449 6.94932 13.1929 6.59636C11.5446 6.2441 9.73284 5.53434 8.69544 4.49728Z"
                fill="url(#paint0_linear_1022_31082)"
              />
              <path
                d="M15.4311 12.8039C14.8121 12.1846 14.3873 11.1018 14.1769 10.1172C13.9661 11.1018 13.5428 12.1846 12.9231 12.8043C12.3034 13.4233 11.2208 13.8477 10.2363 14.0585C11.2211 14.2692 12.3034 14.6929 12.9231 15.3126C13.5428 15.932 13.9668 17.0148 14.1776 17.9994C14.388 17.0144 14.8121 15.932 15.4314 15.3123C16.0507 14.6929 17.1335 14.2689 18.1178 14.0578C17.1336 13.8473 16.0508 13.4233 15.4311 12.8039Z"
                fill="url(#paint1_linear_1022_31082)"
              />
              <defs>
                <linearGradient id="paint0_linear_1022_31082" x1="6.59644" y1="0" x2="6.59644" y2="13.1938" gradientUnits="userSpaceOnUse">
                  <stop stopColor="#7D5191" />
                  <stop offset="1" stopColor="#FFD52B" />
                </linearGradient>
                <linearGradient id="paint1_linear_1022_31082" x1="14.1771" y1="10.1172" x2="14.1771" y2="17.9994" gradientUnits="userSpaceOnUse">
                  <stop stopColor="#7D5191" />
                  <stop offset="1" stopColor="#FFD52B" />
                </linearGradient>
              </defs>
            </svg>
            Enable AI Analysis
          </Button>
        </div>
        <SimpleAreaChart data={fitbitProfile?.stressData || stressData} type="stress" />
      </div>
    </div>
  );
};
