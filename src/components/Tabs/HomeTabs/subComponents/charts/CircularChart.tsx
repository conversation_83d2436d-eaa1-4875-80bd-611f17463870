"use client";

import React from "react";

interface CircularChartProps {
  type: "steps" | "spo2" | "calories" | "heartRate" | "storage";
  value: number;
  maxValue?: number;
  unit?: string;
  icon?: React.ReactNode;
  size?: "sm" | "md" | "lg";
}

export const CircularChart: React.FC<CircularChartProps> = ({ type, value, maxValue, unit, icon, size = "sm" }) => {
  const percentage = maxValue ? (value / maxValue) * 100 : value;

  const sizeClasses = {
    sm: "w-20 h-20",
    md: "w-32 h-32",
    lg: "w-40 h-40",
  };

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-lg",
  };

  const getLabel = () => {
    switch (type) {
      case "steps":
        return `${value}/${maxValue}`;
      case "spo2":
        return `${value}%`;
      case "calories":
        return `${value}Calories`;
      case "heartRate":
        return `${value} BPM`;
      case "storage":
        return `${value}/${maxValue} Requests`;
      default:
        return value;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div className={`relative ${sizeClasses[size]}`}>
        <svg className="w-full h-full" viewBox="0 0 100 100">
          {/* Background circle */}
          <circle className="stroke-gray-700" strokeWidth="8" fill="none" cx="50" cy="50" r="40" />
          {/* Progress circle */}
          <circle
            className="stroke-blue-500 transition-all duration-300"
            strokeWidth="8"
            strokeLinecap="round"
            fill="none"
            cx="50"
            cy="50"
            r="40"
            strokeDasharray={`${percentage * 2.51} 251`}
            transform="rotate(-90 50 50)"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          {icon ? (
            React.cloneElement(icon as React.ReactElement<{ className?: string }>, {
              className: "w-5 h-5 text-blue-500",
            })
          ) : (
            <span className={`font-medium ${textSizeClasses[size]}`}>
              {getLabel()}
            </span>
          )}
        </div>
      </div>
      {icon && (
        <div className="mt-1 text-center">
          <span className={`font-medium ${textSizeClasses[size]}`}>{getLabel()}</span>
        </div>
      )}
    </div>
  );
};
