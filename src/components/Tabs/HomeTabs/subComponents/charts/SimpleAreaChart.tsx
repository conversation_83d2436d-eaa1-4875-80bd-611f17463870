import { Area, Area<PERSON>hart, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON> } from "recharts";
import { useTheme } from "next-themes";

export const SimpleAreaChart = ({ data, type }: { data: any[]; type: string }) => {
  const { theme } = useTheme();
  const isDarkTheme = theme === "dark";

  return (
    <ResponsiveContainer width="100%" height={200}>
      <AreaChart data={data} margin={{ top: 10, right: 0, left: 0, bottom: 0 }}>
        <defs>
        <linearGradient id={`color${type}`} x1="0" y1="0" x2="0" y2="1">
          <stop
            offset="5%"
            stopColor={type === "sleep" ? "#7393e0" : type === "active" ? "#7393e0" : type === "stress" ? "#ef4444" : "#ef4444"}
            stopOpacity={0.8}
          />
          <stop
            offset="95%"
            stopColor={type === "sleep" ? "#8B5CF6" : type === "active" ? "#8B5CF6" : type === "stress" ? "#f87171" : "#f87171"}
            stopOpacity={0}
          />
        </linearGradient>
      </defs>
      <XAxis dataKey={type === "stress" ? "time" : "month"} tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
      <Tooltip 
        contentStyle={{
          backgroundColor: "#060C1B",
          borderColor: "#060C1B",
          borderRadius: "8px",
          borderWidth: "1px",
          borderStyle: "solid",
        }}
        labelStyle={{
          color: isDarkTheme ? "#8B5CF6" : "#6366f1"
        }}
        itemStyle={{
          color: isDarkTheme ? "#3b82f6" : "#2563eb"
        }}
      />
      <Area
        type="monotone"
        dataKey={type === "sleep" ? "sleep" : type === "bloodPressure" ? "bloodPressure" : "value"} 
        stroke={type === "sleep" || type === "active" ? "#3b82f6" : "#ef4444"}
        fillOpacity={1}
        fill={`url(#color${type})`}
      />
    </AreaChart>
    </ResponsiveContainer>
  );
};
