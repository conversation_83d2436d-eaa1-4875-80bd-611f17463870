import { Button } from "@heroui/button";
import { MoveRight, RefreshCw } from "lucide-react";
import React from "react";
import { useGetFitbitProfile, useHandleFitbitAuth } from "@/hooks/home-dashboard/useMobileHealth";
import { useQueryClient } from "@tanstack/react-query";

export default function FitbitProfile() {
  const queryClient = useQueryClient();

  const { data: fitbitProfile, isLoading, isError } = useGetFitbitProfile();
  const { mutate: handleFitbitAuth } = useHandleFitbitAuth();

  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ["fitbit-profile"] });
  };
  return (
    <section>
      {!fitbitProfile ? (
        <div className="w-full flex items-center justify-center px-2 my-2 sm:my-3">
          <Button
            size="lg"
            className="w-full sm:w-fit flex items-center justify-center bg-white/5 hover:bg-white/10 dark:bg-slate-950 dark:hover:bg-gray-800/75 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white rounded-2xl p-2 sm:p-4"
            onPress={() => handleFitbitAuth()}
          >
            <div className="flex items-center gap-2">
              <img src="/images/fitbit.png" alt="Fitbit" className="h-5 w-12 sm:h-6 sm:w-16 object-contain" />
              <span className="text-xs sm:text-sm">Connect to Fitbit & provide access</span>
            </div>
            <MoveRight className="h-4 w-4 sm:h-5 sm:w-5 ml-2" />
          </Button>
        </div>
      ) : (
        <div className="w-full flex items-center justify-between px-2 my-2 sm:my-3">
          <div className="flex items-center gap-2 text-green-500 text-sm">
            <img src="/images/fitbit.png" alt="Fitbit" className="h-5 w-12 sm:h-6 sm:w-16 object-contain" />
            <span>Connected to Fitbit</span>
          </div>
          <Button size="sm" variant="bordered" onPress={handleRefresh} disabled={isLoading} className="flex items-center gap-2">
            <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            <span>Refresh Data</span>
          </Button>
        </div>
      )}
    </section>
  );
}
