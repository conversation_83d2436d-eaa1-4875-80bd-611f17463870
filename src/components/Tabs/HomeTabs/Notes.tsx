"use client";
import { FormInput, FormTextArea } from "@/components/Forms/commons";
import SubmitButton from "@/components/SubmitButton";
import { useCreateNote, useDeleteNote, useGetNotes, useUpdateNote } from "@/hooks/home-dashboard/useNotes";
import { NoteFormType, NoteFormValidation } from "@/lib/utils/validations";
import { Button, Card, CardBody, Modal, ModalBody, ModalContent, ModalHeader, useDisclosure } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { useState } from "react";
import TabContainer from "./containers/TabContainer";

const Notes = () => {
  const { mutate: createNote } = useCreateNote();
  const { mutate: deleteNote } = useDeleteNote();
  const { mutate: updateNote } = useUpdateNote();
  const { notes, isLoading, error } = useGetNotes();

  // State to track the currently selected note for viewing/editing
  const [selectedNote, setSelectedNote] = useState<Notes | null>(null);

  // Single form for both adding and editing notes
  const form = useForm<NoteFormType>({
    shouldUnregister: false,
    resolver: zodResolver(NoteFormValidation),
    mode: "onSubmit",
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = form;

  const onSubmit = (data: NoteFormType) => {
    if (selectedNote?.id) {
      updateNote([
        {
          title: data.title,
          description: data.description,
          id: selectedNote.id,
        },
      ]);
    } else {
      createNote([
        {
          title: data.title,
          description: data.description,
        },
      ]);
    }
    handleCloseModal();
  };

  // Modal handling
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleOpenModal = (note?: Notes) => {
    if (note) {
      setSelectedNote(note);
      reset({
        title: note.title,
        description: note.description,
      });
    } else {
      setSelectedNote(null);
      reset({
        title: "",
        description: "",
      });
    }
    onOpen();
  };

  const handleCloseModal = () => {
    setSelectedNote(null);
    reset();
    onClose();
  };

  const handleDeleteNote = () => {
    if (selectedNote?.id) {
      deleteNote(selectedNote.id);
      handleCloseModal();
    }
  };

  return (
    <TabContainer tabId="NOTES">
      <div className="flex flex-col w-full p-2">
        <div className="flex justify-between w-full mb-4">
          <h1 className="blue-heading">My Medical Notes</h1>
          <Button size="sm" color="primary" onPress={() => handleOpenModal()}>
            Add Note
          </Button>
        </div>

        {/* Notes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {notes?.map((note: Notes) => (
            <Button key={note.id} onPress={() => handleOpenModal(note)} className="p-0 h-auto bg-transparent">
              <Card className="w-full dark:bg-[#060C1B] border rounded-xl dark:border-slate-800">
                <CardBody>
                  <h2 className="font-semibold truncate">{note.title}</h2>
                  <p className="mt-2 line-clamp-3">{note.description}</p>
                </CardBody>
              </Card>
            </Button>
          ))}
        </div>

        {/* Single Modal for Create/Edit */}
        <FormProvider {...form}>
          <Modal className="dark:bg-[#060C1B]" isOpen={isOpen} onOpenChange={handleCloseModal}>
            <ModalContent>
              <ModalHeader>
                <h2>{selectedNote ? "Edit Note" : "Create Note"}</h2>
              </ModalHeader>
              <ModalBody>
                <form onSubmit={handleSubmit(onSubmit)}>
                  <FormInput control={control} name="title" label="Title" />
                  <FormTextArea control={control} name="description" label="Description" />
                  <div className="flex w-full justify-end space-x-2 mt-4">
                    {selectedNote && (
                      <SubmitButton
                        type="button"
                        variant="solid"
                        className="bg-red-700 hover:bg-red-800 text-white text-xs"
                        label="Delete"
                        onClick={handleDeleteNote}
                      />
                    )}
                    <SubmitButton type="submit" variant="solid" color="primary" label={selectedNote ? "Update" : "Add"} />
                  </div>
                </form>
              </ModalBody>
            </ModalContent>
          </Modal>
        </FormProvider>
      </div>
    </TabContainer>
  );
};

export default Notes;
