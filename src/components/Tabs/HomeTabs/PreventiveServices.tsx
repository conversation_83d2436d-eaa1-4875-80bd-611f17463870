import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, B<PERSON>crumbItem } from "@heroui/react";
import { Check, X, ChevronLeft, ChevronRight, Clock, User, Calendar } from "lucide-react";
import Image from "next/image";
// import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "../ui/breadcrumb";
import { useSearchParams } from "next/navigation";
import GenomicPackages from "@/components/packages/genomic/GenomicPackages";
import MultidisciplinaryPackages from "@/components/packages/MultidisciplinaryPackages";
import AlzheimersDementiaDetails from "@/components/packages/AlzheimersDementiaDetails";
import CancerScreeningDetails from "@/components/packages/CancerScreeningDetails";
import DiabetesScreeningDetails from "@/components/packages/DiabetesScreeningDetails";
import LongevityPackages from "@/components/packages/LongevityPackages";
import TabContainer from "./containers/TabContainer";

// Add this type for path tracking
type Path = "Services" | "packages" | "doctors" | "appointment" | "payment" | "success";

// Add this type near the top with other type definitions
type ServiceCategory =
  | "Multidisciplinary"
  | "Genomic Services"
  | "Longevity & Wellness"
  | "Alzheimer's/ Dementia Screening"
  | "Cancer Screening"
  | "Diabetes Screening";

const PreventiveServices = () => {
  const [showPackages, setShowPackages] = useState(false);
  const [showDoctors, setShowDoctors] = useState(false);
  const [showAppointment, setShowAppointment] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [currentPath, setCurrentPath] = useState<Path>("Services");
  const [showPayment, setShowPayment] = useState(false);
  const [selectedTime, setSelectedTime] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);
  const [selectedService, setSelectedService] = useState<ServiceCategory | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [showPackageDetails, setShowPackageDetails] = useState(false);

  const searchParams = useSearchParams();

  useEffect(() => {
    // Check for directToService when component mounts or searchParams changes
    const directService = localStorage.getItem("directToService");
    if (directService) {
      setTimeout(() => {
        handleExplorePackages(directService as ServiceCategory);
        localStorage.removeItem("directToService");
      }, 0);
    }
  }, [searchParams]); // Re-run effect when searchParams changes

  const handleExplorePackages = (service: ServiceCategory | string) => {
    resetToInitialState();

    // Convert the service string to match our ServiceCategory type
    let serviceCategory: ServiceCategory;
    switch (service) {
      case "Genomic Services":
        serviceCategory = "Genomic Services";
        setSelectedService(serviceCategory);
        setShowPackages(true);
        setCurrentPath("packages");
        break;

      case "Multidisciplinary":
        serviceCategory = "Multidisciplinary";
        setSelectedService(serviceCategory);
        setShowPackages(true);
        setCurrentPath("packages");
        break;

      case "Longevity & Wellness":
        serviceCategory = "Longevity & Wellness";
        setSelectedService(serviceCategory);
        setShowPackages(true);
        setCurrentPath("packages");
        break;

      default:
        serviceCategory = service as ServiceCategory;
        setSelectedService(serviceCategory);
        setShowPackages(true);
        setCurrentPath("packages");
    }
  };

  const handleBackToServices = () => {
    if (selectedPackage) {
      setSelectedPackage(null);
      setShowPackageDetails(false);
      setCurrentPath("packages");
    } else {
      resetToInitialState();
    }
  };

  const handleChoosePackage = () => {
    setShowDoctors(true);
    setCurrentPath("doctors");
  };

  const handleBackToPackages = () => {
    setShowDoctors(false);
    setCurrentPath("packages");
  };

  const handleBookAppointment = (doctor: any) => {
    setSelectedDoctor(doctor);
    setShowAppointment(true);
    setCurrentPath("appointment");
  };

  const handleBackToDoctors = () => {
    setShowAppointment(false);
    setCurrentPath("doctors");
  };

  const handleFinalBooking = () => {
    setShowPayment(true);
    setCurrentPath("payment");
  };

  const handlePayment = () => {
    setShowSuccess(true);
    setCurrentPath("success");
  };

  // Add handler for breadcrumb navigation
  const handleNavigate = (path: Path) => {
    setCurrentPath(path);
    switch (path) {
      case "Services":
        resetToInitialState();
        break;
      case "packages":
        if (selectedPackage) {
          setSelectedPackage(null);
          setShowPackageDetails(false);
        }
        setShowPackages(true);
        setShowDoctors(false);
        setShowAppointment(false);
        setShowPayment(false);
        break;
      case "doctors":
        setShowPackages(true);
        setShowDoctors(true);
        setShowAppointment(false);
        setShowPayment(false);
        break;
      case "appointment":
        setShowPackages(true);
        setShowDoctors(true);
        setShowAppointment(true);
        setShowPayment(false);
        break;
      case "payment":
        setShowPackages(true);
        setShowDoctors(true);
        setShowAppointment(true);
        setShowPayment(true);
        break;
      case "success":
        setShowPackages(true);
        setShowDoctors(true);
        setShowAppointment(true);
        setShowPayment(true);
        setShowSuccess(true);
        break;
    }
  };

  const NavigationBreadcrumb = () => (
    <Breadcrumbs>
      <BreadcrumbItem>
        <span
          onClick={() => handleNavigate("Services")}
          className={`text-xs cursor-pointer ${currentPath === "Services" ? "text-gray-900 dark:text-white" : "text-gray-500 dark:text-gray-400"}`}
        >
          Preventive/ Services
        </span>
      </BreadcrumbItem>
      {(currentPath === "packages" || currentPath === "doctors" || currentPath === "appointment" || currentPath === "payment" || selectedPackage) && (
        <BreadcrumbItem>
          <span
            onClick={() => handleNavigate("packages")}
            className={`text-xs cursor-pointer ${
              currentPath === "packages" && !selectedPackage ? "text-gray-900 dark:text-white" : "text-gray-500 dark:text-gray-400"
            }`}
          >
            {selectedService === "Alzheimer's/ Dementia Screening" || selectedService === "Cancer Screening" || selectedService === "Diabetes Screening"
              ? "Learn More"
              : `${selectedService} Packages`}
          </span>
        </BreadcrumbItem>
      )}
      {selectedService === "Genomic Services" && selectedPackage && (
        <BreadcrumbItem>
          <span className="text-xs text-gray-900 dark:text-white">Package Details</span>
        </BreadcrumbItem>
      )}
      {selectedService !== "Genomic Services" && selectedPackage && (
        <BreadcrumbItem>
          <span className="text-xs text-gray-900 dark:text-white">
            {selectedPackage === "women-essential" ? "Essential Wellness Package for Women" : selectedPackage}
          </span>
        </BreadcrumbItem>
      )}
      {(currentPath === "doctors" || currentPath === "appointment" || currentPath === "payment") && (
        <BreadcrumbItem>
          <span
            onClick={() => handleNavigate("doctors")}
            className={`text-xs cursor-pointer ${currentPath === "doctors" ? "text-gray-900 dark:text-white" : "text-gray-500 dark:text-gray-400"}`}
          >
            Doctors
          </span>
        </BreadcrumbItem>
      )}
      {(currentPath === "appointment" || currentPath === "payment") && (
        <BreadcrumbItem>
          <span
            onClick={() => handleNavigate("appointment")}
            className={`text-xs cursor-pointer ${currentPath === "appointment" ? "text-gray-900 dark:text-white" : "text-gray-500 dark:text-gray-400"}`}
          >
            Appointment
          </span>
        </BreadcrumbItem>
      )}
      {currentPath === "payment" && (
        <BreadcrumbItem>
          <span className="text-xs text-gray-900 dark:text-white">Payment</span>
        </BreadcrumbItem>
      )}
      {currentPath === "success" && (
        <BreadcrumbItem>
          <span className="text-xs text-gray-900 dark:text-white">Payment Successful</span>
        </BreadcrumbItem>
      )}
    </Breadcrumbs>
  );

  // Add a reset function to handle returning to initial state
  const resetToInitialState = () => {
    setShowPackages(false);
    setShowDoctors(false);
    setShowAppointment(false);
    setShowPayment(false);
    setShowSuccess(false);
    setCurrentPath("Services");
    setSelectedDoctor(null);
    setSelectedTime("");
    setSelectedService(null);
    setSelectedPackage(null);
    setShowPackageDetails(false);
  };

  return (
    <TabContainer tabId="PREVENTIVE_SERVICES">
      <div className="flex flex-col gap-2 p-2">
      {showPackages && (
        <div className="mb-4">
          <NavigationBreadcrumb />
        </div>
      )}

      {!showPackages ? (
        <>
          <h2 className="text-sm font-medium text-blue-700 dark:text-blue-400">Preventive/ Services</h2>
          <p className="text-xs text-gray-600 dark:text-gray-400 mb-4 text-justify">
            Preventive services are healthcare measures that aim to detect potential health issues early or prevent diseases before they occur, encompassing
            screenings, vaccinations, and health education. The packages offered herein are currently geographically specific to the Hospital / Clinic offering
            these Services/ Packages.
          </p>
          <div className="flex flex-col gap-1"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Alzheimer's/Dementia Screening */}
            <Card className="p-4 dark:bg-slate-900 flex flex-col">
              <h3 className="font-medium text-xs mb-2 text-gray-900 dark:text-gray-100">Alzheimer's/ Dementia Screening</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-4 text-justify">
                Alzheimer's/Dementia Screening involves the use of cognitive tests, neurological assessments, and sometimes brain imaging to detect early signs
                of cognitive impairment, aiding in the diagnosis of Alzheimer's disease or other forms of dementia.
              </p>
              <div className="flex justify-center items-end mt-auto">
                <Button
                  size="sm"
                  className="w-full md:w-fit bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-4 rounded-md transition-colors"
                  onClick={() => handleExplorePackages("Alzheimer's/ Dementia Screening")}
                >
                  Learn More
                </Button>
              </div>
            </Card>

            {/* Cancer Screening */}
            <Card className="p-4 dark:bg-slate-900 flex flex-col">
              <h3 className="font-medium text-xs mb-2 text-gray-900 dark:text-gray-100">Cancer Screening</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-4 text-justify">
                Cancer Screening involves the use of specific tests or examinations to detect cancer in people who do not have symptoms, aiming for early
                detection when treatment is most effective
              </p>
              <div className="flex justify-center items-end mt-auto">
                <Button
                  size="sm"
                  className="w-full md:w-fit bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-4 rounded-md transition-colors"
                  onClick={() => handleExplorePackages("Cancer Screening")}
                >
                  Learn More
                </Button>
              </div>
            </Card>

            {/* Diabetes Screening */}
            <Card className="p-4 dark:bg-slate-900 flex flex-col">
              <h3 className="font-medium text-xs mb-2 text-gray-900 dark:text-gray-100">Diabetes Screening</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-4 text-justify">
                Diabetes Screening involves testing individuals for high blood glucose levels to identify prediabetes, type 1, or type 2 diabetes early,
                enabling timely intervention and management.
              </p>
              <div className="flex justify-center items-end mt-auto">
                <Button
                  size="sm"
                  className="w-full md:w-fit bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-4 rounded-md transition-colors"
                  onClick={() => handleExplorePackages("Diabetes Screening")}
                >
                  Learn More
                </Button>
              </div>
            </Card>

            {/* Genomic Services */}
            <Card className="p-4 dark:bg-slate-900 flex flex-col">
              <h3 className="font-medium text-xs mb-2 text-gray-900 dark:text-gray-100">Genomic Services</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-4 text-justify">
                Genomic Services refer to the range of laboratory and analytical offerings that involve sequencing, analyzing, and interpreting an organism's
                complete set of DNA to provide insights into genetics, health, ancestry or biological functions.
              </p>

              <div className="flex justify-center items-end mt-auto">
                <Button
                  size="sm"
                  className="w-full md:w-fit bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-4 rounded-md transition-colors"
                  onClick={() => handleExplorePackages("Genomic Services")}
                >
                  Explore Packages
                </Button>
              </div>
            </Card>

            {/* Longevity & Wellness */}
            <Card className="p-4 dark:bg-slate-900 flex flex-col">
              <h3 className="font-medium text-xs mb-2 text-gray-900 dark:text-gray-100">Longevity & Wellness</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-4 text-justify">
                A professional focused on promoting healthy aging and optimizing physical, mental, and emotional well-being through personalized lifestyle and
                Preventive care strategies.
              </p>
              <div className="flex justify-center items-end mt-auto">
                <Button
                  size="sm"
                  className="w-full md:w-fit bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-4 rounded-md transition-colors"
                  onClick={() => handleExplorePackages("Longevity & Wellness")}
                >
                  Explore Packages
                </Button>
              </div>
            </Card>

            {/* Multidisciplinary */}
            <Card className="p-4 dark:bg-slate-900  flex flex-col">
              <h3 className="font-medium text-xs mb-2 text-gray-900 dark:text-gray-100">Multidisciplinary</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-4 text-justify">
                A medical doctor who provides primary care, diagnosing and treating a wide range of illnesses and managing overall health for patients of all
                ages.
              </p>
              <div className="flex justify-center items-end mt-auto">
                <Button
                  size="sm"
                  className="w-full md:w-fit bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-4 rounded-md transition-colors"
                  onClick={() => handleExplorePackages("Multidisciplinary")}
                >
                  Explore Packages
                </Button>
              </div>
            </Card>
          </div>
        </>
      ) : !showDoctors ? (
        <div className="space-y-4">
          {selectedService === "Longevity & Wellness" ? (
            <LongevityPackages
              onChoosePackage={handleChoosePackage}
              onSelectPackage={(packageType: string) => {
                setSelectedPackage(packageType);
                setShowPackageDetails(true);
                setCurrentPath("packages");
              }}
              showPackageDetails={showPackageDetails}
            />
          ) : selectedService === "Multidisciplinary" ? (
            <MultidisciplinaryPackages
              onChoosePackage={handleChoosePackage}
              onSelectPackage={(packageType: string) => {
                setSelectedPackage(packageType);
                setShowPackageDetails(true);
                setCurrentPath("packages");
              }}
              showPackageDetails={showPackageDetails}
            />
          ) : selectedService === "Genomic Services" ? (
            <GenomicPackages
              onSelectPackage={(packageType: string) => {
                setSelectedPackage(packageType);
                setShowPackageDetails(true);
                setCurrentPath("packages");
              }}
              showPackageDetails={showPackageDetails}
            />
          ) : selectedService === "Alzheimer's/ Dementia Screening" ? (
            <AlzheimersDementiaDetails onChoosePackage={handleChoosePackage} />
          ) : selectedService === "Cancer Screening" ? (
            <CancerScreeningDetails onChoosePackage={handleChoosePackage} />
          ) : selectedService === "Diabetes Screening" ? (
            <DiabetesScreeningDetails onChoosePackage={handleChoosePackage} />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Basic Monitoring */}
              <div className="rounded-2xl bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 p-6 space-y-6">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Basic Monitoring</h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">3 Hrs uninterrupted Video Chat with Specialist.</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">e-Prescriptions and Diagnosis analysis.</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <X className="h-4 w-4 text-red-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">Follow-ups after a week with updates</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <X className="h-4 w-4 text-red-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">Advanced Gene analysis and consultations</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <p className="text-xs text-gray-900 dark:text-white">All Inclusive Monthly Fee: $xyz</p>
                  <Button size="sm" className="w-full bg-blue-600 text-white hover:bg-blue-700 text-xs" onClick={handleChoosePackage}>
                    Choose Package
                  </Button>
                </div>
              </div>

              {/* Monthly Care */}
              <div className="rounded-2xl bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 p-6 space-y-6">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Monthly Care</h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">3 Hrs uninterrupted Video Chat with Specialist.</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">e-Prescriptions and Diagnosis analysis.</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">Follow-ups after a week with updates</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <X className="h-4 w-4 text-red-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">Advanced Gene analysis and consultations</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <p className="text-xs text-gray-900 dark:text-white">All Inclusive Monthly Fee: $xyz</p>
                  <Button size="sm" className="w-full bg-blue-600 text-white hover:bg-blue-700 text-xs" onClick={handleChoosePackage}>
                    Choose Package
                  </Button>
                </div>
              </div>

              {/* Premium Care */}
              <div className="rounded-2xl bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 p-6 space-y-6">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Premium Care</h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">3 Hrs uninterrupted Video Chat with Specialist.</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">e-Prescriptions and Diagnosis analysis.</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">Follow-ups after a week with updates</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-500 mt-1" />
                    <span className="text-xs text-gray-600 dark:text-gray-200">Advanced Gene analysis and consultations</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <p className="text-xs text-gray-900 dark:text-white">All Inclusive Monthly Fee: $xyz</p>
                  <Button size="sm" className="w-full bg-blue-600 text-white hover:bg-blue-700 text-xs" onClick={handleChoosePackage}>
                    Choose Package
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      ) : !showAppointment ? (
        <div className="space-y-4">
          <h2 className="text-sm font-semibold text-white">Select a Specialist for your consultation:</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Doctor Card */}
            {[1, 2, 3, 4, 5, 6].map((index) => (
              <div key={index} className="rounded-2xl bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-transparent p-6 space-y-4">
                <div className="aspect-square rounded-lg overflow-hidden">
                  <Image src={`/images/doctor-${(index % 4) + 1}.png`} alt="Doctor" className="w-full h-full object-contain" width={100} height={100} />
                </div>

                <div className="space-y-2">
                  <h3 className="text-xs font-semibold text-gray-900 dark:text-white">Dr. Lenna Hamilton</h3>

                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <span key={star} className="text-yellow-400 text-xs">
                        ★
                      </span>
                    ))}
                    <span className="text-xs text-gray-600 dark:text-gray-200 ml-1">4.7</span>
                  </div>

                  <div className="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1">
                    <span className="text-xs text-gray-700 dark:text-gray-200">Consultant Cardiology</span>
                  </div>
                </div>

                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs" onClick={handleBookAppointment}>
                  Book Appointment
                </Button>
              </div>
            ))}
          </div>
        </div>
      ) : !showPayment ? (
        <div className="space-y-4">
          <h2 className="text-sm font-semibold text-gray-900 dark:text-white">Select appointment slot for consultation:</h2>

          <div className="rounded-2xl bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-transparent p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left side - Doctor info and time slots */}
              <div className="space-y-4">
                <h3 className="text-sm text-gray-900 dark:text-white">Appointment</h3>
                <div className="text-gray-600 dark:text-gray-400 text-xs">R.A.V.I.D. Dashboard</div>

                <div className="flex items-center gap-4">
                  <Image src="/images/doctor-1.png" alt="Doctor" width={80} height={80} className="w-16 h-16 rounded-full object-cover" />
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-gray-900 dark:text-white">
                      <User className="h-4 w-4" />
                      <span className="text-sm">Dr. Lenna Hamilton</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <Clock className="h-4 w-4" />
                      <span className="text-xs">08:15 PM - 10:15 PM</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-xs text-gray-600 dark:text-gray-400">Select Slot:</label>
                  <div className="grid grid-cols-2 gap-2">
                    {["06:15 PM", "08:15 PM", "08:30 PM", "09:00 PM", "09:15 PM", "09:30 PM"].map((time) => (
                      <Button key={time} size="sm" variant="flat" className="w-full text-xs">
                        {time}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Right side - Calendar */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <select className="bg-transparent text-gray-900 dark:text-white text-xs">
                    <option>Jan 2025</option>
                  </select>
                  <select className="bg-transparent text-gray-900 dark:text-white text-xs">
                    <option>UTC +3:00 Moscow</option>
                  </select>
                </div>

                <div className="flex justify-between items-center text-gray-600 dark:text-gray-400 text-xs">
                  <Button variant="ghost" size="sm" className="w-full md:w-auto">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <div className="grid grid-cols-7 gap-4 text-center">
                    <span>S</span>
                    <span>M</span>
                    <span>T</span>
                    <span>W</span>
                    <span>TH</span>
                    <span>F</span>
                    <span>ST</span>
                  </div>
                  <Button variant="ghost" size="sm" className="w-full md:w-auto">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-7 gap-2 text-center">
                  {Array.from({ length: 31 }, (_, i) => (
                    <Button
                      key={i + 1}
                      variant="ghost"
                      className={`text-xs w-full ${i + 1 === 6 ? "ring-2 ring-blue-500" : ""} 
                      ${[1, 2, 5, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30].includes(i + 1) ? "text-blue-500" : "text-gray-500"}`}
                    >
                      {i + 1}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-6">
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleFinalBooking}>
                Book Appointment
              </Button>
            </div>
          </div>
        </div>
      ) : !showSuccess ? (
        <div className="space-y-4">
          <h2 className="text-sm font-semibold text-gray-900 dark:text-white">Appointment Summary & Payments</h2>

          <div className="rounded-2xl bg-white dark:bg-gray-800/50 p-6 border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left side - Appointment Details */}
              <div className="space-y-6">
                <h3 className="text-sm text-gray-900 dark:text-white">Appointment Details:</h3>

                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Image src="/images/doctor-1.png" alt="Doctor" width={80} height={80} className="w-16 h-16 rounded-full object-cover" />
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-gray-900 dark:text-white">
                        <User className="h-4 w-4" />
                        <span className="text-sm">Dr. Lenna Hamilton</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <Clock className="h-4 w-4" />
                      <span className="text-xs">08:15 PM - 10:15 PM</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <Calendar className="h-4 w-4" />
                      <span className="text-xs">06 Jan, 2025</span>
                    </div>
                  </div>

                  <div className="text-gray-900 dark:text-white">
                    <p className="text-sm">Total Fees: $135.50</p>
                  </div>
                </div>
              </div>

              {/* Right side - Payment Form */}
              <div className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-xs text-gray-600 dark:text-gray-400">Name of Card Holder</label>
                    <input
                      type="text"
                      className="w-full mt-1 p-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white text-xs border border-gray-300 dark:border-gray-600"
                      placeholder="Enter cardholder name"
                    />
                  </div>

                  <div>
                    <label className="text-xs text-gray-600 dark:text-gray-400">Card Number</label>
                    <input
                      type="text"
                      className="w-full mt-1 p-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white text-xs border border-gray-300 dark:border-gray-600"
                      placeholder="1234 - - - - - - - -"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-xs text-gray-600 dark:text-gray-400">CCV</label>
                      <input
                        type="text"
                        className="w-full mt-1 p-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white text-xs border border-gray-300 dark:border-gray-600"
                        placeholder="- - -"
                        maxLength={3}
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-600 dark:text-gray-400">Expiry Date</label>
                      <input
                        type="text"
                        className="w-full mt-1 p-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white text-xs border border-gray-300 dark:border-gray-600"
                        placeholder="MM-YYYY"
                      />
                    </div>
                  </div>
                </div>

                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handlePayment}>
                  Proceed to Pay
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <h2 className="text-sm font-semibold text-gray-900 dark:text-white">Payment Successful</h2>

          <div className="rounded-2xl border dark:bg-gray-800/50 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left side - Appointment Details */}
              <div className="space-y-6">
                <h3 className="text-sm text-gray-900 dark:text-white">Appointment Details</h3>

                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Image src="/images/doctor-1.png" alt="Doctor" width={80} height={80} className="w-16 h-16 rounded-full object-cover" />
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-gray-900 dark:text-white">
                        <User className="h-4 w-4" />
                        <span className="text-sm">Dr. Lenna Hamilton</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <Clock className="h-4 w-4" />
                      <span className="text-xs">08:15 PM - 10:15 PM</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <Calendar className="h-4 w-4" />
                      <span className="text-xs">06 Jan, 2025</span>
                    </div>
                    <div className="flex items-center gap-2 text-green-600 dark:text-green-500">
                      <Check className="h-4 w-4" />
                      <span className="text-xs">Appointment Booked</span>
                    </div>
                  </div>

                  <div className="text-gray-900 dark:text-white">
                    <p className="text-sm">Total Fees: $135.50</p>
                  </div>
                </div>
              </div>

              {/* Right side - Success Image */}
              <div className="flex flex-col items-center justify-center">
                <h2 className="text-sm font-bold text-gray-900 dark:text-white mb-4">Appointment Booked !</h2>
                <Image src="/images/pana.png" alt="Payment Success" width={200} height={200} className="object-contain" />
              </div>
            </div>

            <div className="mt-6">
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-xs text-white" onClick={resetToInitialState}>
                Go to My R.A.V.I.D. Dashboard
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </TabContainer>
  );
};

export default PreventiveServices;
