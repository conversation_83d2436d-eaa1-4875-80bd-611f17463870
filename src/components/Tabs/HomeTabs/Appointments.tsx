import AlertComponent from "@/components/Disclaimers/Alert";
import { FormInput, FormTextArea } from "@/components/Forms/commons";
import { Appointment, useAppointmentAvailability } from "@/hooks/use-appointments";
import { AppointmentFormType } from "@/lib/utils/validations";
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import interactionPlugin from "@fullcalendar/interaction";
import FullCalendar from "@fullcalendar/react";
import { Button, Modal, ModalBody, ModalContent, ModalFooter, ModalHeader, TimeInput } from "@heroui/react";
import { Time } from "@internationalized/date";
import { BellIcon, CheckCircleIcon, XCircleIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useStore } from "@/store/store";
import TabContainer from "./containers/TabContainer";
// Helper function to parse time string HH:mm or HH:mm:ss to Time object
const parseTimeString = (timeString: string | undefined): Time | null => {
  if (!timeString) return null;
  const parts = timeString.split(":").map(Number);
  // Ensure at least hour and minute are present and valid numbers
  if (parts.length >= 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
    // Handle potential out-of-bounds values gracefully if needed
    const hour = Math.max(0, Math.min(23, parts[0]));
    const minute = Math.max(0, Math.min(59, parts[1]));
    return new Time(hour, minute);
  }
  return null;
};

// Helper function to format a time object to HH:mm string
const formatTimeObject = (timeValue: { hour: number; minute: number } | null): string => {
  if (!timeValue || typeof timeValue.hour !== "number" || typeof timeValue.minute !== "number") return "";
  return `${String(timeValue.hour).padStart(2, "0")}:${String(timeValue.minute).padStart(2, "0")}`;
};

export default function Calendar() {
  const { user } = useStore();
  const [selectedDate, setSelectedDate] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<any>(null);
  const {
    createAppointment,
    isCreatingAppointment,
    appointments,
    updateAppointmentMutation,
    isUpdatingAppointment,
    deleteAppointmentMutation,
    isDeletingAppointment,
  } = useAppointmentAvailability();
  const [upcomingAppointments, setUpcomingAppointments] = useState<Appointment[]>([]);

  // Setup form with default values
  const { control, handleSubmit, reset, setValue, watch } = useForm<AppointmentFormType>({
    defaultValues: {
      title: "",
      notes: "",
      startTime: "10:00",
      endTime: "11:00",
    },
  });

  // Setup edit form
  const { control: editControl, handleSubmit: handleEditSubmit, reset: resetEditForm, setValue: setEditValue, watch: watchEdit } = useForm<
    AppointmentFormType
  >();

  // Process appointments to find upcoming ones
  useEffect(() => {
    if (appointments) {
      const now = new Date();
      const upcoming = appointments
        .filter((app) => app.appointment_type === "manual")
        .filter((app) => new Date(app.start_time) > now) // Filter for future appointments
        .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime()) // Sort by start time
        .slice(0, 3); // Get the next three
      setUpcomingAppointments(upcoming);
    }
  }, [appointments]);

  // Transform API appointments to FullCalendar events format
  const events = appointments
    ? appointments
        // .filter((appointment: Appointment) => appointment.appointment_type === "")
        .map((appointment: Appointment) => ({
          id: appointment.id,
          title: appointment.title,
          start: appointment.start_time,
          end: appointment.end_time,
          extendedProps: {
            notes: appointment.notes,
            location: appointment.location,
            backgroundColor:
              appointment.appointment_type === "manual"
                ? "bg-[#9333ea]" // Purple for manual appointments
                : "bg-[#4f46e5]", // Blue for booking appointments
            meetingLink: appointment.meeting_link,
            appointmentType: appointment.appointment_type,
          },
        }))
    : [];

  const handleDateClick = (info: any) => {
    setSelectedDate(info.dateStr);
    reset(); // Reset form to default values
    setIsDialogOpen(true);
  };

  const handleEventClick = (info: any) => {
    const eventId = info.event.id;
    const appointment = appointments?.find((app) => app.id === eventId);

    if (appointment) {
      setSelectedAppointment(appointment);

      // Parse time from ISO string
      const startDate = new Date(appointment.start_time);
      const endDate = new Date(appointment.end_time);

      const startTime = `${String(startDate.getHours()).padStart(2, "0")}:${String(startDate.getMinutes()).padStart(2, "0")}`;
      const endTime = `${String(endDate.getHours()).padStart(2, "0")}:${String(endDate.getMinutes()).padStart(2, "0")}`;

      // Reset edit form with appointment data
      resetEditForm({
        title: appointment.title,
        notes: appointment.notes || "",
        startTime: startTime,
        endTime: endTime,
      });

      setIsEditDialogOpen(true);
    }
  };

  const onSubmit = (data: AppointmentFormType) => {
    // Create a new date object from the selected date
    const selectedDateObj = new Date(selectedDate);

    // Parse start and end times
    const [startHour, startMinute] = data.startTime.split(":").map(Number);
    const [endHour, endMinute] = data.endTime.split(":").map(Number);

    // Set the times for start and end
    const startDateTime = new Date(selectedDateObj);
    startDateTime.setHours(startHour, startMinute, 0);

    const endDateTime = new Date(selectedDateObj);
    endDateTime.setHours(endHour, endMinute, 0);

    // Create appointment data
    const appointmentData = {
      appointment_type: "manual",
      start_time: startDateTime.toISOString(),
      end_time: endDateTime.toISOString(),
      title: data.title,
      notes: data.notes || undefined,
      location: "Online",
      is_all_day: false,
    };

    // Call the API to create the appointment
    createAppointment(appointmentData);
    setIsDialogOpen(false);
  };

  const onEditSubmit = (data: AppointmentFormType) => {
    if (!selectedAppointment) return;

    // Get the date from the existing appointment
    const appointmentDate = new Date(selectedAppointment.start_time);
    const dateStr = appointmentDate.toISOString().split("T")[0];

    // Parse start and end times
    const [startHour, startMinute] = data.startTime.split(":").map(Number);
    const [endHour, endMinute] = data.endTime.split(":").map(Number);

    // Create new date objects for start and end times
    const startDateTime = new Date(`${dateStr}T00:00:00`);
    startDateTime.setHours(startHour, startMinute, 0);

    const endDateTime = new Date(`${dateStr}T00:00:00`);
    endDateTime.setHours(endHour, endMinute, 0);

    // Create appointment data for update
    const appointmentData = {
      id: selectedAppointment.id,
      title: data.title,
      notes: data.notes || undefined,
      start_time: startDateTime.toISOString(),
      end_time: endDateTime.toISOString(),
      location: selectedAppointment.location || "Online",
      is_all_day: selectedAppointment.is_all_day || false,
      appointment_type: selectedAppointment.appointment_type || "manual",
    };

    // Call the update mutation
    updateAppointmentMutation(appointmentData);
    setIsEditDialogOpen(false);
  };

  const handleDelete = () => {
    if (selectedAppointment && selectedAppointment.id) {
      deleteAppointmentMutation(selectedAppointment.id);
      setIsEditDialogOpen(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Helper function to format appointment date/time for display
  const formatAppointmentDateTime = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();

    const time = date.toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit", hour12: true });

    if (isToday) {
      return `Today at ${time}`;
    } else {
      const datePart = date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
      return `${datePart} at ${time}`;
    }
  };

  const renderEventContent = (eventInfo: any) => {
    // Get background color from extendedProps
    const backgroundColor = eventInfo.event.extendedProps.backgroundColor || "bg-[#4f46e5]";
    const meetingLink = eventInfo.event.extendedProps.meetingLink;

    // Format time range for first line
    const startTime = new Date(eventInfo.event.start).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });

    const endTime = eventInfo.event.end
      ? new Date(eventInfo.event.end).toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        })
      : "";

    const timeRange = endTime ? `${startTime} - ${endTime}` : startTime;

    return (
      <div className={`flex flex-col text-xs rounded-md py-1 ${backgroundColor}`}>
        <div className="text-white font-medium truncate">{timeRange}</div>
        <div className="text-white truncate">{eventInfo.event.title}</div>
        {meetingLink && (
          <div className="text-white text-xs flex items-center">
            <span className="bg-blue-500 text-white text-xs px-1 py-0.5 rounded">Meet</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <TabContainer tabId="APPOINTMENTS">
      {/* Upcoming Appointments Section */}
      {upcomingAppointments.length > 0 && (
        <div className="flex gap-4">
          {/* Left side - Upcoming appointments */}
          <div className="w-1/2">
            <h2>Upcoming Appointments</h2>
            <div className="flex flex-col gap-3 mt-4">
              {upcomingAppointments.map((app) => (
                <div key={app.id} className="rounded-lg border border-gray-200 dark:border-gray-800 p-3 shadow">
                  <div className="flex justify-between items-start mb-2">
                    <h2 className="font-medium truncate mr-2">{app.title}</h2>
                    <BellIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{formatAppointmentDateTime(app.start_time)}</p>
                  {app.notes && <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 truncate">{app.notes}</p>}
                  {app.meeting_link && (
                    <a href={app.meeting_link} target="_blank" rel="noopener noreferrer" className="text-blue-500 text-sm mt-2 inline-flex items-center">
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs px-2 py-0.5 rounded flex items-center">
                        Join Meeting
                      </span>
                    </a>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="w-1/2">
            <h2>Appointment Updates</h2>
            <div className="flex flex-col gap-3 mt-4">
              {appointments?.map((app) => {
                if (app.appointment_type === "booking" && app.patient === user?.user_id + "") {
                  return (
                    <div key={app.id} className="p-3 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
                      <div className="flex items-center gap-2">
                        {app.status === "confirmed" ? (
                          <div className="flex items-center gap-2 text-green-500">
                            <CheckCircleIcon className="h-4 w-4" />
                            <h2>Appointment Accepted for {formatAppointmentDateTime(app.start_time)}</h2>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 text-red-500">
                            <XCircleIcon className="h-4 w-4" />
                            <h2>Appointment Rejected for {formatAppointmentDateTime(app.start_time)}</h2>
                          </div>
                        )}
                      </div>
                      {app.status === "canceled" && app.cancellation_reason && <span className="muted">Reason: {app.cancellation_reason}</span>}
                      {app.meeting_link && (
                        <div className="mt-2">
                          <a href={app.meeting_link} target="_blank" rel="noopener noreferrer" className="text-blue-500 text-sm inline-flex items-center">
                            <span className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs px-2 py-0.5 rounded flex items-center">
                              Join Meeting
                            </span>
                          </a>
                        </div>
                      )}
                    </div>
                  );
                }
                return null;
              })}
            </div>
          </div>
        </div>
      )}

      <div className="calendar-container rounded-xl shadow-lg overflow-ahidden">
        <FullCalendar
          plugins={[dayGridPlugin, interactionPlugin]}
          dateClick={handleDateClick}
          eventClick={handleEventClick}
          events={events}
          selectable={true}
          height="calc(100vh - 280px)"
          eventBorderColor="transparent"
          slotLabelFormat={{
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          }}
          displayEventTime={true}
          displayEventEnd={true}
          // allDaySlot={false}
          nowIndicator={true}
          dayMaxEvents={true}
          eventContent={renderEventContent}
        />
      </div>

      {/* Create Appointment Modal */}
      <Modal
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        classNames={{
          base: "dark:bg-slate-950 rounded-xl",
          backdrop: "bg-black/50 backdrop-blur-sm",
        }}
        size="lg"
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalContent>
            <ModalHeader className="border-b border-gray-200 dark:border-gray-700 pb-3 text-base">
              <h2>Add Event for {formatDate(selectedDate)}</h2>
            </ModalHeader>
            <ModalBody className="space-y-4 py-5">
              <FormInput name="title" control={control} variant="underlined" placeholder="Enter event title" label="Event Title" autoFocus />
              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Start Time</label>
                  <TimeInput
                    size="md"
                    aria-label="Start Time"
                    value={parseTimeString(watch("startTime")) as any}
                    onChange={(timeValue) => {
                      setValue("startTime", formatTimeObject(timeValue));
                    }}
                    granularity="minute"
                    hideTimeZone
                    className="w-full"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">End Time</label>
                  <TimeInput
                    size="md"
                    aria-label="End Time"
                    value={parseTimeString(watch("endTime")) as any}
                    onChange={(timeValue) => {
                      setValue("endTime", formatTimeObject(timeValue));
                    }}
                    granularity="minute"
                    hideTimeZone
                    className="w-full"
                  />
                </div>
              </div>
              <FormTextArea name="notes" control={control} placeholder="Add notes (optional)" label="Notes" />
              {selectedAppointment?.meeting_link && (
                <div className="mt-2">
                  <label className="block text-sm font-medium mb-1">Meeting Link</label>
                  <div className="flex items-center">
                    <a href={selectedAppointment.meeting_link} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                      {selectedAppointment.meeting_link}
                    </a>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter className="border-t border-gray-200 dark:border-gray-700 pt-3">
              <Button size="sm" variant="light" onPress={() => setIsDialogOpen(false)} type="button">
                Cancel
              </Button>
              <Button size="sm" color="primary" type="submit" isLoading={isCreatingAppointment}>
                Save
              </Button>
            </ModalFooter>
          </ModalContent>
        </form>
      </Modal>

      {/* Edit Appointment Modal */}
      <Modal
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        classNames={{
          base: "dark:bg-slate-950 rounded-xl",
          backdrop: "bg-black/50 backdrop-blur-sm",
        }}
        size="lg"
      >
        <form onSubmit={handleEditSubmit(onEditSubmit)}>
          <ModalContent>
            <ModalHeader className="border-b border-gray-200 dark:border-gray-700 pb-3 text-base">
              <h2>Edit Appointment</h2>
            </ModalHeader>
            <ModalBody className="space-y-4 py-5">
              <FormInput name="title" control={editControl} variant="underlined" placeholder="Enter event title" label="Event Title" autoFocus />
              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Start Time</label>
                  <TimeInput
                    size="md"
                    aria-label="Start Time"
                    value={parseTimeString(watchEdit("startTime")) as any}
                    onChange={(timeValue) => {
                      setEditValue("startTime", formatTimeObject(timeValue));
                    }}
                    granularity="minute"
                    hideTimeZone
                    className="w-full"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">End Time</label>
                  <TimeInput
                    size="md"
                    aria-label="End Time"
                    value={parseTimeString(watchEdit("endTime")) as any}
                    onChange={(timeValue) => {
                      setEditValue("endTime", formatTimeObject(timeValue));
                    }}
                    granularity="minute"
                    hideTimeZone
                    className="w-full"
                  />
                </div>
              </div>
              <FormTextArea name="notes" control={editControl} placeholder="Add notes (optional)" label="Notes" />
              {selectedAppointment?.meeting_link && (
                <div className="mt-2">
                  <label className="block text-sm font-medium mb-1">Meeting Link</label>
                  <div className="flex items-center">
                    <a href={selectedAppointment.meeting_link} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                      {selectedAppointment.meeting_link}
                    </a>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter className="border-t border-gray-200 dark:border-gray-700 pt-3 flex justify-between">
              <Button size="sm" color="danger" type="button" onPress={handleDelete} isLoading={isDeletingAppointment}>
                Delete
              </Button>
              <div className="flex gap-2">
                <Button size="sm" variant="light" onPress={() => setIsEditDialogOpen(false)} type="button">
                  Cancel
                </Button>
                <Button size="sm" color="primary" type="submit" isLoading={isUpdatingAppointment}>
                  Update
                </Button>
              </div>
            </ModalFooter>
          </ModalContent>
        </form>
      </Modal>
    </TabContainer>
  );
}
