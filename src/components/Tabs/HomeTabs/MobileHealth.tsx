import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import FitbitProfile from "./subComponents/fitbitProfile";
import { ActiveHoursCard } from "./subComponents/ActiveHoursCard";
import { DailyStressCard } from "./subComponents/DailyStressCard";
import { SleepQualityCard } from "./subComponents/SleepQualityCard";
import { WearableDataCard } from "./subComponents/WearableDataCard";
import TabContainer from "./containers/TabContainer";

const MobileHealth = () => {
  return (
    <TabContainer tabId="MOBILE_HEALTH">
      <div className="flex flex-col gap-2 p-2">
        <ImportantNoticeDropdown />
        <FitbitProfile />
        <div className="grid grid-cols-1 sm:grid-cols-12 gap-2 sm:gap-4">
          {/* Active Hours Card */}
          <ActiveHoursCard />

          {/* Daily Stress Tracking Card */}
          <DailyStressCard />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-12 gap-2 sm:gap-4">
          <SleepQualityCard />
          <WearableDataCard />
        </div>
      </div>
    </TabContainer>
  );
};

export default MobileHealth;
