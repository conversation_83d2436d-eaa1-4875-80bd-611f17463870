"use client";
import AIChat from "@/components/AIChat";
import ServiceCard from "@/components/Card/ServiceCard";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import { AnalysisIcon } from "@/components/icons";
import CustomModal from "@/components/Modals/CustomModal";
import QRCodeGenerator from "@/components/QRCodeGenerator";
import UploadFile from "@/components/UploadFile";
import { useDeleteDocument, useGetDiagnosis } from "@/hooks/home-dashboard/useDiagnosisANDPrescriptions";
import { formatDate } from "@/lib/dateFactor";
import { cn } from "@/lib/utils";
import { Button, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";
import type { Selection } from "@react-types/shared";
import { ArrowDownToLine, ChevronDown, ChevronUp, Mail, Trash2 } from "lucide-react";
import React, { useState } from "react";
import { MdAddToDrive } from "react-icons/md";
import TabContainer from "./containers/TabContainer";

interface DiagnosisItem {
  id: string;
  name: string;
  file_data?: {
    url: string;
  };
  created_at: string;
}

const Diagnosis = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { diagnosis } = useGetDiagnosis();
  const { mutate: deleteDocument } = useDeleteDocument();
  const [isAIChatOpen, setIsAIChatOpen] = useState(false);
  const [isAISubscriptionOpen, setIsAISubscriptionOpen] = useState(false);
  const [isQRCodeModalOpen, setIsQRCodeModalOpen] = useState(false);
  const qrCodeRef = React.useRef<HTMLDivElement>(null);
  const [qrToken, setQrToken] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState("");
  const [previewFileName, setPreviewFileName] = useState("");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set([]));
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);

  const handleAIAnalysis = (documentId: string[]) => {
    if (documentId.length > 0) {
      setSelectedDocuments(documentId);
      setIsAIChatOpen(true);
    }
  };

  const handleBulkDelete = () => {
    // Delete all selected documents
    if (selectedKeys.size > 0) {
      const selectedIds = Array.from(selectedKeys) as string[];
      // Call delete for each document
      selectedIds.forEach((id) => {
        deleteDocument(id);
      });

      // Clear selection after deletion
      setSelectedKeys(new Set([]));
    }
  };

  const handlePreview = (fileUrl: string, fileName: string) => {
    // Make sure fileUrl is not undefined or empty
    if (!fileUrl) {
      console.error("File URL is missing or undefined");
      // You might want to show an error message to the user
      return;
    }

    setPreviewUrl(fileUrl);
    setPreviewFileName(fileName);
    setIsPreviewOpen(true);

    // For debugging
    console.log("Opening preview with URL:", fileUrl, "and filename:", fileName);
  };

  const handleDelete = (documentId: string) => {
    deleteDocument(documentId);
  };

  const handleSelectionChange = (keys: Selection) => {
    if (keys === "all") {
      // If "all" is selected, add all diagnosis IDs to the set
      const allKeys = new Set((diagnosis as DiagnosisItem[])?.map((item) => item.id) || []);
      setSelectedKeys(allKeys);
    } else {
      // Otherwise convert the selection to a Set
      setSelectedKeys(new Set(keys as Iterable<string>));
    }
  };

  return (
    <TabContainer tabId="DIAGNOSES">
      <div className="flex flex-col gap-2 p-2">
        <ImportantNoticeDropdown />
        <div className="space-y-2 py-3 px-1 border-b border-gray-200 dark:border-gray-700">
          {/* QR Code  */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between sm:border-b border-gray-200 dark:border-gray-700 pb-2 gap-2">
            <p
              className="cursor-pointer hover:text-blue-600 transition-colors flex items-center gap-2 whitespace-nowrap overflow-x-auto"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <span className="font-semibold text-sm">QR Code</span>
              <span className="muted">Enabled Upload -</span>
              <span className="muted">This Feature is coming Soon.</span>
              <span>{isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}</span>
            </p>
            <Button size="sm" color="primary" variant="shadow" className="w-full sm:w-auto" onPress={() => setIsQRCodeModalOpen(true)}>
              My Diagnosis QR Code
            </Button>
          </div>
          {/* <QRCode /> */}
          {isExpanded && (
            <div className="space-y-4 text-xs dark:text-gray-300 text-justify mb-2">
              <p>
                In the course of one's lifetime one accumulates many Diagnoses during visits to Doctors or Clinics which are often lost or not stored properly
                for future reference.
              </p>

              <p>
                Often these Diagnoses are deemed inconsequential, however over a period of time, if stored in your own R.A.V.I.D. account, then the accumulated
                Diagnosis can offer clues on trends within the human body.
              </p>

              <p>
                Similarly having a consequential Diagnoses stored or be available to send to other third parties that you authorize for a{" "}
                <span className="font-bold">Second Opinion</span> can offer you additional data points to make an informed decision.
              </p>

              <p className="text-blue-700 dark:text-blue-500">
                We are therefore working on a QR Code enabled solution that will enable you to share a time sensitive QR code personalized for this specific
                task with your Care provider, to enable you to receive your Diagnosis seamlessly into your designated storage area within the R.A.V.I.D.
                account.
              </p>

              <p>
                If you choose to seek a Second Opinion for your Diagnoses, we envision a pathway in this section of your R.A.V.I.D. account by which you can
                seamlessly transfer a copy of your data to the medical practitioner of your choice.
              </p>

              <p className="border-b border-gray-200 dark:border-gray-700 pb-2">
                Additionally, in the future we also envision AI enabled features (with Permission) within this Diagnosis section of your R.A.V.I.D. account, to
                analyse the accumulated or single Diagnosis could perhaps be additionally valuable to you.
              </p>
            </div>
          )}
          <div className="space-y-2 flex flex-col sm:flex-row justify-between gap-2">
            <div className="flex flex-col gap-2">
              <h1>My Diagnoses</h1>
              <span className="muted">Upload, store, share, use Enable AI Analysis and manage all your diagnosis documents here:</span>
            </div>
            <div>
              <Button
                size="sm"
                className="text-xs gap-1 text-orange-300 bg-[#7C389B] hover:bg-[#7C389B] dark:hover:bg-[#5f2c76] px-2 py-1 w-full sm:w-auto"
                onPress={() => setIsAISubscriptionOpen(true)}
                startContent={<AnalysisIcon />}
              >
                Enable AI Analysis
              </Button>
            </div>
          </div>

          {/* Bulk action bar that appears when items are selected */}
          {selectedKeys.size > 0 && (
            <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-800/50 rounded-md p-2 mb-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium">{selectedKeys.size} file(s) selected</span>
                <Button size="sm" variant="light" className="text-red-500" onPress={handleBulkDelete} startContent={<Trash2 className="w-4 h-4" />} />
              </div>
              <Button
                size="sm"
                variant="light"
                className="!p-1 !min-w-0 !h-auto bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700"
                onPress={() => handleAIAnalysis(Array.from(selectedKeys))}
                startContent={<AnalysisIcon />}
              >
                <span className="text-xs">Analyze All</span>
              </Button>
            </div>
          )}

          <div className="overflow-x-auto -mx-2 px-2">
            <Table
              aria-label="Diagnosis Table"
              removeWrapper
              className="rounded-lg p-1 border dark:border-gray-800 overflow-x-scroll sm:overflow-x-hidden min-w-full"
              classNames={{
                th: "dark:bg-slate-900",
                td: "py-2",
                tr: "hover:bg-gray-50 dark:hover:bg-gray-800/50",
              }}
              selectionMode="multiple"
              selectedKeys={selectedKeys}
              onSelectionChange={handleSelectionChange}
              disableAnimation={true}
            >
              <TableHeader>
                <TableColumn key="filename" className="w-[40%]">
                  <h2>File Name</h2>
                </TableColumn>
                <TableColumn key="date" className="hidden sm:table-cell w-[25%]">
                  <h2>Date</h2>
                </TableColumn>
                <TableColumn key="actions" className="w-[25%]">
                  <h2>Actions</h2>
                </TableColumn>
                <TableColumn key="extra" className="w-[10%]">
                  <h2>{""}</h2>
                </TableColumn>
              </TableHeader>
              <TableBody items={diagnosis || []}>
                {(item: any) => (
                  <TableRow
                    className={cn("border-b border-gray-200 dark:border-gray-700", { "border-b-0": item === diagnosis[diagnosis.length - 1] })}
                    key={item.id}
                  >
                    <TableCell className="cursor-pointer hover:text-blue-600 transition-colors">
                      <div className="flex flex-col">
                        <Button
                          variant="light"
                          size="sm"
                          className=" !bg-transparent text-xs !justify-start "
                          onPress={() => handlePreview(item.file_data?.url, item.name)}
                        >
                          <span className="truncate max-w-[150px] sm:max-w-none text-xs">{item.name}</span>
                        </Button>
                        <span className="block sm:hidden text-xs text-gray-500 dark:text-gray-400">{formatDate(item.created_at).split(" ")[0]}</span>
                      </div>
                    </TableCell>
                    <TableCell className="whitespace-nowrap hidden sm:table-cell text-xs">{formatDate(item.created_at)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <Button
                          as="a"
                          href={item.file_data?.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          size="sm"
                          variant="light"
                          className=" !bg-transparent !justify-start !p-0 !min-w-0 !h-auto"
                          title="Download"
                        >
                          <ArrowDownToLine className="w-4 h-4" />
                        </Button>

                        <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                          <Mail className="w-4 h-4" />
                        </Button>

                        <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                          <MdAddToDrive className="w-4 h-4" />
                        </Button>
                        <CustomModal
                          title="Delete Document"
                          body="Are you sure you want to delete this document?"
                          buttonOpenText="Delete"
                          primaryButtonText="Delete"
                          primaryButtonColor="bg-red-600 hover:bg-red-700 text-white"
                          icon={<Trash2 className="w-4 h-4 text-red-500" />}
                          onPress={() => handleDelete(item.id)}
                          className="!bg-transparent !justify-start !p-0 !min-w-0 !h-auto"
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        onPress={() => handleAIAnalysis([item.id])}
                        size="sm"
                        variant="light"
                        // isIconOnly
                        className="!p-1 !min-w-0 !h-auto rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700"
                      >
                        <AnalysisIcon />
                      </Button>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
            <span className="muted text-left w-full sm:w-2/3 text-xs">
              Please note that the maximum file size for uploads is 20 MB. If you exceed your storage limit, you can purchase additional space to continue
              uploading your files <span className="text-blue-700 dark:text-blue-500">seamlessly here</span>
            </span>

            <div className="w-full sm:w-auto">
              <UploadFile type="diagnosis" />
            </div>
          </div>
          <span className="text-xs text-yellow-700 dark:text-yellow-300 block">
            Upload your Diagnosis Files. Supported formats: PDF, JPEG, PNG. Maximum size: 2MB
          </span>
        </div>
        <AIChat
          isOpen={isAIChatOpen}
          onClose={() => {
            setIsAIChatOpen(false);
            setSelectedDocuments([]);
          }}
          documentIds={selectedDocuments}
          type="diagnosis"
        />

        {/* Preview Modal */}
        <CustomModal
          title={previewFileName}
          body={
            previewUrl && (
              <div className="w-full h-[50vh] sm:h-[70vh] relative">
                <iframe
                  src={`${previewUrl}#toolbar=0`}
                  className="w-full h-full rounded-lg"
                  title={previewFileName}
                  onLoad={(e) => {
                    // Attempt to adjust modal width based on content
                    const iframe = e.target as HTMLIFrameElement;
                    if (iframe.contentWindow) {
                      const content = iframe.contentWindow.document;
                      content.body.style.margin = "0";
                      content.body.style.padding = "0";
                    }
                  }}
                />
              </div>
            )
          }
          size="4xl"
          isOpen={isPreviewOpen}
          onOpenChange={setIsPreviewOpen}
          primaryButtonText="Close"
          primaryButtonColor="bg-red-700 hover:bg-red-800 text-white"
          className="max-w-[95vw] w-auto"
        />

        {/* QR Code Modal */}
        <CustomModal
          title="My Diagnosis QR Code"
          body={
            <div className="flex flex-col items-center justify-center gap-4 py-4">
              <div className="flex flex-col items-center">
                <h2>Share this QR Code with your care provider</h2>
              </div>

              <div ref={qrCodeRef} className="w-[250px] h-[250px] bg-white p-4 rounded-xl flex items-center justify-center">
                {/* QR Code will be rendered here */}
              </div>

              {qrToken && (
                <div className="mt-2 text-center">
                  {/* <p className="text-xs text-gray-500 dark:text-gray-400">Token: {qrToken.substring(0, 8)}...</p> */}
                  <p className="text-xs text-gray-500 mt-1">Valid for 24 hours</p>
                </div>
              )}

              <QRCodeGenerator url={`https://test.ravid.cloud`} qrRef={qrCodeRef} isModalOpen={isQRCodeModalOpen} onTokenGenerated={setQrToken} />
            </div>
          }
          size="md"
          isOpen={isQRCodeModalOpen}
          onOpenChange={setIsQRCodeModalOpen}
          // primaryButtonText="Close"
          // primaryButtonColor="bg-blue-700 hover:bg-blue-800 text-white"
        />

        <CustomModal
          isOpen={isAISubscriptionOpen}
          onOpenChange={setIsAISubscriptionOpen}
          size="2xl"
          title="AI Agent Integration"
          body={
            <div className="flex flex-col gap-2">
              <span className="muted">To use AI Analysis you need to purchase this feature</span>
              <h2 className="muted">Explore our AI plans and choose the one that best suits your needs.</h2>
              <div className="flex flex-col sm:flex-row gap-2 mt-2">
                <ServiceCard
                  serviceInfo={{
                    title: "AI Agent Integration Monthly Subscription",
                    benefits: ["Full monthly access to AI Analysis", "Applicable throughout the R.A.V.I.D. platform", "Daily Token Limits for AI Agent"],
                  }}
                  priceInfo={{
                    price: 20,
                    priceUnit: "Month",
                  }}
                  onClick={() => {}}
                />
                <ServiceCard
                  serviceInfo={{
                    title: "One Day Payment",
                    benefits: ["AI Analysis of a single day (24 hours)", "Applicable throughout the R.A.V.I.D. platform", "Daily Token Limits for AI Agent"],
                  }}
                  priceInfo={{
                    price: 1,
                    priceUnit: "Day",
                  }}
                  onClick={() => {}}
                />
              </div>
            </div>
          }
          primaryButtonText="Close"
          primaryButtonColor="bg-red-700 hover:bg-red-800 text-white"
        />
      </div>
    </TabContainer>
  );
};

export default Diagnosis;
