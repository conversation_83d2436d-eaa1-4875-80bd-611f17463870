"use client";
import AIChat from "@/components/AIChat";
import ServiceCard from "@/components/Card/ServiceCard";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import CustomModal from "@/components/Modals/CustomModal";
import QRCodeGenerator from "@/components/QRCodeGenerator";
import UploadFile from "@/components/UploadFile";
import { useDeleteDocument, useGetDiagnosis } from "@/hooks/home-dashboard/useDiagnosisANDPrescriptions";
import useAlerts from "@/hooks/useAlert";
import { formatDate } from "@/lib/dateFactor";
import { cn } from "@/lib/utils";
import { Button, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";
import type { Selection } from "@react-types/shared";
import { ArrowDownToLine, ChevronDown, ChevronUp, Mail, Trash2 } from "lucide-react";
import React, { useState } from "react";
import { MdAddToDrive } from "react-icons/md";
import TabContainer from "./containers/TabContainer";

interface PrescriptionItem {
  id: string;
  name: string;
  file_data?: {
    url: string;
  };
  created_at: string;
}

const Prescriptions = () => {
  const { data: alerts } = useAlerts();

  // Find alert for this tab

  const [isExpanded, setIsExpanded] = useState(false);
  const { prescriptions, isLoading, error } = useGetDiagnosis();
  const { mutate: deleteDocument, isPending: isDeleting } = useDeleteDocument();
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isAIChatOpen, setIsAIChatOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set([]));
  const [isQRCodeModalOpen, setIsQRCodeModalOpen] = useState(false);
  const qrCodeRef = React.useRef<HTMLDivElement>(null);
  const [qrToken, setQrToken] = useState<string | null>(null);

  const [isAISubscriptionOpen, setIsAISubscriptionOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState("");
  const [previewFileName, setPreviewFileName] = useState("");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const handleAIAnalysis = (documentId?: string | string[]) => {
    // If documentId is provided as array, process multiple documents
    if (Array.isArray(documentId) && documentId.length > 0) {
      setSelectedDocuments(documentId); // Store all document IDs
      setIsAIChatOpen(true);
      console.log("Processing multiple documents:", documentId);
    }
    // If documentId is provided as string, open AI Chat for single document
    else if (documentId && typeof documentId === "string") {
      setSelectedDocuments([documentId]); // Store as array with single item
      setIsAIChatOpen(true);
    }
  };

  const handlePreview = (fileUrl: string, fileName: string) => {
    if (!fileUrl) {
      console.error("File URL is missing or undefined");
      return;
    }

    setPreviewUrl(fileUrl);
    setPreviewFileName(fileName);
    setIsPreviewOpen(true);
  };

  const handleBulkDelete = () => {
    // Delete all selected documents
    if (selectedKeys.size > 0) {
      const selectedIds = Array.from(selectedKeys) as string[];
      console.log("Deleting multiple documents:", selectedIds);

      // Call delete for each document
      selectedIds.forEach((id) => {
        deleteDocument(id);
      });

      // Clear selection after deletion
      setSelectedKeys(new Set([]));
    }
  };

  const handleSelectionChange = (keys: Selection) => {
    if (keys === "all") {
      // If "all" is selected, add all prescription IDs to the set
      const allKeys = new Set((prescriptions as PrescriptionItem[])?.map((item) => item.id) || []);
      setSelectedKeys(allKeys);
    } else {
      // Otherwise convert the selection to a Set
      setSelectedKeys(new Set(keys as Iterable<string>));
    }
  };

  return (
    <TabContainer tabId="PRESCRIPTIONS">
      <div className="flex flex-col gap-2 p-2">
        <ImportantNoticeDropdown />
        <div className="space-y-2 py-3 px-1 border-b border-gray-200 dark:border-gray-700">
          {/* QR Code  */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between sm:border-b border-gray-200 dark:border-gray-700 pb-2 gap-2">
            <p
              className="cursor-pointer hover:text-blue-600 transition-colors flex items-center gap-2 whitespace-nowrap overflow-x-auto"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <span className="font-semibold text-sm">QR Code</span>
              <span className="muted">Enabled Upload -</span>
              <span className="muted">This Feature is coming Soon.</span>
              <span>{isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}</span>
            </p>
            <Button size="sm" color="primary" variant="shadow" className="w-full sm:w-auto" onPress={() => setIsQRCodeModalOpen(true)}>
              My Prescriptions QR Code
            </Button>
          </div>

          <div className="flex gap-3">
            <span className="text-red-600 text-sm">Rx (Pharmacy)</span>
            <span className="text-xs dark:bg-gray-800 bg-gray-300 px-2 py-1 rounded-xl">Feature Coming Soon</span>
          </div>
          {/* <QRCode /> */}
          {isExpanded && (
            <div className="space-y-4 text-xs dark:text-gray-300 text-justify mb-2">
              <p>
                In the course of one's lifetime one accumulates many Diagnoses during visits to Doctors or Clinics which are often lost or not stored properly
                for future reference.
              </p>

              <p>
                Often these Diagnoses are deemed inconsequential, however over a period of time, if stored in your own R.A.V.I.D. account, then the accumulated
                Diagnosis can offer clues on trends within the human body.
              </p>

              <p>
                Similarly having a consequential Diagnoses stored or be available to send to other third parties that you authorize for a{" "}
                <span className="font-bold">Second Opinion</span> can offer you additional data points to make an informed decision.
              </p>

              <p className="text-blue-700 dark:text-blue-500">
                We are therefore working on a QR Code enabled solution that will enable you to share a time sensitive QR code personalized for this specific
                task with your Care provider, to enable you to receive your Diagnosis seamlessly into your designated storage area within the R.A.V.I.D.
                account.
              </p>

              <p>
                If you choose to seek a Second Opinion for your Diagnoses, we envision a pathway in this section of your R.A.V.I.D. account by which you can
                seamlessly transfer a copy of your data to the medical practitioner of your choice.
              </p>

              <p className="border-b border-gray-200 dark:border-gray-700 pb-2">
                Additionally, in the future we also envision AI enabled features (with Permission) within this Diagnosis section of your R.A.V.I.D. account, to
                analyse the accumulated or single Diagnosis could perhaps be additionally valuable to you.
              </p>
            </div>
          )}
          <div className="space-y-2 flex flex-col sm:flex-row justify-between gap-2">
            <div className="flex flex-col gap-2">
              <h1>My Prescriptions</h1>
              <span className="muted">Here you can access and manage all your prescription documents</span>
            </div>
            <div>
              <Button
                size="sm"
                className="text-xs gap-1 text-orange-300 bg-[#7C389B] hover:bg-[#7C389B] dark:hover:bg-[#5f2c76] px-2 py-1 w-full sm:w-auto"
                onPress={() => setIsAISubscriptionOpen(true)}
              >
                <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8.69544 4.49728C7.65841 3.45987 6.94861 1.6483 6.59636 0C6.24356 1.64864 5.53451 3.46059 4.49711 4.49799C3.46008 5.53468 1.64796 6.24448 0 6.59745C1.64864 6.9497 3.46042 7.65908 4.49745 8.69653C5.53447 9.73355 6.24427 11.5455 6.59707 13.1938C6.94932 11.5452 7.65892 9.73322 8.69577 8.69581C9.7328 7.65879 11.5449 6.94932 13.1929 6.59636C11.5446 6.2441 9.73284 5.53434 8.69544 4.49728Z"
                    fill="url(#paint0_linear_1022_31082)"
                  />
                  <path
                    d="M15.4311 12.8039C14.8121 12.1846 14.3873 11.1018 14.1769 10.1172C13.9661 11.1018 13.5428 12.1846 12.9231 12.8043C12.3034 13.4233 11.2208 13.8477 10.2363 14.0585C11.2211 14.2692 12.3034 14.6929 12.9231 15.3126C13.5428 15.932 13.9668 17.0148 14.1776 17.9994C14.388 17.0144 14.8121 15.932 15.4314 15.3123C16.0507 14.6929 17.1335 14.2689 18.1178 14.0578C17.1336 13.8473 16.0508 13.4233 15.4311 12.8039Z"
                    fill="url(#paint1_linear_1022_31082)"
                  />
                  <defs>
                    <linearGradient id="paint0_linear_1022_31082" x1="6.59644" y1="0" x2="6.59644" y2="13.1938" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#7D5191" />
                      <stop offset="1" stopColor="#FFD52B" />
                    </linearGradient>
                    <linearGradient id="paint1_linear_1022_31082" x1="14.1771" y1="10.1172" x2="14.1771" y2="17.9994" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#7D5191" />
                      <stop offset="1" stopColor="#FFD52B" />
                    </linearGradient>
                  </defs>
                </svg>
                Enable AI Analysis
              </Button>
            </div>
          </div>
          {/* Add bulk action bar before the table */}
          {selectedKeys.size > 0 && (
            <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-800/50 rounded-md p-2 mb-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium">{selectedKeys.size} file(s) selected</span>
                <Button size="sm" variant="light" className="text-red-500" onPress={handleBulkDelete} startContent={<Trash2 className="w-4 h-4" />} />
              </div>
              <Button
                size="sm"
                variant="light"
                className="!p-1 !min-w-0 !h-auto bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700"
                onPress={() => handleAIAnalysis(Array.from(selectedKeys))}
              >
                <svg className="w-4 h-4 mr-1" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8.69544 4.49728C7.65841 3.45987 6.94861 1.6483 6.59636 0C6.24356 1.64864 5.53451 3.46059 4.49711 4.49799C3.46008 5.53468 1.64796 6.24448 0 6.59745C1.64864 6.9497 3.46042 7.65908 4.49745 8.69653C5.53447 9.73355 6.24427 11.5455 6.59707 13.1938C6.94932 11.5452 7.65892 9.73322 8.69577 8.69581C9.7328 7.65879 11.5449 6.94932 13.1929 6.59636C11.5446 6.2441 9.73284 5.53434 8.69544 4.49728Z"
                    fill="url(#paint0_linear_1022_31082)"
                  />
                  <path
                    d="M15.4311 12.8039C14.8121 12.1846 14.3873 11.1018 14.1769 10.1172C13.9661 11.1018 13.5428 12.1846 12.9231 12.8043C12.3034 13.4233 11.2208 13.8477 10.2363 14.0585C11.2211 14.2692 12.3034 14.6929 12.9231 15.3126C13.5428 15.932 13.9668 17.0148 14.1776 17.9994C14.388 17.0144 14.8121 15.932 15.4314 15.3123C16.0507 14.6929 17.1335 14.2689 18.1178 14.0578C17.1336 13.8473 16.0508 13.4233 15.4311 12.8039Z"
                    fill="url(#paint1_linear_1022_31082)"
                  />
                  <defs>
                    <linearGradient id="paint0_linear_1022_31082" x1="6.59644" y1="0" x2="6.59644" y2="13.1938" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#7D5191" />
                      <stop offset="1" stopColor="#FFD52B" />
                    </linearGradient>
                    <linearGradient id="paint1_linear_1022_31082" x1="14.1771" y1="10.1172" x2="14.1771" y2="17.9994" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#7D5191" />
                      <stop offset="1" stopColor="#FFD52B" />
                    </linearGradient>
                  </defs>
                </svg>
                <span className="text-xs">Analyze All</span>
              </Button>
            </div>
          )}
          <div className="overflow-x-auto -mx-2 px-2">
            <Table
              aria-label="Prescriptions Table"
              removeWrapper
              className="rounded-lg overflow-x-scroll sm:overflow-x-hidden p-1 border dark:border-slate-800 border-slate-300 min-w-full"
              classNames={{
                th: "dark:bg-slate-900",
                td: "py-2",
                tr: "hover:bg-gray-50 dark:hover:bg-gray-800/50",
              }}
              selectionMode="multiple"
              selectedKeys={selectedKeys}
              onSelectionChange={handleSelectionChange}
              disableAnimation={true}
            >
              <TableHeader>
                <TableColumn key="filename" className="w-[40%]">
                  <h2>File Name</h2>
                </TableColumn>
                <TableColumn key="date" className="hidden sm:table-cell w-[25%]">
                  <h2>Date</h2>
                </TableColumn>
                <TableColumn key="actions" className="w-[25%]">
                  <h2>Actions</h2>
                </TableColumn>
                <TableColumn key="extra" className="w-[10%]">
                  <h2>{""}</h2>
                </TableColumn>
              </TableHeader>
              <TableBody items={prescriptions || []}>
                {(item: any) => (
                  <TableRow
                    className={cn("border-b border-gray-200 dark:border-gray-700", { "border-b-0": item === prescriptions[prescriptions.length - 1] })}
                    key={item.id}
                  >
                    <TableCell className="cursor-pointer hover:text-blue-600 transition-colors">
                      <div className="flex flex-col">
                        <Button
                          variant="light"
                          size="sm"
                          className=" !bg-transparent text-xs !justify-start "
                          onPress={() => handlePreview(item.file_data?.url, item.name)}
                        >
                          <span className="truncate max-w-[150px] sm:max-w-none text-xs">{item.name}</span>
                        </Button>
                        <span className="block sm:hidden text-xs text-gray-500 dark:text-gray-400">{formatDate(item.created_at).split(" ")[0]}</span>
                      </div>
                    </TableCell>
                    <TableCell className="whitespace-nowrap hidden sm:table-cell text-xs">{formatDate(item.created_at)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <a
                          href={item.file_data?.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded"
                          title="Download"
                        >
                          <ArrowDownToLine className="w-4 h-4" />
                        </a>
                        <button className="flex items-center">
                          <Mail className="w-4 h-4" />
                        </button>
                        <button className="flex items-center">
                          <MdAddToDrive className="w-4 h-4" />
                        </button>
                        <CustomModal
                          title="Delete Document"
                          body="Are you sure you want to delete this document?"
                          buttonOpenText="Delete"
                          primaryButtonText="Delete"
                          primaryButtonColor="bg-red-600 hover:bg-red-700 text-white"
                          icon={<Trash2 className="w-4 h-4 text-red-500" />}
                          onPress={() => deleteDocument(item.id)}
                          className="!bg-transparent !justify-start !p-0 !min-w-0 !h-auto"
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <button
                        onClick={() => handleAIAnalysis(item.id)}
                        className="flex bg-gray-100 dark:bg-gray-800 rounded-full p-1 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors items-center"
                      >
                        <svg className="w-4 h-4" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M8.69544 4.49728C7.65841 3.45987 6.94861 1.6483 6.59636 0C6.24356 1.64864 5.53451 3.46059 4.49711 4.49799C3.46008 5.53468 1.64796 6.24448 0 6.59745C1.64864 6.9497 3.46042 7.65908 4.49745 8.69653C5.53447 9.73355 6.24427 11.5455 6.59707 13.1938C6.94932 11.5452 7.65892 9.73322 8.69577 8.69581C9.7328 7.65879 11.5449 6.94932 13.1929 6.59636C11.5446 6.2441 9.73284 5.53434 8.69544 4.49728Z"
                            fill="url(#paint0_linear_1022_31082)"
                          />
                          <path
                            d="M15.4311 12.8039C14.8121 12.1846 14.3873 11.1018 14.1769 10.1172C13.9661 11.1018 13.5428 12.1846 12.9231 12.8043C12.3034 13.4233 11.2208 13.8477 10.2363 14.0585C11.2211 14.2692 12.3034 14.6929 12.9231 15.3126C13.5428 15.932 13.9668 17.0148 14.1776 17.9994C14.388 17.0144 14.8121 15.932 15.4314 15.3123C16.0507 14.6929 17.1335 14.2689 18.1178 14.0578C17.1336 13.8473 16.0508 13.4233 15.4311 12.8039Z"
                            fill="url(#paint1_linear_1022_31082)"
                          />
                          <defs>
                            <linearGradient id="paint0_linear_1022_31082" x1="6.59644" y1="0" x2="6.59644" y2="13.1938" gradientUnits="userSpaceOnUse">
                              <stop stopColor="#7D5191" />
                              <stop offset="1" stopColor="#FFD52B" />
                            </linearGradient>
                            <linearGradient id="paint1_linear_1022_31082" x1="14.1771" y1="10.1172" x2="14.1771" y2="17.9994" gradientUnits="userSpaceOnUse">
                              <stop stopColor="#7D5191" />
                              <stop offset="1" stopColor="#FFD52B" />
                            </linearGradient>
                          </defs>
                        </svg>
                      </button>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
            <span className="muted text-left w-full sm:w-2/3 text-xs">
              Please note that the maximum file size for uploads is 20 MB. If you exceed your storage limit, you can purchase additional space to continue
              uploading your files <span className="text-blue-700 dark:text-blue-500">seamlessly here</span>
            </span>
            <div className="w-full sm:w-auto">
              <UploadFile type="prescription" />
            </div>
          </div>
          <span className="text-xs text-yellow-700 dark:text-yellow-300 block">
            Upload your Diagnosis Files. Supported formats: PDF, JPEG, PNG. Maximum size: 2MB
          </span>
        </div>
        <AIChat
          isOpen={isAIChatOpen}
          onClose={() => {
            setIsAIChatOpen(false);
            setSelectedDocuments([]);
          }}
          documentIds={selectedDocuments}
          type="prescription"
        />

        {/* Preview Modal */}
        <CustomModal
          title={previewFileName}
          body={
            previewUrl && (
              <div className="w-full h-[50vh] sm:h-[70vh] relative">
                <iframe src={`${previewUrl}#toolbar=0`} className="w-full h-full rounded-lg" title={previewFileName} />
              </div>
            )
          }
          primaryButtonText="Close"
          size="4xl"
          primaryButtonColor="bg-red-700 hover:bg-red-800 text-white"
          onPrimaryAction={() => setIsPreviewOpen(false)}
          isOpen={isPreviewOpen}
          onOpenChange={setIsPreviewOpen}
        />

        {/* QR Code Modal */}
        <CustomModal
          title="My Prescriptions QR Code"
          body={
            <div className="flex flex-col items-center justify-center gap-4 py-4">
              <div className="flex flex-col items-center">
                <h2>Share this QR Code with your care provider</h2>
              </div>

              <div ref={qrCodeRef} className="w-[250px] h-[250px] bg-white p-4 rounded-xl flex items-center justify-center">
                {/* QR Code will be rendered here */}
              </div>

              {qrToken && (
                <div className="mt-2 text-center">
                  {/* <p className="text-xs text-gray-500 dark:text-gray-400">Token: {qrToken.substring(0, 8)}...</p> */}
                  <p className="text-xs text-gray-500 mt-1">Valid for 24 hours</p>
                </div>
              )}

              <QRCodeGenerator url={`https://test.ravid.cloud`} qrRef={qrCodeRef} isModalOpen={isQRCodeModalOpen} onTokenGenerated={setQrToken} />
            </div>
          }
          size="md"
          isOpen={isQRCodeModalOpen}
          onOpenChange={setIsQRCodeModalOpen}
          // primaryButtonText="Close"
          // primaryButtonColor="bg-blue-700 hover:bg-blue-800 text-white"
        />

        <CustomModal
          title="AI Agent Integration"
          body={
            <>
              <span className="muted">To use AI Analysis you need to purchase this feature</span>
              <h2 className="muted">Explore our AI plans and choose the one that best suits your needs.</h2>
              <div className="flex flex-col sm:flex-row gap-2 mt-2">
                <ServiceCard
                  serviceInfo={{
                    title: "AI Agent Integration Monthly Subscription",
                    benefits: ["Full monthly access to AI Analysis", "Applicable throughout the R.A.V.I.D. platform", "Daily Token Limits for AI Agent"],
                  }}
                  priceInfo={{
                    price: 20,
                    priceUnit: "Month",
                  }}
                  onClick={() => {}}
                />
                <ServiceCard
                  serviceInfo={{
                    title: "One Day Payment",
                    benefits: ["AI Analysis of a single day (24 hours)", "Applicable throughout the R.A.V.I.D. platform", "Daily Token Limits for AI Agent"],
                  }}
                  priceInfo={{
                    price: 1,
                    priceUnit: "Day",
                  }}
                  onClick={() => {}}
                />
              </div>{" "}
            </>
          }
          primaryButtonText="Close"
          primaryButtonColor="bg-red-700 hover:bg-red-800 text-white"
          onPrimaryAction={() => setIsAISubscriptionOpen(false)}
          isOpen={isAISubscriptionOpen}
          onOpenChange={setIsAISubscriptionOpen}
          size="2xl"
        />
      </div>
    </TabContainer>
  );
};

export default Prescriptions;
