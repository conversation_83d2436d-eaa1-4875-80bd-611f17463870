import { ReactNode } from "react";
import Alert from "@/components/Disclaimers/Alert";
import useAlerts from "@/hooks/useAlert";

interface TabContainerProps {
  children: ReactNode;
  tabId: string;
}

const TabContainer = ({ children, tabId }: TabContainerProps) => {
  const { data: alerts } = useAlerts();

  // Find alert for this tab
  // if end_date is past then don't show the alert
  const tabAlert = alerts?.find((alert) => (alert.tab === tabId || alert.tab === "ALL") && alert.end_date && new Date(alert.end_date) > new Date());

  return (
    <div className="flex flex-col gap-2">
      {tabAlert && <Alert alert={tabAlert} />}
      {children}
    </div>
  );
};

export default TabContainer;
