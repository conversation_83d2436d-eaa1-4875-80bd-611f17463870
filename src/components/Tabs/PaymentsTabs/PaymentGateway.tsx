import React, { useState } from "react";
import { Card, CardBody } from "@heroui/card";
import { Radio, RadioGroup } from "@heroui/radio";
import { Input } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";
import { Checkbox } from "@heroui/checkbox";

const PaymentGateway = () => {
  const [accountType, setAccountType] = useState("individual");

  return (
    <div className="flex flex-col lg:flex-row gap-6 w-full">
      {/* Left Card - Account Information */}
      <Card className="flex-1 dark:bg-slate-950 rounded-xl p-6">
        <CardBody className="flex flex-col gap-6">
          <h2>Choose Account Type</h2>
          <RadioGroup value={accountType} onValueChange={setAccountType} orientation="horizontal" className="flex gap-4">
            <Radio value="individual" className=" rounded-full">
              <h2>Individual Account</h2>
            </Radio>
            <Radio value="business" className=" rounded-full">
              <h2>Business Account</h2>
            </Radio>
          </RadioGroup>

          <Input
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500 ",
              inputWrapper: "dark:bg-slate-900",
            }}
            size="sm"
            placeholder="Account Holder Name"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              classNames={{
                input: "text-xs",
                label: "text-xs text-gray-500 ",
                inputWrapper: "dark:bg-slate-900",
              }}
              size="sm"
              placeholder="Email Address"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              classNames={{
                trigger: "dark:bg-slate-900 text-xs",
                value: "dark:bg-slate-900 text-xs",
                innerWrapper: "dark:bg-slate-900 text-xs",
                label: "text-xs",
              }}
              placeholder="Bank Account Country"
              className="dark:bg-[#131B2C] rounded-lg"
            >
              <SelectItem key="us">United States</SelectItem>
              <SelectItem key="ca">Canada</SelectItem>
              <SelectItem key="uk">United Kingdom</SelectItem>
            </Select>
            <Select
              classNames={{
                trigger: "dark:bg-slate-900 text-xs",
                value: "dark:bg-slate-900 text-xs",
                innerWrapper: "dark:bg-slate-900 text-xs",
                label: "text-xs",
              }}
              placeholder="Preferred Currency"
              className="dark:bg-[#131B2C] rounded-lg"
            >
              <SelectItem key="usd">USD</SelectItem>
              <SelectItem key="eur">EUR</SelectItem>
              <SelectItem key="gbp">GBP</SelectItem>
            </Select>
          </div>

          <Input
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500 ",
              inputWrapper: "dark:bg-slate-900",
            }}
            size="sm"
            placeholder="Bank Account Number"
          />

          <Input
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500 ",
              inputWrapper: "dark:bg-slate-900",
            }}
            size="sm"
            placeholder="Routing Number"
          />
        </CardBody>
      </Card>

      {/* Right Card - Payment Details */}
      <Card className="flex-1 dark:bg-slate-950 rounded-xl p-6">
        <CardBody className="flex flex-col gap-6">
          <Select
            classNames={{
              trigger: "dark:bg-slate-900 text-xs",
              value: "dark:bg-slate-900 text-xs",
              innerWrapper: "dark:bg-slate-900 text-xs",
              label: "text-xs",
            }}
            size="sm"
            placeholder="Preferred Payout Method"
            className="dark:bg-[#131B2C] rounded-lg"
          >
            <SelectItem key="bank">Bank Transfer</SelectItem>
            <SelectItem key="paypal">PayPal</SelectItem>
            <SelectItem key="crypto">Cryptocurrency</SelectItem>
          </Select>

          <Input
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500 ",
              inputWrapper: "dark:bg-slate-900",
            }}
            size="sm"
            placeholder="TAX ID"
            className="dark:bg-[#131B2C] rounded-lg"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              classNames={{
                input: "text-xs",
                label: "text-xs text-gray-500 ",
                inputWrapper: "dark:bg-slate-900",
              }}
              size="sm"
              placeholder="Street"
              className="dark:bg-[#131B2C] rounded-lg"
            />
            <Input
              classNames={{
                input: "text-xs",
                label: "text-xs text-gray-500 ",
                inputWrapper: "dark:bg-slate-900",
              }}
              size="sm"
              placeholder="City"
              className="dark:bg-[#131B2C] rounded-lg"
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              classNames={{
                input: "text-xs",
                label: "text-xs text-gray-500 ",
                inputWrapper: "dark:bg-slate-900",
              }}
              size="sm"
              placeholder="State"
              className="dark:bg-[#131B2C] rounded-lg"
            />
            <Input
              classNames={{
                input: "text-xs",
                label: "text-xs text-gray-500 ",
                inputWrapper: "dark:bg-slate-900",
              }}
              size="sm"
              placeholder="Postal/ZIP Code"
              className="dark:bg-[#131B2C] rounded-lg"
            />
          </div>

          <Input
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500 ",
              inputWrapper: "dark:bg-slate-900",
            }}
            size="sm"
            placeholder="Date of Birth"
            type="date"
            className="dark:bg-[#131B2C] rounded-lg"
          />

          <div className="mt-6 flex items-start gap-3">
            <Checkbox id="terms" className="mt-1" />
            <div>
              <h2>Terms & Conditions</h2>
              <p className="muted">Some Text goes here →</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default PaymentGateway;
