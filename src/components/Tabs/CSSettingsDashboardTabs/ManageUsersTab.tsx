import { useState } from "react";
import { staffUsers } from "../../../config/constants";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";
import { Avatar, Badge } from "@heroui/react";

export const ManageUsersTab = () => {
  const [users, setUsers] = useState(staffUsers);

  const handleRemoveUser = (userId: string) => {
    setUsers(users.filter((user) => user.id !== userId));
  };

  const specialties = [
    { key: "all", label: "All Specialties" },
    { key: "cardiologist", label: "Cardiologist" },
    { key: "neurologist", label: "Neurologist" },
    { key: "pediatrician", label: "Pediatrician" },
  ];

  return (
    <div className="w-full">
      {/* Search and Filter Section */}
      <div className="flex gap-4 mb-4">
        <div className="flex-1">
          <Input
            variant="bordered"
            placeholder="Search staff by name or specialty"
            className="w-full bg-transparent  rounded-lg"
            classNames={{
              input: "bg-transparent",
              inputWrapper: "bg-transparent",
            }}
          />
        </div>
        <div className="w-[300px]">
          <Select variant="bordered" placeholder="Filter by Specialty" classNames={{ trigger: "bg-transparent  rounded-lg", value: "bg-transparent" }}>
            {specialties.map((specialty) => (
              <SelectItem key={specialty.key}>{specialty.label}</SelectItem>
            ))}
          </Select>
        </div>
        <Button variant="solid" className="bg-blue-500 hover:bg-blue-600 text-xs">
          Add New User
        </Button>
      </div>

      {/* Users List */}
      {users.map((user) => (
        <div key={user.id} className="py-1 px-2 border dark:border-gray-800 rounded-md flex items-center justify-between mb-2">
          <div className="flex items-center">
            <Avatar src={user.image} className="mr-4" size="sm" />
            <div>
              <h1 className="text-sm">{user.name}</h1>
              <p className="text-xs text-gray-400">{user.specialty}</p>
              {user.verified && <span className="text-xs text-blue-400 border-blue-400">Verified</span>}
            </div>
          </div>

          <div className="flex">
            <Button variant="bordered" size="sm" className="mr-2 text-xs">
              Edit Details
            </Button>
            <Button variant="solid" size="sm" onPress={() => handleRemoveUser(user.id)} className="bg-red-500 hover:bg-red-600 text-white text-xs">
              Remove
            </Button>
          </div>
        </div>
      ))}

      {users.length === 0 && (
        <div className="text-center p-8">
          <p className="text-sm">No users found</p>
        </div>
      )}
    </div>
  );
};
