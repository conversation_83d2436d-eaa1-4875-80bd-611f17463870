"use client";

import ServiceCard from "@/components/Card/ServiceCard";
import { Button } from "@heroui/button";
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";

const serviceCards = [
  {
    title: "AI Agent Integration Monthly Subscription",
    benefits: ["Any thing to mention about the subscription with brief", "Any thing to mention about the subscription with brief"],
    price: 20,
    priceUnit: "month",
  },
  {
    title: "DNA Analysis",
    benefits: ["Any thing to mention about the subscription with brief", "Any thing to mention about the subscription with brief"],
    price: 1000,
    priceUnit: "run",
  },
  {
    title: "Data Storage",
    benefits: ["Any thing to mention about the subscription with brief", "Any thing to mention about the subscription with brief"],
    price: 20,
    priceUnit: "month",
    isActive: true,
  },
  {
    title: "Verification Services",
    benefits: ["Any thing to mention about the subscription with brief", "Any thing to mention about the subscription with brief"],
    price: 2,
    priceUnit: "month",
  },
];

export const SubscriptionTab = () => {
  return (
    <div className="space-y-6">
      {/* Active Features Section */}
      <div className="space-y-6">
        {/* Active Features Section */}
        <div>
          <h2 className="text-sm font-medium mb-4">Active Features:</h2>
          <Table
            removeWrapper
            className=" rounded-lg p-2 dark:border-slate-500 border-slate-300 min-w-full"
            classNames={{
              table: "bg-transparent ",
              base: "bg-transparent",
              tbody: "bg-transparent",
              thead: "bg-transparent",
              th: "px-2 py-2 text-xs sm:text-sm sm:px-4",
              td: "px-2 py-2 text-xs sm:text-sm sm:px-4",
            }}
          >
            <TableHeader className="bg-gray-800 rounded-lg">
              <TableColumn>
                <h2>Feature Name</h2>
              </TableColumn>
              <TableColumn>
                <h2>Date</h2>
              </TableColumn>
              <TableColumn>
                <h2>Amount</h2>
              </TableColumn>
              <TableColumn>
                <h2>Transaction ID</h2>
              </TableColumn>
              <TableColumn>
                <h2>Action</h2>
              </TableColumn>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>
                  <h2>AI Agent Integration Monthly Subscription</h2>
                </TableCell>
                <TableCell>
                  <h2>15 December 2024</h2>
                </TableCell>
                <TableCell>
                  <h2>$ 20.00</h2>
                </TableCell>
                <TableCell>
                  <h2>1234567890</h2>
                </TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm" className="text-xs text-red-500">
                    Unsubscribe
                  </Button>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>
                  <h2>Data Storage</h2>
                </TableCell>
                <TableCell>
                  <h2>12 November 2024</h2>
                </TableCell>
                <TableCell>
                  <h2>$ 1000</h2>
                </TableCell>
                <TableCell>1232967890</TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm" className="text-xs text-red-500">
                    Unsubscribe
                  </Button>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>
                  <h2>R.A.V.I.D. Premium Services</h2>
                </TableCell>
                <TableCell>
                  <h2>10 November 2024</h2>
                </TableCell>
                <TableCell>
                  <h2>$ 1.00</h2>
                </TableCell>
                <TableCell>1234567895</TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm" className="text-xs text-red-500">
                    Unsubscribe
                  </Button>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* Premium Features Section */}
        <div>
          <h2 className="text-sm font-medium mb-4">Premium Features:</h2>

          {/* Basic Package Card - Full Width */}
          <div className="w-full mb-4 rounded-[16px] bg-card p-6 flex justify-between items-center border-t-2 border-blue-500 shadow-sm">
            <div className="flex-1">
              <h2 className="font-medium">Clinic Solution - Basic Package</h2>
              <div className="flex items-center gap-2">
                <div className="w-3.5 h-3.5 rounded-full flex items-center justify-center">
                  <svg className="w-3.5 h-3.5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" />
                  </svg>
                </div>
                <span className="text-xs text-foreground">Any thing to mention about the subscription with brief</span>
              </div>
            </div>

            <div className="md:flex items-center md:gap-8 gap-4">
              <div className="text-right">
                <p className="text-2xl font-semibold text-foreground">
                  $20
                  <span className="text-xs font-normal text-muted-foreground">/month</span>
                </p>
              </div>
              <Button color="primary" isDisabled className=" text-xs px-8">
                Subscribed
              </Button>
            </div>
          </div>

          {/* Existing 4 cards grid */}
          <div className="grid md:grid-cols-4 gap-4 px-4 md:px-0">
            {serviceCards.map((card, index) => (
              <ServiceCard
                key={index}
                onClick={() => {}}
                serviceInfo={card}
                priceInfo={card}
                status={card.isActive ? { isActive: true } : { isDisabled: true }}
              />
            ))}
          </div>
        </div>

        {/* Footer Notes */}
        <div className="space-y-2 text-xs text-gray-400">
          <p>
            * All subscriptions are billed monthly and can be canceled at any time. Your subscription will help support the platform and unlock premium
            features.
          </p>
          <p>
            ** DNA Analysis is based on a Pay for Service model, i.e. you pay for every time you request an analysis. As computational biology evolves so will
            be the understanding and the results of the analytical services.
          </p>
        </div>
      </div>
    </div>
  );
};
