import { Table, TableHeader, TableBody, TableColumn, TableRow, TableCell, Button } from "@heroui/react";
import Image from "next/image";

// Sample billing data
const billingData = [
  {
    featureName: "Ads Free Feature",
    date: "15 December 2024",
    amount: "$ 1.00",
    status: "Processing",
    transactionId: "1234567890",
  },
  {
    featureName: "DNA Data Storage",
    date: "12 November 2024",
    amount: "$ 1.00",
    status: "Paid",
    transactionId: "1234567890",
  },
  {
    featureName: "Profile Verification",
    date: "10 November 2024",
    amount: "$ 1.00",
    status: "Paid",
    transactionId: "1234567890",
  },
  {
    featureName: "DNA Data Storage",
    date: "12 October 2024",
    amount: "$ 1.00",
    status: "Pending",
    transactionId: "1234567890",
  },
];

export const BillingTab = () => {
  return (
    <div className="space-y-6">
      {/* No Billings Message */}
      <div className="text-center py-3">
        <h2 className="text-sm font-medium mb-2">Currently you have no billings</h2>
        <p className="text-xs text-center text-gray-500">A sample of how your billings will be displayed</p>
      </div>

      {/* Sample Transactions Table */}
      <div className="space-y-4 overflow-x-auto">
        {/* Table with dark background */}
        <div className="dark:bg-slate-950 rounded-xl overflow-hidden">
          <Table
            removeWrapper
            className=" rounded-lg p-2 min-w-full"
            classNames={{
              table: "bg-transparent ",
              base: "bg-transparent",
              tbody: "bg-transparent",
              thead: "bg-transparent",
              th: "px-2 py-2 text-xs sm:text-sm sm:px-4",
              td: "px-2 py-2 text-xs sm:text-sm sm:px-4",
            }}
            aria-label="Billing information table"
          >
            <TableHeader>
              <TableColumn>
                <h2>Feature Name</h2>
              </TableColumn>
              <TableColumn>
                <h2>Date</h2>
              </TableColumn>
              <TableColumn>
                <h2>Amount</h2>
              </TableColumn>
              <TableColumn>
                <h2>Status</h2>
              </TableColumn>
              <TableColumn>
                <h2>Transaction ID</h2>
              </TableColumn>
              <TableColumn>
                <h2>Action</h2>
              </TableColumn>
            </TableHeader>
            <TableBody>
              {billingData.map((item, index) => (
                <TableRow key={index} className="border-b border-slate-700">
                  <TableCell>
                    <h2>{item.featureName}</h2>
                  </TableCell>
                  <TableCell>
                    <h2>{item.date}</h2>
                  </TableCell>
                  <TableCell>
                    <h2>{item.amount}</h2>
                  </TableCell>
                  <TableCell className="text-xs">
                    {item.status === "Processing" && <span className="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full">Processing</span>}
                    {item.status === "Paid" && <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-full">Paid</span>}
                    {item.status === "Pending" && <span className="bg-red-500/20 text-red-400 px-3 py-1 rounded-full">Pending</span>}
                  </TableCell>
                  <TableCell>{item.transactionId}</TableCell>
                  <TableCell>
                    <Button variant="ghost" className="text-xs flex items-center gap-1 border border-slate-600 rounded-lg">
                      Download Invoice
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3" />
                      </svg>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Powered by Stripe */}
      <div className="flex justify-center items-center gap-2 text-xs text-gray-500">
        <span>Powered by</span>
        <Image src="/images/stripe.png" alt="Stripe" width={60} height={60} />
      </div>
    </div>
  );
};