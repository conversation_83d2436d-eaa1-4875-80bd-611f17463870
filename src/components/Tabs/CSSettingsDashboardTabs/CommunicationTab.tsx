import { Switch } from "@heroui/switch";

export const CommunicationTab = () => {
  return (
    <div className="p-1 rounded-2xl">
      <div className="space-y-4">
        {/* WhatsApp Reminders Card */}
        <div className="p-4 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between gap-2 md:gap-0">
            <div className="space-y-2">
              <h2 className="text-sm ">Service expiration reminders via WhatsApp</h2>
              <p className="text-xs text-muted-foreground">
                By enabling this feature, you agree to the use of your phone number for reminder notifications on WhatsApp. You can revoke your consent at any
                time.
              </p>
            </div>
            <Switch className="text-xs" />
          </div>
        </div>

        {/* Email Updates Card */}
        <div className="p-4 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between gap-2 md:gap-0">
            <div className="space-y-2">
              <h2 className="text-sm ">Account Updates & Special Offers via Email</h2>
              <p className="text-xs text-muted-foreground">
                By enabling updates, you agree to receive information about your account and stay informed about our latest features and special offers.
              </p>
            </div>
            <Switch className="text-xs" />
          </div>
        </div>
      </div>
    </div>
  );
};
