import React, { useState, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Button, Input, Switch } from "@heroui/react";
import toast from "react-hot-toast";
import { FormInput, FormTextArea, FormSelect } from "@/components/Forms/commons";
import { ClinicFormType } from "@/lib/utils/validations";
import { useGetClinicProfile } from "@/hooks/useClinicSolution";
import { languageSpokenOptions } from "@/config/constants";

export const ClinicInfoTab = () => {
  const { data: clinicProfile, isLoading: isLoadingProfile } = useGetClinicProfile(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ClinicFormType>({
    defaultValues: {
      name: "",
      license_number: "",
      language_spoken: "en",
      date_of_incorporation: "",
      additional_email: "",
      insurance_accepted: false,
      contact_number: "",
      email: "",
      location: {
        city: "",
        zip_code: "",
        address: "",
        country: "",
      },
      logo: "",
      about: "",
    },
    values: clinicProfile,
  });

  const { control, handleSubmit, setValue, watch, reset } = form;

  // Update form when clinicProfile data is available
  useEffect(() => {
    if (clinicProfile) {
      reset(clinicProfile);
    }
  }, [clinicProfile, reset]);

  const onSubmit = (data: ClinicFormType) => {
    setIsLoading(true);
    console.log("Form submitted with data:", data);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      toast.success("Clinic information updated successfully");
    }, 1000);
  };

  const handleUploadLogo = (url: string) => {
    setValue("logo", url);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="p-4 border space-y-2 dark:border-slate-800 rounded-xl">
          <h2 className="font-medium">Clinic Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput placeholder="Clinic Name" name="name" control={control} type="text" />
            <FormInput placeholder="License Number" name="license_number" control={control} type="text" />
            <FormSelect placeholder="Select Language" name="language_spoken" control={control} options={languageSpokenOptions} />
            <div>
              <Controller
                name="date_of_incorporation"
                control={control}
                render={({ field }) => {
                  const [dateType, setDateType] = useState("text");

                  return (
                    <div className="w-full">
                      <Input
                        classNames={{
                          input: "text-xs",
                          label: "text-xs text-gray-500",
                          inputWrapper: "dark:bg-slate-900",
                        }}
                        type={dateType}
                        placeholder="Date of Incorporation"
                        value={field.value || ""}
                        onFocus={() => setDateType("date")}
                        onBlur={(e) => {
                          field.onBlur();
                          if (!e.target.value) setDateType("text");
                        }}
                        onChange={field.onChange}
                      />
                    </div>
                  );
                }}
              />
            </div>
            <FormInput placeholder="Additional Email" name="additional_email" control={control} type="email" />
            <div className="flex items-center space-x-2">
              <Controller
                name="insurance_accepted"
                control={control}
                render={({ field }) => <Switch size="sm" id="insurance" checked={field.value} onChange={field.onChange} />}
              />
              <label className="text-xs" htmlFor="insurance">
                Insurance Accepted
              </label>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="p-4 border space-y-2 dark:border-slate-800 rounded-xl">
          <h2 className=" font-medium ">Address & Contact Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput placeholder="Contact Number" name="contact_number" control={control} type="tel" />
            <FormInput placeholder="City" name="location.city" control={control} type="text" />
            <FormInput placeholder="Zip Code" name="location.zip_code" control={control} type="text" />
            <FormInput placeholder="Email" name="email" control={control} type="email" />
            <FormInput placeholder="Address" name="location.address" control={control} type="text" />
            <FormInput placeholder="Country" name="location.country" control={control} type="text" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {/* Upload Clinic Logo Section */}
        <div className="space-y-3 bg-white dark:bg-[#030712] rounded-2xl p-3 md:p-4 border border-gray-200 dark:border-gray-700 h-fit">
          <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-600 pb-2">
            <h2 className="font-medium">Upload Clinic Logo</h2>
          </div>
          <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg h-48 flex justify-center items-center">
            {/* Logo upload functionality would go here */}
            <div className="flex flex-col items-center justify-center h-full">
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Drag and drop or Upload from Files</p>
              {/* Uncomment when LogoImageUpload component is available */}
              {/* <LogoImageUpload currentImage={watch("logo")} onUpload={handleUploadLogo} /> */}
            </div>
          </div>
        </div>

        {/* Additional Information Section */}
        <div className="p-4 border space-y-2 dark:border-slate-800 rounded-xl">
          <h2 className="font-medium">Additional Information</h2>
          <FormTextArea customClassNames={{ input: "min-h-[100px]" }} placeholder="Write Here..." name="about" control={control} />
        </div>
      </div>

      <div className="flex justify-end mt-8">
        <Button
          size="sm"
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg flex items-center gap-2 w-full md:w-fit"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Saving...</span>
            </>
          ) : (
            <span>Save Changes</span>
          )}
        </Button>
      </div>
    </form>
  );
};
