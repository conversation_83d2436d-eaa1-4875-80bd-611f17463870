"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Input } from "@heroui/input";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@heroui/react";
import { Checkbox } from "@heroui/react";

export const VerificationTab = () => {
  return (
    <div className="space-y-6">
      {/* Two Column Layout for Legal and Representative Info */}
      <div className="grid md:grid-cols-2 gap-8 ">
        {/* Clinic's Legal Information Section */}
        <div className="space-y-2">
          <h2 className="font-medium">Clinic's Legal Information</h2>
          <div className="space-y-4">
            <Input
              className="text-xs"
              placeholder="Clinic/Organization Name"
              classNames={{
                input: "bg-transparent text-xs",
                inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
              }}
            />
            <div className="grid md:grid-cols-2 gap-4">
              <Input
                className="text-xs"
                placeholder="Registration/License Number"
                classNames={{
                  input: "bg-transparent text-xs",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
              <Input
                className="text-xs"
                placeholder="mm/dd/yyyy"
                type="date"
                classNames={{
                  input: "bg-transparent text-xs",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <Input
                className="text-xs"
                placeholder="City & Country of Incorporation"
                classNames={{
                  input: "bg-transparent text-xs  ",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
              <Input
                className="text-xs"
                placeholder="Registered Address"
                classNames={{
                  input: "bg-transparent text-xs",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
            </div>
          </div>
        </div>

        {/* Clinic's Authorized Representative Information */}
        <div className="space-y-2">
          <h2 className="font-medium">Clinic's Authorized Representative Information</h2>
          <div className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <Input
                className="text-xs"
                placeholder="Full Name"
                classNames={{
                  input: "bg-transparent text-xs",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
              <Input
                className="text-xs"
                placeholder="Position/Role"
                classNames={{
                  input: "bg-transparent text-xs",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <Input
                className="text-xs"
                placeholder="Employee ID"
                classNames={{
                  input: "bg-transparent text-xs",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
              <Input
                className="text-xs"
                placeholder="Contact Email"
                type="email"
                classNames={{
                  input: "bg-transparent text-xs",
                  inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
                }}
              />
            </div>
            <Input
              className="text-xs"
              placeholder="Phone Number"
              type="tel"
              classNames={{
                input: "bg-transparent text-xs",
                inputWrapper: "bg-transparent border border-gray-200 dark:border-gray-800",
              }}
            />
          </div>
        </div>
      </div>

      {/* Upload Documents Section */}
      <div className="space-y-2">
        <h2 className="font-medium">Upload Clinic's Verification Documents</h2>
        <p className="text-xs text-gray-500 mb-4">Upload any one of the following documents to verify your organization</p>
        <ul className="list-disc text-xs text-gray-500 ml-5 mb-4">
          <li>Business License</li>
          <li>Government Registration Certificate</li>
          <li>Tax Registration Certificate</li>
        </ul>

        {/* Documents Table */}
        <div className="rounded-md">
          <Table removeWrapper className="dark:bg-slate-950" aria-label="Documents Table">
            <TableHeader>
              <TableColumn className="w-[50px]">
                <Checkbox aria-label="Select all" />
              </TableColumn>
              <TableColumn>File Name</TableColumn>
              <TableColumn>Date</TableColumn>
              <TableColumn>Actions</TableColumn>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>-</TableCell>
                <TableCell>-</TableCell>
                <TableCell>-</TableCell>
                <TableCell>-</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* Upload Box */}
        <div className="border-2 border-dashed border-gray-200 dark:border-gray-800 rounded-lg p-8 mt-4 flex flex-col justify-center items-center">
          <p className="text-center text-xs text-gray-500 mb-2">No document(s) uploaded yet</p>
          <div className="flex justify-end">
            <Button size="sm" className="w-fit bg-blue-600 hover:bg-blue-700 text-white gap-2">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 5v14M5 12h14" />
              </svg>
              Upload
            </Button>
          </div>
        </div>
      </div>

      {/* Confirmation Checkbox */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox id="confirmation" />
          <label htmlFor="confirmation" className="text-xs">
            I confirm that the information provided is accurate and that I am authorized to act on behalf of this organization.
          </label>
        </div>
        <div>
          <Button className="text-blue-500 text-xs bg-transparent border-none">View Terms & Conditions</Button>
        </div>
      </div>
    </div>
  );
};
