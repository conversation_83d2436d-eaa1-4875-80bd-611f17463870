import React from "react";
import { Switch } from "@heroui/react";
import { FormInput } from "@/components/Forms/commons";
import { useForm, Controller } from "react-hook-form";

export const AppointmentInfoTab = () => {
  const form = useForm({
    defaultValues: {
      defaultDuration: "",
      bufferTime: "",
      enableOnlineBooking: false,
    },
  });

  const { control } = form;

  return (
    <div className="p-3 border space-y-2 border-gray-200 dark:border-gray-700 rounded-md">
      <h2 className="font-medium">Appointment Settings</h2>

      <div className="flex gap-2 mb-3">
        <FormInput placeholder="Default Appointment Duration" name="defaultDuration" control={control} type="text" />

        <FormInput placeholder="Buffer Time Between Appointments" name="bufferTime" control={control} type="text" />
      </div>
      <div className="flex items-center gap-2">
        <Controller name="enableOnlineBooking" control={control} render={({ field }) => <Switch size="sm" checked={field.value} onChange={field.onChange} />} />
        <label className="text-xs">Enable Online Booking</label>
      </div>
    </div>
  );
};
