"use client";
import CardContainer from "@/components/Card";
import { FormDatePicker, FormInput, FormSelect } from "@/components/Forms/commons";
import SubmitButton from "@/components/SubmitButton";
import { dependentOptions, insuranceTypes } from "@/config/constants";
import { useCreateInsurance, useDeleteInsurance, useGetInsurance, useUpdateInsurance } from "@/hooks/settings-dashboard/useInsurance";
import { InsuranceFormType, InsuranceFormValidation } from "@/lib/utils/validations";
import { Button } from "@heroui/button";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Modal, ModalBody, ModalContent, ModalHeader, useDisclosure } from "@heroui/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

export const Insurance = () => {
  const { t } = useTranslation();
  const { data: insurance, isLoading } = useGetInsurance();
  const { mutateAsync: createInsurance, isPending } = useCreateInsurance();
  const { mutateAsync: updateInsurance, isPending: isUpdating } = useUpdateInsurance();
  const { mutateAsync: deleteInsurance, isPending: isDeleting } = useDeleteInsurance();

  // Check if the insurance limit is reached (max 3)
  const isLimitReached = insurance?.length >= 3;

  // Main form for adding new insurance
  const form = useForm<InsuranceFormType>({
    shouldUnregister: true,
    resolver: zodResolver(InsuranceFormValidation),
    mode: "onSubmit",
  });

  // State to track the currently selected insurance for editing
  const [selectedInsurance, setSelectedInsurance] = useState<InsuranceFormType | null>(null);

  // Separate form for editing insurance
  const editForm = useForm<InsuranceFormType>({
    shouldUnregister: true,
    resolver: zodResolver(InsuranceFormValidation),
    mode: "onSubmit",
  });

  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const { control, handleSubmit, reset } = form;
  const { control: editControl, handleSubmit: handleEditSubmit, reset: resetEditForm } = editForm;

  // Handle opening the edit modal
  const handleOpenEditModal = (card: InsuranceFormType) => {
    setSelectedInsurance(card);
    resetEditForm(card); // Set the edit form values to the selected insurance
    onOpen();
  };

  // Handle closing the modal
  const handleCloseModal = () => {
    setSelectedInsurance(null);
    resetEditForm(); // Reset the edit form
    onOpenChange();
  };

  const onSubmit = (data: InsuranceFormType) => {
    createInsurance(data);
    reset();
  };

  const onEditSubmit = (data: InsuranceFormType) => {
    if (selectedInsurance?.id) {
      data.id = selectedInsurance.id;
      updateInsurance(data);
      handleCloseModal();
    }
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 p-2">
        <div className="grid grid-cols-12 md:grid-cols-6 rounded-2xl gap-4 w-full">
          {/* Add New Insurance Card */}
          <CardContainer title="Add New Insurance" classNames={{ body: "w-full h-full gap-1" }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput name="provider" control={control} label={t("ravid.ravidSettings.insurance.insuranceProvider")} required disabled={isLimitReached} />

              <FormInput
                name="policy_number"
                control={control}
                label={t("ravid.ravidSettings.insurance.insurancePolicyNumber")}
                required
                disabled={isLimitReached}
              />

              <FormSelect
                name="type"
                control={control}
                label={t("ravid.ravidSettings.insurance.insuranceType")}
                options={insuranceTypes}
                required
                disabled={isLimitReached}
              />

              <FormInput
                name="policy_holder_name"
                control={control}
                label={t("ravid.ravidSettings.insurance.policyHolderName")}
                required
                disabled={isLimitReached}
              />

              <FormDatePicker name="start_date" control={control} label={t("ravid.ravidSettings.insurance.startDate")} isDisabled={isLimitReached} />

              <FormDatePicker name="end_date" control={control} label={t("ravid.ravidSettings.insurance.endDate")} isDisabled={isLimitReached} />

              <FormSelect
                name="dependent_information"
                control={control}
                label={t("ravid.ravidSettings.insurance.dependentInformation")}
                options={dependentOptions}
                disabled={isLimitReached}
              />

              <FormInput name="group_number" control={control} label={t("ravid.ravidSettings.insurance.groupNumber")} disabled={isLimitReached} />

              <FormInput name="deductable_amount" control={control} label={t("ravid.ravidSettings.insurance.deductibleAmount")} disabled={isLimitReached} />

              <FormInput name="copay_amount" control={control} label={t("ravid.ravidSettings.insurance.copayAmount")} disabled={isLimitReached} />
            </div>
            <div className="flex w-full justify-end mt-4">
              {isLimitReached && (
                <div className="flex items-center bg-amber-100 border border-amber-400 text-amber-700 px-3 py-1 rounded-md mr-auto">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm font-medium">You can only add up to 3 insurances</span>
                </div>
              )}
              <SubmitButton variant="solid" color="primary" isLoading={isPending} type="submit" label={t("ravid.ravidSettings.insurance.save")} disabled={isLimitReached} />
            </div>
          </CardContainer>
          {/* Insurance Information Card */}
          <CardContainer title="Insurance Information" classNames={{ body: "w-full h-full gap-1" }}>
            {/* <CardHeader className="border-b dark:border-slate-800">
              <h1>{t("ravid.ravidSettings.insurance.insuranceInformation")}</h1>
            </CardHeader> */}
            <CardBody>
              {insurance?.map((card: InsuranceFormType) => (
                <div key={card.id} className="mb-4 w-full">
                  <Button onPress={() => handleOpenEditModal(card)} className="w-full h-36 p-0 bg-transparent">
                    <Card className="dark:bg-[#060C1B] w-full border rounded-xl dark:border-slate-800 cursor-pointer p-4">
                      <CardBody className="w-full">
                        <h2 className="text-lg font-semibold truncate">{card.type}</h2>
                        <div className="text-sm mt-2 w-full">
                          <p className="flex justify-between">
                            <span>{t("ravid.ravidSettings.insurance.insuranceProvider")}:</span>{" "}
                            <span className="font-medium truncate ml-2">{card.provider}</span>
                          </p>
                          <p className="flex justify-between">
                            <span>{t("ravid.ravidSettings.insurance.policy")}:</span> <span className="font-medium truncate ml-2">{card.policy_number}</span>
                          </p>
                          <p className="flex justify-between">
                            <span className="font-medium">{card.start_date}</span> - <span className="font-medium">{card.end_date}</span>
                          </p>
                        </div>
                      </CardBody>
                    </Card>
                  </Button>

                  {selectedInsurance?.id === card.id && (
                    <Modal isOpen={isOpen} onOpenChange={handleCloseModal}>
                      <ModalContent className="dark:bg-[#060C1B]">
                        <ModalHeader>
                          <h1>{t("ravid.ravidSettings.insurance.editInsurance")}</h1>
                        </ModalHeader>
                        <ModalBody>
                          <FormProvider {...editForm}>
                            <form onSubmit={handleEditSubmit(onEditSubmit)}>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
                                <FormInput name="provider" control={editControl} label={t("ravid.ravidSettings.insurance.insuranceProvider")} />
                                <FormInput name="policy_number" control={editControl} label={t("ravid.ravidSettings.insurance.insurancePolicyNumber")} />
                                <FormSelect
                                  name="type"
                                  control={editControl}
                                  label={t("ravid.ravidSettings.insurance.insuranceType")}
                                  options={insuranceTypes}
                                />
                                <FormInput name="policy_holder_name" control={editControl} label={t("ravid.ravidSettings.insurance.policyHolderName")} />
                                <FormDatePicker name="start_date" control={editControl} label={t("ravid.ravidSettings.insurance.startDate")} />
                                <FormDatePicker name="end_date" control={editControl} label={t("ravid.ravidSettings.insurance.endDate")} />
                                <FormSelect
                                  name="dependent_information"
                                  control={editControl}
                                  label={t("ravid.ravidSettings.insurance.dependentInformation")}
                                  options={dependentOptions}
                                />
                                <FormInput name="group_number" control={editControl} label={t("ravid.ravidSettings.insurance.groupNumber")} />
                                <FormInput
                                  name="deductable_amount"
                                  control={editControl}
                                  label={t("ravid.ravidSettings.insurance.deductibleAmount")}
                                  type="number"
                                />

                                <FormInput name="copay_amount" control={editControl} label={t("ravid.ravidSettings.insurance.copayAmount")} type="number" />
                              </div>
                              <div className="flex w-full justify-end space-x-2">
                                <SubmitButton
                                  type="button"
                                  variant="solid"
                                  className="bg-red-700 hover:bg-red-800 text-white text-xs"
                                  label="Delete"
                                  onClick={() => {
                                    handleCloseModal();
                                    deleteInsurance(card?.id || "");
                                  }}
                                  isLoading={isDeleting}
                                />
                                <SubmitButton type="submit" variant="solid" color="primary" label="Update" isLoading={isUpdating} />
                              </div>
                            </form>
                          </FormProvider>
                        </ModalBody>
                      </ModalContent>
                    </Modal>
                  )}
                </div>
              ))}
            </CardBody>
          </CardContainer>
        </div>
      </form>
    </FormProvider>
  );
};
