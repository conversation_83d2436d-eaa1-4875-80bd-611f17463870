import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardBody, Button, Input, Select, SelectItem } from "@heroui/react";
import { useAccess, useGetSharedAccess, useRequestAccess, useRevokeAccess } from "@/hooks/settings-dashboard/useAccess";

export const AccountSharing = () => {
  const [userId, setUserId] = useState("");
  const [email, setEmail] = useState("");
  const { mutate: shareAccess } = useAccess();
  const { data: sharedAccess } = useGetSharedAccess();
  const { mutate: requestAccess, isPending } = useRequestAccess();
  const { mutate: revokeAccess } = useRevokeAccess();

  const permissions = [
    { key: "settings", label: "Settings" },
    { key: "ai", label: "AI" },
    { key: "payments", label: "Payments" },
    { key: "diagnosis", label: "Diagnosis" },
    { key: "prescription", label: "Prescription" },
    { key: "subscriptions", label: "Subscriptions" },
    { key: "services", label: "Services" },
    { key: "publicProfile", label: "Public Profile" },
  ];

  const handleShareAccess = () => {
    if (userId) {
      shareAccess(userId);
    }
  };

  const handleRequestAccess = () => {
    if (email) {
      requestAccess(email);
    }
  };

  const handleRevokeAccess = (id: string) => {
    revokeAccess(id);
  };

  const [values, setValues] = React.useState(new Set(["cat", "dog"]));
  return (
    <div className="flex flex-col gap-6 p-2">
      {/* Request Access Section */}
      <Card className="w-full dark:bg-slate-950 shadow-lg border-t">
        <CardHeader className="flex flex-col items-start gap-2">
          <h2>Grant Access</h2>
          <span className="muted">
            Choose this option if you have a person creating or managing or having access to your R.A.V.I.D account. When the access to your account is granted,
            a confirmation email is sent to the email address provided below.
          </span>
        </CardHeader>
        <CardBody className="flex gap-4">
          <div className="flex flex-col md:flex-row gap-4 w-full md:w-2/3">
            <Input
              classNames={{
                input: "text-xs",
                label: "text-xs text-gray-500",
                inputWrapper: "dark:bg-slate-900",
                base: "w-full",
              }}
              type="text"
              label="User ID"
              placeholder="Enter user id"
              value={userId}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleShareAccess();
                }
              }}
              onChange={(e) => setUserId(e.target.value)}
              size="sm"
            />
            <Select
              classNames={{
                trigger: "dark:bg-slate-900",
                label: "text-xs text-gray-500",
                base: "w-full",
                value: "text-xs",
              }}
              size="sm"
              className="max-w-xs"
              label="Access Level"
              selectedKeys={values}
              selectionMode="multiple"
              onSelectionChange={(keys) => setValues(keys as Set<string>)}
            >
              {permissions.map((permission) => (
                <SelectItem key={permission.key}>{permission.label}</SelectItem>
              ))}
            </Select>
          </div>
          <Button size="sm" color="primary" className="w-full sm:w-fit" onPress={handleShareAccess}>
            Grant Access
          </Button>
        </CardBody>
      </Card>

      {/* Grant Access Section */}
      <Card className="w-full dark:bg-slate-950 shadow-lg border-t ">
        <CardHeader className="flex flex-col items-start gap-2">
          <h2>Request Access</h2>
          <span className="muted">
            Choose this option if there is another R.A.V.I.D. account you would like to manage. A request email will be sent to the email address.
          </span>
        </CardHeader>
        <CardBody className="flex flex-col gap-4">
          <Input
            required
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500",
              inputWrapper: "dark:bg-slate-900",
              base: "w-full sm:w-2/5",
            }}
            type="email"
            label="Email Address"
            placeholder="Enter email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            size="sm"
          />

          <Button size="sm" color="primary" className="w-full sm:w-fit" isLoading={isPending} onPress={handleRequestAccess}>
            Requst Access
          </Button>
        </CardBody>
      </Card>

      {/* Users with Access Section */}
      <Card className="w-full dark:bg-slate-950 shadow-lg">
        <CardHeader>
          <h2>Users that have access to your account:</h2>
        </CardHeader>
        <CardBody>
          {sharedAccess?.map((user: any) => (
            <div className="flex flex-col sm:flex-row sm:items-center justify-between sm:p-4 sm:border-b dark:border-gray-800 gap-4 sm:gap-0">
              <div className="flex items-center justify-between w-full gap-4">
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white flex-shrink-0">
                    {user?.granted_to?.email?.charAt(0)}
                  </div>
                  <div className="flex flex-col min-w-0">
                    <span className="text-sm truncate">{user?.granted_to?.email}</span>
                    <span className="text-xs text-gray-400">Granted on: {new Date(user?.created_at).toLocaleString()}</span>
                  </div>
                </div>
                <Button color="danger" variant="flat" size="sm" className="w-full sm:w-auto" onPress={() => handleRevokeAccess(user?.id)}>
                  Revoke Access
                </Button>
              </div>
            </div>
          ))}
        </CardBody>
      </Card>
    </div>
  );
};
