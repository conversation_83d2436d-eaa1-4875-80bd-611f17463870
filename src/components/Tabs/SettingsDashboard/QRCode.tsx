"use client";
import QRCodeGenerator from "@/components/QRCodeGenerator";
import SimpleQRCode from "@/components/SimpleQRCode";
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, <PERSON>dal, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON>eader, <PERSON>dalBody, ModalFooter, useDisclosure, Link } from "@heroui/react";
import React, { useRef, useState } from "react";
import { useGetUser } from "@/hooks/useUser";
import { useGenerateQRCode } from "@/hooks/settings-dashboard/useQRCode";
import { useTranslation } from "react-i18next";

export const QRCode = () => {
  const { t } = useTranslation();
  const qrRef = useRef<HTMLDivElement>(null);
  const [qrError, setQrError] = useState(false);
  const [useSimpleQR, setUseSimpleQR] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { data: user } = useGetUser();
  const { data: qrCodeData } = useGenerateQRCode();
  const [token, setToken] = useState<string | null>(null);

  const handleOpen = () => {
    setQrError(false);
    onOpen();
  };

  const handleClose = () => {
    if (qrRef.current) {
      qrRef.current.innerHTML = "";
    }
    setToken(null);
    onClose();
  };

  const handleError = (hasError: boolean) => {
    setQrError(hasError);
    if (hasError) {
      setUseSimpleQR(true);
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 gap-6 p-2">
        <Card className="w-full dark:bg-[#060C1B] rounded-xl dark:border-slate-800">
          <CardHeader className="border-b dark:border-slate-800">
            <Button size="sm" color="primary" variant="shadow" className="w-full sm:w-fit text-xs" onPress={handleOpen}>
              {t("ravid.ravidSettings.qrCode.myEmergencyQRCode")}
            </Button>
          </CardHeader>
          <CardBody>
            <span className="muted">
              Disclaimer: Information captured in this QR code is generated from the last server saved content that resides within the Personal Info, Emergency
              Info & Medical Team section of your R.A.V.I.D. account, in addition to your personalized UID number. Please exercise care & caution with whoever
              you wish to share your valuable information.
            </span>
          </CardBody>
        </Card>
      </div>

      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        classNames={{
          base: "dark:bg-[#060C1B]",
          backdrop: "bg-black/50 backdrop-blur-sm",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col items-center justify-center gap-1 border-b text-sm dark:border-slate-800">
            <h2>Scan QR Code</h2>
            <p>Scan this QR Code to share your Personal Information</p>
          </ModalHeader>
          <ModalBody>
            {useSimpleQR ? (
              <SimpleQRCode onError={handleError} onTokenGenerated={setToken} />
            ) : (
              <>
                <QRCodeGenerator qrRef={qrRef} onError={handleError} isModalOpen={isOpen} onTokenGenerated={setToken} />
                {!qrError && (
                  <div
                    ref={qrRef}
                    id="qr-code-container"
                    className="flex justify-center items-center  w-full border-gray-300 dark:border-gray-700 rounded-lg p-2"
                  />
                )}
              </>
            )}

            {qrError && !useSimpleQR && <div className="text-center text-red-500 p-4">Unable to generate styled QR code. Switching to simple version...</div>}
          </ModalBody>
          <ModalFooter className="border-t dark:border-slate-800">
            {!qrError && user?.user_id && (qrCodeData?.token || token) && (
              <Link
                target="_blank"
                href={`${process.env.NEXT_PUBLIC_APP_URL}/share/${user.user_id}?token=${token || qrCodeData?.token}`}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                Open Link
              </Link>
            )}
            <Button size="sm" className="bg-red-700 hover:bg-red-800 text-white text-xs" onPress={handleClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
