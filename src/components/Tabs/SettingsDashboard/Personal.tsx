"use client";
import CardContainer from "@/components/Card";
import { FormDatePicker, FormInput, FormSelect } from "@/components/Forms/commons";
import ProfileImageUpload from "@/components/Forms/PublicProfile/ProfileImageUpload";
import LanguageToggle from "@/components/LanguageToggle";
import SubmitButton from "@/components/SubmitButton";
import { bloodTypes, genderOptions, yesNoOptions } from "@/config/constants";
import { useUpdateUser } from "@/hooks/useUser";
import { PersonalFormType } from "@/lib/utils/validations";
import { useStore } from "@/store/store";
import { Button } from "@heroui/button";
import { Textarea } from "@heroui/input";
import { Tooltip } from "@heroui/tooltip";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

export const Personal = () => {
  const { t } = useTranslation();
  // const { data: user, isLoading } = useGetUser();
  const { user, setUser } = useStore();
  const { mutate: updateUser, isPending } = useUpdateUser();

  const form = useForm<PersonalFormType>({
    shouldUnregister: false,
    mode: "onChange",
    values: {
      first_name: user?.first_name || "",
      middle_name: user?.middle_name || "",
      last_name: user?.last_name || "",
      dob: user?.dob || "",
      language: user?.language || "",
      blood_group: user?.blood_group || "",
      gender: user?.gender || "",
      genome_tested: user?.genome_tested || false,
      address: user?.address || "",
      city: user?.city || "",
      state: user?.state || "",
      country: user?.country || "",
      zipcode: user?.zipcode || "",
      mobile: user?.mobile || "",
      private_profile_picture: user?.private_profile_picture || "",
    },
  });

  const { control, handleSubmit } = form;

  const onSubmit = (data: PersonalFormType) => {
    const { private_profile_picture, ...dataToUpdate } = data;
    const payload = {
      ...dataToUpdate,
      user_id: user?.user_id,
    };
    updateUser(payload as User);
  };

  // if (isLoading) return <SpinnerLoading />;
  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 p-2">
        <div className="grid grid-cols-12 md:grid-cols-6 rounded-2xl gap-4 w-full">
          {/* Personal Information */}
          <CardContainer title="ravid.ravidSettings.tabs.personal">
            <FormInput id="first_name" name="first_name" control={control} label={t("ravid.ravidSettings.personal.firstName")} />
            <FormInput name="middle_name" control={control} label={t("ravid.ravidSettings.personal.middleName")} />
            <FormInput name="last_name" control={control} label={t("ravid.ravidSettings.personal.lastName")} />
            <FormDatePicker name="dob" control={control} label={t("ravid.ravidSettings.personal.dateOfBirth")} />
            {/* <FormSelect name="language" control={control} label={t("ravid.ravidSettings.personal.language")} options={languages} /> */}
            <LanguageToggle
              label={t("ravid.ravidSettings.personal.language")}
              className={{ base: "justify-start", trigger: "dark:bg-slate-900 text-xs h-[56px]" }}
            />
            <FormSelect name="blood_group" control={control} label={t("ravid.ravidSettings.personal.bloodType")} options={bloodTypes} />
            <FormSelect name="gender" control={control} label={t("ravid.ravidSettings.personal.gender")} options={genderOptions} />
            <FormSelect name="genome_tested" control={control} label={t("ravid.ravidSettings.personal.genomeTested")} options={yesNoOptions} />
          </CardContainer>
          {/* Address Information */}
          <CardContainer title="ravid.ravidSettings.personal.personalAddress">
            <div className="col-span-2">
              <FormInput name="address" control={control} label={t("ravid.ravidSettings.personal.address")} />
            </div>
            <FormInput name="city" control={control} label={t("ravid.ravidSettings.personal.city")} />
            <FormInput name="state" control={control} label={t("ravid.ravidSettings.personal.state")} />
            <FormInput name="country" control={control} label={t("ravid.ravidSettings.personal.country")} />
            <FormInput name="zipcode" control={control} label={t("ravid.ravidSettings.personal.zipCode")} />
            <FormInput name="mobile" control={control} label={t("ravid.ravidSettings.personal.mobilePhone")} />
            <Tooltip content="Update your email in Access & Security" placement="bottom-end" className="text-xs">
              <Button disabled={true} className="text-xs justify-start dark:bg-slate-900 text-left" variant="solid" size="lg" radius="sm">
                {user?.email}
              </Button>
            </Tooltip>
          </CardContainer>
          {/* Profile Picture */}
          <CardContainer title="ravid.ravidSettings.personal.personalProfilePicture">
            <div className="col-span-full flex justify-center mb-4">
              <ProfileImageUpload value="private_profile_picture" currentImage={user?.private_profile_picture || null} className="rounded-full w-32 h-32" />
            </div>
          </CardContainer>
          {/* Additional Information */}
          <CardContainer title="ravid.ravidSettings.personal.additionalInformation" classNames={{ body: "w-full h-full gap-1" }}>
            <Textarea
              minRows={10}
              classNames={{
                input: `text-xs`,
                label: `text-xs text-gray-500 placeholder:text-gray-500`,
                inputWrapper: `dark:bg-slate-900`,
              }}
              name="additional_data"
              placeholder={t("ravid.ravidSettings.personal.additionalInformation")}
            />
          </CardContainer>
        </div>
        <div className="flex justify-end col-span-6">
          <SubmitButton color="primary" isLoading={isPending} isLoadingValue="Updating..." size="sm" type="submit" label="Save" />
        </div>
      </form>
    </FormProvider>
  );
};
