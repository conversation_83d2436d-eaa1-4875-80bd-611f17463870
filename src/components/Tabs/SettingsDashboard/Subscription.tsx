"use client";

import { But<PERSON> } from "@heroui/button";
import { <PERSON>, CardHeader, CardBody } from "@heroui/card";
import { ChevronUp, ChevronDown } from "lucide-react";
import { useState } from "react";
import Disclaimer from "../../Disclaimer";

export const Subscription = () => {
  const [showDetails, setShowDetails] = useState(false);

  const mockPlans = [
    {
      name: "AI Analysis",
      price: "20",
      features: ["Any thing to mention about the subscription with brief", "Any thing to mention about the subscription with brief"],
    },
    {
      name: "Data Storage",
      price: "5",
      features: ["Any thing to mention about the subscription with brief", "Any thing to mention about the subscription with brief"],
    },
    {
      name: "R.A.V.I.D. Premium Services",
      price: "1",
      features: ["Any thing to mention about the subscription with brief", "Any thing to mention about the subscription with brief"],
      showDetailsButton: true,
    },
  ];

  const GenomicsDetailsCard = () => (
    <Card className="w-full bg-white dark:bg-slate-900 animate-in slide-in-from-top-4 duration-300 border border-gray-200 dark:border-gray-800">
      <CardBody className="space-y-4">
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-800 pb-4">
          <h3 className="text-xs font-medium text-gray-900 dark:text-white">Premium Genomic Analysis Details</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(false)}
            className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <ChevronUp className="h-4 w-4" />
            Show Less
          </Button>
        </div>

        <div className="space-y-4">
          <p className="text-xs text-gray-600 dark:text-gray-300">
            In association with an institute of excellence, we offer a complete Genome analysis via a dedicated GPU based server. This service analyzes the
            majority of the variants including alignment calling.
          </p>
          {/* ... existing details content ... */}
        </div>
      </CardBody>
    </Card>
  );

  return (
    <>
      <Disclaimer />
      <div className="space-y-6 max-w-7xl p-1 mx-auto dark:bg-slate-950 border rounded-xl px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-2 mb-8">
          <h1>Premium Features "Pay for"</h1>
          <p className="muted">Enhance your experience with our essential premium features</p>
        </div>

        {/* Active Subscriptions Table */}
        <div className="dark:bg-slate-950 bg-white rounded-lg space-y-3">
          <h3 className="text-xs font-medium text-gray-900 dark:text-white">Active Subscriptions</h3>

          <div className="overflow-hidden rounded-lg border border-gray-300 dark:border-gray-700">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
              <thead className="bg-gray-100 dark:bg-slate-800">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400">Date</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400">Name</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400">Amount</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400">Status</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-300 dark:divide-gray-700 bg-white dark:bg-slate-900">
                <tr>
                  <td colSpan={5} className="px-3 py-4 text-sm text-gray-500 text-center">
                    No active subscriptions
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Subscription Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {mockPlans.map((plan, index) => (
            <Card key={index} className="border-t-2 border-blue-500 shadow-sm max-w-[320px] mx-auto w-full">
              <CardHeader>
                <h3 className="text-xs font-semibold text-foreground">{plan.name}</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-2 flex-grow">
                  {plan.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <div className="w-3.5 h-3.5 rounded-full flex items-center justify-center">
                        <svg className="w-3.5 h-3.5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" />
                        </svg>
                      </div>
                      <span className="text-xs text-foreground">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-4">
                  <p className="text-xs font-medium text-foreground mb-2">
                    Price: ${plan.price} {plan.price === "1000" ? "per run" : "/ month"}
                  </p>

                  {plan.showDetailsButton && (
                    <Button
                      size="sm"
                      onPress={() => setShowDetails(!showDetails)}
                      className="w-full mb-2 bg-transparent border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
                    >
                      <span className="mr-2">{showDetails ? "Show Less" : "Learn More"}</span>
                      {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </Button>
                  )}

                  <Button size="sm" className="w-full bg-blue-600 rounded-lg hover:bg-blue-700 transition py-1.5 text-xs font-medium">
                    Subscribe
                  </Button>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>

        {showDetails && <GenomicsDetailsCard />}

        <div className="space-y-3 text-xs text-gray-500 mt-8">
          <p>
            * All subscriptions are billed monthly and can be canceled at any time. Your subscription will help support the platform and unlock premium
            features.
          </p>
          <p>
            ** DNA Analysis is based on a Pay for Service model, i.e. you pay for every time you request an analysis. As computational biology evolves so will
            be the understanding and the results of the analytical services.
          </p>
        </div>
      </div>
    </>
  );
};
