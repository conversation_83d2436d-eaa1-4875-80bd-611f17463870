import React, { useEffect } from "react";
import { Table, TableHeader, TableBody, TableColumn, TableRow, TableCell, Card, CardHeader, CardBody, Chip } from "@heroui/react";
import { useGetBillings } from "@/hooks/useTransactions";

export const BillingsTab = () => {
  const { data: billingHistory, isLoading, error } = useGetBillings();

  return (
    <div className="space-y-4 p-2 sm:p-4">
      <Card className="dark:bg-slate-950">
        <CardHeader>
          <h3 className="text-xs font-medium">Billing History</h3>
        </CardHeader>
        <CardBody>
          <Table
            removeWrapper
            className="dark:bg-slate-950"
            aria-label="Billing history table"
            classNames={{
              th: "dark:bg-slate-900",
            }}
          >
            <TableHeader>
              <TableColumn>
                <h2>DATE</h2>
              </TableColumn>
              <TableColumn>
                <h2>NAME</h2>
              </TableColumn>
              <TableColumn>
                <h2>AMOUNT</h2>
              </TableColumn>
              <TableColumn>
                <h2>STATUS</h2>
              </TableColumn>
              <TableColumn>
                <h2>INVOICE</h2>
              </TableColumn>
            </TableHeader>
            <TableBody>
              {billingHistory?.transactions?.map((transaction: any) => (
                <TableRow key={transaction.id}>
                  <TableCell  className="text-xs">{new Date(transaction.payload.status_transitions.paid_at * 1000).toLocaleDateString()}</TableCell>
                  <TableCell  className="text-xs">{transaction.payload.customer_name}</TableCell>
                  <TableCell  className="text-xs">${transaction.amount.toFixed(2)}</TableCell>
                  <TableCell  className="text-xs">
                    <Chip color={transaction.status === "success" ? "success" : "danger"} variant="flat" size="sm">
                      {transaction.status}
                    </Chip>
                  </TableCell>
                  <TableCell  className="text-xs">
                    <a href={transaction.payload.hosted_invoice_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:opacity-80">
                      View Invoice
                    </a>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>
    </div>
  );
};
