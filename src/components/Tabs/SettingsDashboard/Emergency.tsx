"use client";
import Card<PERSON>ontainer from "@/components/Card";
import { FormInput, FormSelect, FormTextArea } from "@/components/Forms/commons";
import FormTagInput from "@/components/Forms/commons/tag-input";
import SubmitButton from "@/components/SubmitButton";
import { bloodTypes } from "@/config/constants";
import { useGetEmergency, useUpdateEmergency } from "@/hooks/settings-dashboard/useEmergency";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

export const Emergency = () => {
  const { t } = useTranslation();
  const { data: emergency } = useGetEmergency();
  const { mutate: updateEmergency, isPending } = useUpdateEmergency();

  const form = useForm<EmergencyInfo>({
    shouldUnregister: true,
    mode: "onChange",
    values: emergency,
  });
  const { control, handleSubmit } = form;

  const onSubmit = (data: EmergencyInfo) => {
    // Ensure IDs are preserved when updating existing records
    if (emergency) {
      // Preserve contact information IDs
      if (emergency.contact_information && data.contact_information) {
        data.contact_information = data.contact_information.map((contact, index) => {
          return emergency.contact_information[index]?.id ? { ...contact, id: emergency.contact_information[index].id } : contact;
        });
      }

      // Preserve medical information ID
      if (emergency.medical_information?.id && data.medical_information) {
        data.medical_information.id = emergency.medical_information.id;
      }
    }

    updateEmergency(data);
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 p-2">
        <div className="grid grid-cols-12 md:grid-cols-6 rounded-2xl gap-4 w-full">
          {/* Emergency Contact Information */}
          <CardContainer title="ravid.ravidSettings.emergency.emergencyContactInformation" classNames={{ body: "w-full h-full gap-1" }}>
            <div className="mb-6">
              <h2>{t("ravid.ravidSettings.emergency.primaryEmergencyContact")}</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">
                <FormInput
                  size="sm"
                  name="contact_information.0.contact_name"
                  id="contact_information_0_contact_name"
                  control={control}
                  label={t("ravid.ravidSettings.emergency.contactName")}
                />
                <FormInput
                  size="sm"
                  name="contact_information.0.phone_number"
                  control={control}
                  label={t("ravid.ravidSettings.emergency.contactPhoneNumber")}
                />
                <FormInput size="sm" name="contact_information.0.email" control={control} label={t("ravid.ravidSettings.emergency.contactEmail")} />
                <FormInput size="sm" name="contact_information.0.relationship" control={control} label={t("ravid.ravidSettings.emergency.relationship")} />
              </div>
            </div>

            <div className="mb-6">
              <h2>{t("ravid.ravidSettings.emergency.additionalEmergencyContact")}</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">
                <FormInput size="sm" name="contact_information.1.contact_name" control={control} label={t("ravid.ravidSettings.emergency.contactName")} />
                <FormInput
                  size="sm"
                  name="contact_information.1.phone_number"
                  control={control}
                  label={t("ravid.ravidSettings.emergency.contactPhoneNumber")}
                />
                <FormInput size="sm" name="contact_information.1.email" control={control} label={t("ravid.ravidSettings.emergency.contactEmail")} />
                <FormInput size="sm" name="contact_information.1.relationship" control={control} label={t("ravid.ravidSettings.emergency.relationship")} />
              </div>
            </div>

            <div>
              <h2>{t("ravid.ravidSettings.emergency.nextOfKinContact")}</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">
                <FormInput size="sm" name="contact_information.2.contact_name" control={control} label={t("ravid.ravidSettings.emergency.contactName")} />
                <FormInput
                  size="sm"
                  name="contact_information.2.phone_number"
                  control={control}
                  label={t("ravid.ravidSettings.emergency.contactPhoneNumber")}
                />
                <FormInput size="sm" name="contact_information.2.email" control={control} label={t("ravid.ravidSettings.emergency.contactEmail")} />
                <FormInput size="sm" name="contact_information.2.relationship" control={control} label={t("ravid.ravidSettings.emergency.relationship")} />
              </div>
            </div>
          </CardContainer>
          {/* Additional Emergency Information */}
          <CardContainer title="ravid.ravidSettings.emergency.additionalEmergencyInformation" classNames={{ body: "w-full h-full gap-1" }}>
            <div className="space-y-4">
              <div className="space-y-2">
                <h2>{t("ravid.ravidSettings.emergency.allergies")}</h2>
                <FormTagInput size="sm" name="medical_information.allergies" control={control} />
              </div>

              <div className="space-y-2">
                <h2>{t("ravid.ravidSettings.emergency.emergencyMedications")}</h2>
                <FormTagInput size="sm" name="medical_information.emergency_medications" control={control} />
              </div>

              <div className="space-y-2">
                <h2>{t("ravid.ravidSettings.emergency.critialInformation")}</h2>
                <FormTextArea
                  name="medical_information.critical_information"
                  control={control}
                  placeholder={t("ravid.ravidSettings.emergency.critialPlaceholder")}
                />
              </div>

              <div className="space-y-2">
                <h2>{t("ravid.ravidSettings.emergency.pastAdmissionsSurgeries")}</h2>
                <FormTextArea
                  name="medical_information.past_admissions"
                  control={control}
                  placeholder={t("ravid.ravidSettings.emergency.enterPastMedicalSurgeries")}
                />
              </div>

              <div className="space-y-2">
                <h2>{t("ravid.ravidSettings.emergency.bloodType")}</h2>
                <FormSelect name="medical_information.blood_type" control={control} options={bloodTypes} />
              </div>
            </div>
          </CardContainer>
        </div>
        <div className="flex flex-col sm:flex-row col-span-12 justify-between ">
          <span className="muted max-w-4xl mb-2 sm:mb-0">{t("ravid.ravidSettings.emergency.disclaimer")}</span>
          <SubmitButton
            color="primary"
            isLoading={isPending}
            isLoadingValue={t("ravid.ravidSettings.emergency.saving")}
            type="submit"
            label={t("ravid.ravidSettings.emergency.save")}
          />
        </div>
      </form>
    </FormProvider>
  );
};
