import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { GoogleIcon, AppleIcon } from "@/components/icons";
import EmailChange from "@/components/Modals/EmailChange";
import PasswordChange from "@/components/Modals/PasswordChange";
import DeleteAccount from "@/components/Modals/DeleteAccount";
import Disclaimer from "@/components/Disclaimer";
import { useGetUser } from "@/hooks/useUser";

export const AccessSecurity = () => {
  const { data: user } = useGetUser();
  return (
    <>
      <Disclaimer />
      {/* Account Access Section */}
      <div className="flex flex-col gap-4 p-2">
        <Card className="w-full dark:bg-gray-900 shadow-lg">
          <CardHeader>
            <div className="flex flex-col">
              <h1>Account Access</h1>
              <p>Manage and review your security settings</p>
            </div>
          </CardHeader>
          <CardBody className="grid sm:grid-cols-3 gap-4">
            {/* Email Verification */}
            <Card className="dark:bg-slate-950">
              <CardBody className="flex justify-center items-between px-6">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col gap-1">
                    <h2>Email Verification</h2>
                    <span className="muted">{user?.email}</span>
                  </div>
                  <EmailChange />
                </div>
              </CardBody>
            </Card>

            {/* Change Password */}
            <Card className="dark:bg-slate-950">
              <CardBody className="flex justify-center items-between px-6">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col gap-1">
                    <h2>Change Password</h2>
                    <span className="muted">Last updated 30 days ago</span>
                  </div>
                  <PasswordChange />
                </div>
              </CardBody>
            </Card>

            {/* Two-Factor Authentication */}
            <Card className="dark:bg-slate-950">
              <CardBody className="flex justify-center items-between px-6">
                <div className="flex justify-between items-center ">
                  <div className="flex flex-col gap-1">
                    <h2>Two-Factor Authentication</h2>
                    <span className="muted">Currently inactive</span>
                  </div>
                  <Button color="primary" size="sm" variant="light">
                    Activate
                  </Button>
                </div>
              </CardBody>
            </Card>
          </CardBody>
        </Card>

        {/* Integration with External Services */}
        <Card className="w-full dark:bg-slate-950 border dark:border-slate-800 shadow-lg overflow-hidden">
          <CardHeader>
            <h1>Integration with External Services</h1>
          </CardHeader>
          <CardBody className="px-6 py-2 flex flex-col gap-4 overflow-visible">
            {/* Google */}
            <div className="flex justify-between items-center py-2 border-b dark:border-slate-800">
              <div className="flex items-center gap-2">
                <div className="w-7 h-7 bg-gray-800 rounded-full flex items-center justify-center">
                  <GoogleIcon size={10} className="dark:text-white" />
                </div>
                <div>
                  <h2>Integration with Google</h2>
                  <span className="muted">Linked on Sept 12, 2023</span>
                </div>
              </div>
              <Button color="danger" size="sm" variant="light">
                Unlink
              </Button>
            </div>

            {/* Apple */}
            <div className="flex justify-between items-center py-2 border-b dark:border-slate-800">
              <div className="flex items-center gap-2">
                <div className="w-7 h-7 bg-gray-800 rounded-full flex items-center justify-center">
                  <AppleIcon size={10} className="text-white" />
                </div>
                <div>
                  <h2>Integration with Apple</h2>
                </div>
              </div>
              <Button color="success" size="sm" variant="light">
                Link
              </Button>
            </div>

            <span className="muted text-xs">You can have only one active third party authentication login at a time</span>
          </CardBody>
        </Card>

        {/* Account Termination */}
        <Card className="w-full dark:bg-gray-900">
          <CardHeader className="flex flex-col sm:flex-row gap-2 justify-between">
            <div className="flex flex-col gap-2">
              <h1>Account Termination</h1>
              <span className="muted">Deleting your account is irreversible and will erase all data permanently.</span>
            </div>
            <DeleteAccount />
          </CardHeader>
        </Card>
      </div>
    </>
  );
};
