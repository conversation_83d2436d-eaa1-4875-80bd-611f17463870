import { Button } from "@heroui/react";
import ServiceCard from "../../Card/ServiceCard";
import { useState } from "react";

interface FileData {
  fileName: string;
  location: string;
  date: string;
  size: string;
}

export const Storage = () => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [showFilesTable, setShowFilesTable] = useState(false);

  const files: FileData[] = [
    { fileName: "file345434.pdf", location: "Prescription", date: "2025-04-12", size: "09.12 MB" },
    { fileName: "file346654.pdf", location: "Diagnosis", date: "2025-04-12", size: "09.12 MB" },
    { fileName: "file3453g67.pdf", location: "Prescription", date: "2025-04-12", size: "09.12 MB" },
    { fileName: "file3453g67.pdf", location: "Prescription", date: "2025-04-12", size: "09.12 MB" },
    { fileName: "file3453g67.pdf", location: "Diagnosis", date: "2025-04-12", size: "09.12 MB" },
    { fileName: "Kristen09_k.vcf", location: "My DNA", date: "2025-04-02", size: "5 GB" },
    { fileName: "Shalom888.vcf", location: "My DNA", date: "2025-04-02", size: "5 GB" },
    { fileName: "Sherry8003.vcf", location: "My DNA", date: "2025-04-02", size: "5 GB" },
    { fileName: "Kristen09_k.vcf", location: "My DNA", date: "2025-04-02", size: "5 GB" },
    { fileName: "Kristen09_k.vcf", location: "My DNA", date: "2025-04-02", size: "5 GB" },
    { fileName: "Kristen09_k.vcf", location: "My DNA", date: "2025-04-02", size: "5 GB" },
  ];

  const handleStorageChange = (value: string) => {
    console.log("Storage changed:", value);
  };

  const handleExtraStorageSelect = () => {
    // Handle Extra Storage selection
  };

  const handleCustomStorageSelect = () => {
    // Handle Custom Storage selection
  };

  const toggleFileSelection = (fileName: string) => {
    setSelectedFiles((prev) => (prev.includes(fileName) ? prev.filter((f) => f !== fileName) : [...prev, fileName]));
  };

  const StorageInfo = () => (
    <div className="dark:bg-slate-900 bg-gray-100 rounded-xl p-6 mb-6">
      <div className="flex flex-col items-center text-center mb-4">
        <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mb-3">
          <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
            />
          </svg>
        </div>
        <h3 className="text-sm font-medium mb-1">You've got 300 GB of storage</h3>
        <p className="text-xs muted">Used for: Diagnosis, Prescriptions & My DNA</p>
      </div>
      <div className="flex flex-col items-center gap-2 justify-center mb-4">
        <p>Want to free up some space?</p>
        <Button className="w-fit px-6" color="primary" size="sm" onClick={() => setShowFilesTable(true)}>
          Continue
        </Button>
      </div>
      <div className="w-full space-y-4">
        <div className="h-2 bg-gray-700 rounded-full overflow-hidden flex">
          <div className="h-full bg-purple-500" style={{ width: "18%" }} />
          <div className="h-full bg-blue-500" style={{ width: "20%" }} />
          <div className="h-full bg-amber-500" style={{ width: "62%" }} />
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-purple-500" />
            <span className="text-xs text-gray-300">18% Diagnosis</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-500" />
            <span className="text-xs text-gray-300">20% Prescriptions</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-amber-500" />
            <span className="text-xs text-gray-300">62% My DNA</span>
          </div>
        </div>

        <p className="text-xs">19.5 GB of 20 GB used</p>
      </div>
    </div>
  );

  const FilesTable = () => (
    <div className="flex-1 sm:p-6 p-2">
      <div className="flex flex-col h-full">
        <div className="flex items-center space-x-2 text-xs text-gray-400 mb-6">
          <button onClick={() => setShowFilesTable(false)} className="hover:text-white transition-colors">
            Storage
          </button>
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
          <span className="text-white">Files</span>
        </div>

        <div className="flex justify-end items-center mb-4">
          {selectedFiles.length === files.length && (
            <div className="flex justify-end gap-3">
              <span className="text-xs w-1/2  text-gray-400">Don't want to delete your important files? Buy more storage</span>
              <Button size="sm" color="primary" className="text-xs" onClick={handleExtraStorageSelect}>
                Continue
              </Button>
            </div>
          )}
        </div>

        {selectedFiles.length > 0 && (
          <div className="flex gap-2 mb-4 justify-between items-center">
            <span className="text-xs text-gray-400">
              {selectedFiles.length} file{selectedFiles.length > 1 ? "s" : ""} selected
            </span>
            <div className="flex gap-2">
              <Button size="sm" color="danger" className="px-4 py-1 text-xs bg-red-600 text-white rounded-lg">
                Delete
              </Button>
              <Button size="sm" color="primary" className="px-4 py-1 text-xs bg-blue-600 text-white rounded-lg">
                Download
              </Button>
            </div>
          </div>
        )}

        <div className="w-full overflow-x-auto">
          <div className="min-w-[800px]">
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-400">
                  <th className="p-2 text-left">
                    <input
                      type="checkbox"
                      checked={selectedFiles.length === files.length}
                      onChange={() => setSelectedFiles((prev) => (prev.length === files.length ? [] : files.map((f) => f.fileName)))}
                    />
                  </th>
                  <th className="p-2 text-left">File Name</th>
                  <th className="p-2 text-left">Location</th>
                  <th className="p-2 text-left">Date</th>
                  <th className="p-2 text-left">Size</th>
                  <th className="p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {files.map((file, index) => (
                  <tr key={index} className="text-[0.65rem] border-t border-gray-800">
                    <td className="p-2">
                      <input type="checkbox" checked={selectedFiles.includes(file.fileName)} onChange={() => toggleFileSelection(file.fileName)} />
                    </td>
                    <td className="p-3">{file.fileName}</td>
                    <td className="p-3">{file.location}</td>
                    <td className="p-3">{file.date}</td>
                    <td className="p-3">{file.size}</td>
                    <td className="p-3">
                      <div className="flex gap-2">
                        <button className="text-blue-500">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                            />
                          </svg>
                        </button>
                        <button className="text-red-500">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="flex justify-center gap-2 mt-4">
          <button className="px-2 py-1 text-xs bg-blue-600 rounded">1</button>
          <button className="px-2 py-1 text-xs">2</button>
          <button className="px-2 py-1 text-xs">3</button>
          <span className="px-2 py-1 text-xs">...</span>
          <button className="px-2 py-1 text-xs">10</button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-2">
      <div className="flex flex-col sm:flex-row gap-6">
        <div className="max-w-xl sm:m-auto justify-center flex flex-col flex-1 border border-gray-600 rounded-xl p-4">
          {showFilesTable ? <FilesTable /> : <StorageInfo />}

          {!showFilesTable && (
            <>
              <h2>Buy Additional Data Storage</h2>
              <div className="flex flex-col sm:flex-row gap-4 mt-2">
                <ServiceCard
                  serviceInfo={{
                    title: "Extra Data Storage",
                    benefits: ["100GB secure storage", "End-to-end encryption", "Regular backups and Data portability"],
                  }}
                  priceInfo={{
                    price: 5.0,
                    priceUnit: "Month",
                  }}
                 
                  onClick={handleExtraStorageSelect}
                  buttonText="Select"
                />
                <ServiceCard
                  serviceInfo={{
                    title: "Custom Data Storage",
                    benefits: ["Custom capacity secure storage", "End-to-end encryption", "Regular backups and Data portability"],
                  }}
                  priceInfo={{
                    price: "XYZ",
                    priceUnit: "TBD",
                  }}
                  customization={{
                    showStorageInput: true,
                    onStorageChange: handleStorageChange,
                  }}
                  onClick={handleCustomStorageSelect}
                  buttonText="Select"
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
