"use client";

import { useFetchIdVerifications } from "@/hooks/settings-dashboard/useVerification";
import { useCreatePaymentLink, useGetBillings, useGetSubscriptionPlans } from "@/hooks/useTransactions";
import { useStore } from "@/store/store";
import { Button } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { CheckCircle } from "lucide-react";
import { usePathname } from "next/navigation";
import React, { useState } from "react";
import toast from "react-hot-toast";
import VerificationPreview from "./Preview";
import VerifyIdentityModal from "./VerifyIdentityModal";
import ServiceCard from "@/components/Card/ServiceCard";
type SubscriptionPlan = {
  id: string;
  name: string;
  price: number;
};

const VerificationCard = ({
  title,
  description,
  children,
  buttonElement,
}: {
  title: string;
  description: string;
  children?: React.ReactNode;
  buttonElement: React.ReactNode;
}) => (
  <Card className="bg-gray-50 p-2 dark:bg-slate-900 hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors duration-200">
    <CardBody className="flex gap-4">
      <div className="w-full flex flex-col sm:flex-row sm:justify-between gap-4 sm:gap-0">
        <div>
          <h2>{title}</h2>
          <p>{description}</p>
        </div>
        <div className="flex justify-center sm:justify-end">{buttonElement}</div>
      </div>
      {children}
    </CardBody>
  </Card>
);

export const Verification = () => {
  const pathname = usePathname();
  // api hooks
  const { data: idVerifications, isLoading, isSuccess } = useFetchIdVerifications();
  const { data: subscriptionPlans } = useGetSubscriptionPlans();
  const { mutate: createPaymentLink, isPending: isCreatingPaymentLink } = useCreatePaymentLink();
  const { data: billingHistory, isSuccess: isSuccessBillingHistory } = useGetBillings();

  const phoneNumber = "";
  const [showPhoneInput, setShowPhoneInput] = useState(false);
  const { showVerifyModal, setShowVerifyModal } = useStore();

  const phoneVerification = async (phoneNumber: string) => {
    console.log("phoneNumber", phoneNumber);
  };
  // Check if user has paid for verification
  const hasVerificationPayment = () => {
    if (!billingHistory?.transactions) return false;
    const verificationPlan = subscriptionPlans?.prices?.find((plan: any) => plan.short_name === "profile-verification");
    if (!verificationPlan) return false;
    return billingHistory.transactions.some((transaction: any) => transaction.amount === verificationPlan.price && transaction.status === "success");
  };

  const isPaid = hasVerificationPayment();

  const handlePhoneVerification = async () => {
    try {
      await phoneVerification(phoneNumber);
      setShowPhoneInput(false); // Hide input after successful verification
      toast.success("Verification code sent to your phone");
    } catch (error) {
      console.error("Phone verification failed:", error);
      toast.error("Failed to send verification code");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "approved":
        return "text-green-500";
      case "rejected":
        return "text-red-500";
      default:
        return "text-yellow-500";
    }
  };

  const handleIdVerificationPayment = async () => {
    const planPrice = subscriptionPlans?.find((plan: SubscriptionPlan) => plan.name === "Verification Services");
    if (planPrice) {
      await createPaymentLink({ price_id: "01944659-7426-7f98-8232-10f213c9b97c", redirect_url: pathname });
    } else {
      toast.error("No plan found");
    }
  };

  const getStatusButton = () => {
    if (isPaid) {
      return (
        <div className="space-y-2">
          <div className="space-y-2">
            <div className="text-xs text-green-500 font-medium text-center">Verification in Process</div>
          </div>
          <Button size="sm" className="w-full bg-gray-600 text-white text-xs" disabled>
            Documents Submitted
          </Button>
        </div>
      );
    }

    // For unpaid users
    return (
      <div className="space-y-2">
        <div className="space-y-2">
          <div className="text-xs text-yellow-500 font-medium text-center">
            {/* Only display status if documents uploaded */}
            {idVerifications?.length > 0 && "Verification Pending"}
          </div>
        </div>
        <Button variant="bordered" size="sm" className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-xs" onPress={() => setShowVerifyModal(true)}>
          {idVerifications?.length > 0 ? "Manage Documents" : "Upload ID Card"}
        </Button>
      </div>
    );
  };

  // add skeleton later
  const isSuccessVerification = isSuccess && isSuccessBillingHistory;

  if (isSuccessVerification)
    return (
      <div className="p-2 sm:p-4">
        <h2>Verification</h2>
        <p>Here you can verify your Identity via various methods</p>
        <section className="flex flex-col lg:flex-row gap-4 mt-4">
          {/* Main content - 2/3 width */}
          <div className="w-full lg:w-2/3 space-y-4">
            {/* ID Verification */}
            <VerificationCard
              title="ID Verification"
              description="Upload a government-issued ID or passport to verify your identity"
              buttonElement={
                <>
                  {isPaid && <div className="flex justify-center sm:justify-start ml-0 sm:ml-4">{getStatusButton()}</div>}
                  {!isPaid && idVerifications?.length > 0 && (
                    <div className="flex justify-center sm:justify-start ml-0 sm:ml-4">
                      <Button size="sm" className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white text-xs" onPress={handleIdVerificationPayment}>
                        Proceed to Payment
                      </Button>
                    </div>
                  )}
                </>
              }
            >
              <div className="mt-3 w-full overflow-x-auto">
                {idVerifications?.length > 0 && <VerificationPreview idVerifications={idVerifications} showPreview={false} />}
              </div>
              {!isPaid && (
                <div className="flex justify-center sm:justify-end">
                  <Button
                    variant="flat"
                    size="sm"
                    className="w-full sm:w-auto bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-xs flex items-center gap-2"
                    onPress={() => setShowVerifyModal(true)}
                  >
                    {idVerifications?.length > 0 ? " + Upload Another Document" : "Upload ID Card"}
                  </Button>
                </div>
              )}
            </VerificationCard>
            {/* Phone Verification */}
            <VerificationCard
              title="Phone Verification"
              description="Verify your phone number for added security"
              buttonElement={
                <>
                  <Button
                    size="sm"
                    className="mt-2 w-full sm:w-auto bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-xs flex items-center gap-2"
                  >
                    Add Phone Number
                  </Button>
                </>
              }
            />
            {/* Email Verification */}
            <VerificationCard
              title="Email Verification"
              description="Verify your email address"
              buttonElement={
                <Button
                  size="sm"
                  className="mt-2 w-full sm:w-auto bg-green-600 text-white hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-xs flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  Verified
                </Button>
              }
            />
          </div>

          {/* Service Card - 1/3 width */}
          <div className="w-full lg:w-1/3">
            <ServiceCard
              serviceInfo={{
                title: "Verification Services",
                benefits: ["Verified badge on profile", "Priority customer support", "Enhanced credibility", "Identity verification"],
              }}
              priceInfo={{
                price: 1.0,
                priceUnit: "Month",
              }}
              onClick={() => {}}
              buttonText="Select"
            />
          </div>
        </section>

        <VerifyIdentityModal
          idVerifications={idVerifications}
          isOpen={showVerifyModal}
          isLoading={isLoading}
          onClose={() => {
            setShowVerifyModal(false);
          }}
        />
      </div>
    );
};