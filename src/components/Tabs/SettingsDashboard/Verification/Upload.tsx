import { useUploadIdVerification } from "@/hooks/settings-dashboard/useVerification";
import { useCreatePaymentLink, useGetBillings, useGetSubscriptionPlans } from "@/hooks/useTransactions";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@heroui/button";
import { CheckCircle } from "lucide-react";
import { usePathname } from "next/navigation";
import { ChangeEvent } from "react";
import toast from "react-hot-toast";
import VerificationPreview from "./Preview";
import { DOCUMENT_TYPES } from "./VerifyIdentityModal";
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

interface IdVerification {
  id: string;
  name: string;
  file_url: string;
}

type VerificationUploadProps = {
  idVerifications: IdVerification[];
  type: DOCUMENT_TYPES;
  onClose?: () => void;
  isLoading?: boolean;
  showPreview?: boolean;
};

const VerificationUpload = ({ idVerifications, type, onClose, isLoading, showPreview = false }: VerificationUploadProps) => {
  const pathname = usePathname();
  // api hooks
  const { mutate: uploadIdVerification, isPending: isUploading } = useUploadIdVerification();
  const { mutate: createPaymentLink, isPending: isCreatingPaymentLink } = useCreatePaymentLink();
  const { data: subscriptionPlans } = useGetSubscriptionPlans();
  const { data: billingHistory } = useGetBillings();

  const buttonText =
    idVerifications?.length === 0 ? "Upload First Document" : idVerifications?.length === 1 ? "Upload Second Document" : "Add Another Document";

  // Check if user has paid for verification
  const hasVerificationPayment = () => {
    if (!billingHistory?.transactions) return false;
    const verificationPlan = subscriptionPlans?.prices?.find((plan: any) => plan.short_name === "profile-verification");
    if (!verificationPlan) return false;
    return billingHistory.transactions.some((transaction: any) => transaction.amount === verificationPlan.price && transaction.status === "success");
  };

  const isPaid = hasVerificationPayment();

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (isPaid) {
      toast.error("Your documents have already been submitted for verification");
      return;
    }

    const files = Array.from(event.target.files || []);
    const validFiles: File[] = [];
    for (const file of files) {
      // Check file size (5MB limit)
      if (file.size > MAX_FILE_SIZE) {
        toast.error(`File ${file.name} exceeds 5MB limit`);
        continue;
      }
      // Check file type
      const allowedTypes = ["application/pdf", "image/jpeg", "image/png"];
      if (!allowedTypes.includes(file.type)) {
        toast.error(`File ${file.name} is not a PDF, JPEG, or PNG`);
        continue;
      }
      validFiles.push(file);
    }
    handleUpload(validFiles);
  };

  const handleUpload = async (files: File[]) => {
    if (isPaid) return;

    try {
      //   setIsUploading(true);
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
        formData.append("type", type);
      });
      await uploadIdVerification(formData);
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Failed to upload ID verification");
    } finally {
      //   setIsUploading(false);
    }
  };

  const handleIdVerificationPayment = async () => {
    // Set activeTab in localStorage
    localStorage.setItem("activeTab", "verification");
    setTimeout(() => {
      localStorage.removeItem("activeTab");
    }, 5000);
    const idVerificationPrice = subscriptionPlans?.prices?.find((plan: any) => plan.short_name === "profile-verification");
    if (idVerificationPrice) {
      await createPaymentLink({ price_id: idVerificationPrice?.id, redirect_url: pathname });
    }
  };

  // Check if we should show the verification success message
  //   useEffect(() => {
  //     // Check if user has returned from payment
  //     // const activeTab = localStorage.getItem("activeTab");
  //     // if (activeTab === "verification" && isPaid) {
  //     //   // If user paid and has returned, hide the modal
  //     //   const { setShowVerifyModal } = useAuthStore.getState();
  //     //   setShowVerifyModal(false);
  //     //   // Remove activeTab from localStorage
  //     //   localStorage.removeItem("activeTab");
  //     // }
  //   }, [isPaid]);

  return (
    <div className="flex flex-col items-center gap-4 w-full">
      {!isLoading && idVerifications?.length > 0 && <VerificationPreview idVerifications={idVerifications} showPreview={showPreview} />}
      {(isLoading || isUploading) && (
        <div className="flex items-center justify-center w-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      )}

      {isPaid ? (
        <div className="flex flex-col items-center gap-2 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg w-full">
          <CheckCircle className="h-8 w-8 text-green-500" />
          <p className="text-center text-sm font-medium text-green-700 dark:text-green-300">We have successfully received your verification documents.</p>
          <p className="text-center text-xs text-green-600 dark:text-green-400">Our team will review your submission and get back to you soon.</p>
        </div>
      ) : (
        <>
          <label
            htmlFor="id-verification-upload"
            className={cn(
              "cursor-pointer text-xs px-4 py-3 rounded-md transition-colors w-full text-center text-white",
              idVerifications?.length >= 2 ? "border border-spacing-1 border-gray-300 hover:border-gray-400" : "bg-blue-600 hover:bg-blue-700"
            )}
          >
            {isUploading ? "Uploading..." : buttonText}
          </label>
          {idVerifications?.length >= 2 && (
            <Button
              onPress={handleIdVerificationPayment}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs px-4 py-3 rounded-md transition-colors"
            >
              Confirm and Proceed to Payment
            </Button>
          )}
          <input
            id="id-verification-upload"
            type="file"
            multiple
            className="hidden"
            accept=".pdf,.jpg,.jpeg,.png"
            onChange={handleFileChange}
            disabled={isLoading || isUploading || isPaid}
          />
        </>
      )}
    </div>
  );
};

export default VerificationUpload;
