import { useGetBillings, useGetSubscriptionPlans } from "@/hooks/useTransactions";
import { Select, SelectItem } from "@heroui/react";
import Image from "next/image";
import { useState } from "react";
import VerificationUpload from "./Upload";
import CustomModal from "@/components/Modals/CustomModal";

interface IdVerification {
  id: string;
  name: string;
  file_url: string;
}

interface VerifyIdentityModalProps {
  idVerifications: IdVerification[];
  isOpen: boolean;
  onClose: () => void;
  isLoading: boolean;
}

export type DOCUMENT_TYPES = "driving_license" | "national_id" | "passport" | "voter_id" | "other";

const VerifyIdentityModal = ({ idVerifications, isOpen, onClose, isLoading }: VerifyIdentityModalProps) => {
  // api hooks
  const { data: subscriptionPlans } = useGetSubscriptionPlans();
  const { data: billingHistory } = useGetBillings();

  const [documentType, setDocumentType] = useState<DOCUMENT_TYPES | "">("");
  // Check if user has paid for verification
  const hasVerificationPayment = () => {
    if (!billingHistory?.transactions) return false;
    const verificationPlan = subscriptionPlans?.prices?.find((plan: any) => plan.short_name === "profile-verification");
    if (!verificationPlan) return false;
    return billingHistory.transactions.some((transaction: any) => transaction.amount === verificationPlan.price && transaction.status === "success");
  };

  const isPaid = hasVerificationPayment();

  const renderBody = () => {
    return (
      <div className="space-y-4 mt-4 max-h-[70vh] overflow-y-auto px-2">
        {/* Instructions */}
        <div className="space-y-1">
          <p className="text-xs font-medium dark:text-white">Instructions for Clear Image:</p>
          <ul className="text-xs text-gray-600 dark:text-gray-300 list-disc pl-5 space-y-1">
            <li>Use good lighting to avoid shadows or glare.</li>
            <li>Ensure the entire ID card is visible within the frame.</li>
            <li>Both text and photo must be legible.</li>
          </ul>
        </div>
        {idVerifications?.length < 1 && (
          <>
            <div className="space-y-2">
              <Select
                classNames={{
                  value: "text-xs",
                  listbox: "text-xs",
                  trigger: "h-9",
                }}
                aria-label="Select Model"
                selectedKeys={[documentType]}
                onSelectionChange={(keys) => {
                  const selectedValue = Array.from(keys)[0];
                  setDocumentType(selectedValue as DOCUMENT_TYPES);
                }}
                placeholder="Choose a Government Issued ID"
                size="sm"
              >
                <SelectItem key="driving_license" className="text-xs text-gray-300">
                  Driver's License
                </SelectItem>
                <SelectItem key="national_id" className="text-xs text-gray-300">
                  National ID
                </SelectItem>
                <SelectItem key="passport" className="text-xs text-gray-300">
                  Passport
                </SelectItem>
                <SelectItem key="voter_id" className="text-xs text-gray-300">
                  Voter's ID
                </SelectItem>
                <SelectItem key="other" className="text-xs text-gray-300">
                  Other
                </SelectItem>
              </Select>
            </div>
            <p className="text-xs font-medium text-gray-900 dark:text-white">Sample Images</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full">
              <div className="relative aspect-[4/3] w-full max-h-[150px] sm:max-h-none">
                <Image src="/images/front-sample.png" alt="Sample ID Front" fill className="rounded-lg object-cover" />
              </div>
              <div className="relative aspect-[4/3] w-full max-h-[150px] sm:max-h-none">
                <Image src="/images/back-sample.png" alt="Sample ID Back" fill className="rounded-lg object-cover" />
              </div>
            </div>
          </>
        )}
        <div className="space-y-3">
          <VerificationUpload
            idVerifications={idVerifications}
            type={documentType as DOCUMENT_TYPES}
            onClose={onClose}
            isLoading={isLoading}
            showPreview={true}
          />
          {idVerifications?.length >= 2 && !isPaid && (
            <div className="text-center">
              <div className="text-yellow-500 text-xs font-medium mb-2">Verification Status: Pending</div>
              <p className="text-xs text-gray-300">Your verification files have been uploaded successfully. Please proceed to payment.</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <CustomModal
      title="Verify your Identity"
      body={
        <>
          <div className="flex flex-col gap-2 mb-3">
            <p className="text-xs dark:text-gray-300 leading-relaxed">
              Ensure your ID card is valid, government-issued, and clearly visible without any damage or obstruction. Examples include a passport, driver's
              license, or national ID card.
            </p>
          </div>
          {renderBody()}
        </>
      }
      isOpen={isOpen}
      onOpenChange={onClose}
      className="max-w-[95vw] sm:max-w-[600px] dark:bg-[#030712] border-gray-800"
    />
  );
};

export default VerifyIdentityModal;
