import CustomModal from "@/components/Modals/CustomModal";
import { useDeleteIdVerification } from "@/hooks/settings-dashboard/useVerification";
import { useGetBillings, useGetSubscriptionPlans } from "@/hooks/useTransactions";
import Image from "next/image";
import { useState } from "react";

type IdVerification = {
  id: string;
  name: string;
  file_url: string;
};

type VerificationPreviewProps = {
  idVerifications: IdVerification[];
  showPreview?: boolean;
};

const VerificationPreview = ({ idVerifications, showPreview = false }: VerificationPreviewProps) => {
  const { mutate: deleteIdVerification, isPending: isDeleting } = useDeleteIdVerification();
  // api hooks
  const { data: subscriptionPlans } = useGetSubscriptionPlans();
  const { data: billingHistory } = useGetBillings();

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);

  // Check if user has paid for verification
  const hasVerificationPayment = () => {
    if (!billingHistory?.transactions) return false;
    const verificationPlan = subscriptionPlans?.prices?.find((plan: any) => plan.short_name === "profile-verification");
    if (!verificationPlan) return false;
    return billingHistory.transactions.some((transaction: any) => transaction.amount === verificationPlan.price && transaction.status === "success");
  };

  const isPaid = hasVerificationPayment();

  const handleDelete = async (id: string) => {
    if (isPaid) {
      return; // Prevent deletion if payment has been made
    }
    await deleteIdVerification(id);
  };

  // Handle opening the preview dialog
  const handlePreview = (fileUrl: string) => {
    setPreviewUrl(fileUrl);
    setIsPreviewOpen(true);
  };

  // Document preview component
  const DocumentPreview = () => (
    <div className="w-full h-full">
      <iframe src={previewUrl || ""} className="w-full h-full" title="Document Preview" />
    </div>
  );

  return (
    <div className="w-full">
      <h1 className="font-semibold mb-3 text-xs">Uploaded Documents</h1>
      <div className="flex flex-col sm:flex-row gap-4 shadow-lg shadow-red-500/20 rounded-lg p-2">
        {idVerifications?.map((idVerification: IdVerification, index: number) => (
          <div key={index} className="flex-1 sm:w-[250px]">
            <div className="w-full space-y-2 relative">
              {showPreview ? (
                <div className="h-48 relative border border-gray-800 rounded-lg overflow-hidden">
                  {idVerification.file_url && (
                    <Image
                      src={idVerification.file_url}
                      alt={`Document preview ${index + 1}`}
                      className="w-full h-full object-contain cursor-pointer"
                      onClick={() => handlePreview(idVerification.file_url)}
                      width={1000}
                      height={1000}
                    />
                  )}
                  {!isPaid && (
                    <button
                      className="absolute top-2 right-2 p-2 bg-red-500/10 rounded-full hover:bg-red-500/20"
                      onClick={() => handleDelete(idVerification.id)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-4 h-4 text-red-500"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                        />
                      </svg>
                    </button>
                  )}
                </div>
              ) : (
                <div className="flex justify-between items-center p-3 border border-gray-800 rounded-lg">
                  <div className="flex items-center gap-2 cursor-pointer flex-grow overflow-hidden" onClick={() => handlePreview(idVerification.file_url)}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-5 h-5 text-gray-400 flex-shrink-0"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                      />
                    </svg>
                    <span className="text-sm truncate">{idVerification?.name?.substring(37) || `Document ${index + 1}`}</span>
                  </div>
                  {!isPaid && (
                    <button
                      className="p-2 bg-red-500/10 rounded-full hover:bg-red-500/20 flex-shrink-0 ml-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(idVerification.id);
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-4 h-4 text-red-500"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                        />
                      </svg>
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Document Preview Dialog using CustomModal */}
      {previewUrl && (
        <CustomModal
          title=""
          body={<DocumentPreview />}
          isOpen={isPreviewOpen}
          onOpenChange={setIsPreviewOpen}
          buttonText=""
          onPress={() => {}}
          showCancelButton={false}
          className="max-w-[95vw] sm:max-w-4xl h-[80vh] p-0"
          // size="full" // Use full size to respect the custom max-width
          // hideCloseButton={false}
        />
      )}
    </div>
  );
};

export default VerificationPreview;
