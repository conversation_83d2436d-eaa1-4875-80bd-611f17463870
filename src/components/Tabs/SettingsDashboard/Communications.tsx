"use client";

import React from "react";
import { Switch } from "@heroui/switch";
import { <PERSON>, CardHeader, CardBody } from "@heroui/card";
import { communicationTypes } from "@/config/constants";
import Disclaimer from "@/components/Disclaimer";

export const Communications = () => {
  return (
    <>
      <Disclaimer />

      <Card className="dark:bg-slate-950 border-none ">
        <CardHeader>
          <h1>Communication Preferences</h1>
        </CardHeader>
        <CardBody className="space-y-8">
          {communicationTypes.map((type) => (
            <Card key={type.key} className="bg-gray-50 dark:bg-slate-900">
              <CardBody className="flex items-start justify-between">
                <div className="gap-2 w-full flex justify-between">
                  <div className="flex flex-col gap-2">
                    <h1>{type.label}</h1>
                    <p className="text-gray-500 dark:text-gray-400">
                      {type.description}
                    </p>
                  </div>
                  <Switch size="sm" disabled />
                </div>
              </CardBody>
            </Card>
          ))}
        </CardBody>
      </Card>
    </>
  );
};
