"use client";

import CardContainer from "@/components/Card";
import { useDeletePractitioner, useGetMedicalTeam, useUpdateMedicalTeam } from "@/hooks/settings-dashboard/useMedicalTeam";
import { RoleMedicalTeamFormValues, roleMedicalTeamSchema } from "@/lib/utils/validations";
import { Button } from "@heroui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { PractitionerForm } from "../../Forms/PractitionerForm";

export const MedicalTeam = () => {
  const { t } = useTranslation();
  const { data: medicalTeam } = useGetMedicalTeam();
  const { mutate: updateMedicalTeam } = useUpdateMedicalTeam();
  const { mutate: deletePractitioner } = useDeletePractitioner();

  const CORE_ROLES = {
    PrimaryPhysician: t("ravid.ravidSettings.medical.primaryPhysician"),
    Geneticist: t("ravid.ravidSettings.medical.geneticist"),
    Dentist: t("ravid.ravidSettings.medical.dentist"),
    Ophthalmologist: t("ravid.ravidSettings.medical.ophthalmologist"),
  };

  const form = useForm<RoleMedicalTeamFormValues>({
    resolver: zodResolver(roleMedicalTeamSchema),
    mode: "onSubmit",
    defaultValues: {},
  });

  const { control, handleSubmit, reset, setValue } = form;

  // Add state to track additional practitioners
  const [additionalPractitioners, setAdditionalPractitioners] = useState<string[]>([]);

  // At the top of component, add a constant for max additional practitioners
  const MAX_ADDITIONAL_PRACTITIONERS = 2;

  useEffect(() => {
    if (medicalTeam) {
      // Separate core roles and additional practitioners
      const coreRoles = Object.keys(CORE_ROLES);
      const additionalPractitionerEntries = Object.entries(medicalTeam).filter(([role]) => !coreRoles.includes(role));

      // Set additional practitioners state
      if (additionalPractitionerEntries.length > 0) {
        setAdditionalPractitioners(additionalPractitionerEntries.map(([role]) => role));
      }

      // Reset form with all data
      reset(medicalTeam);
    } else {
      // Initialize with empty practitioners for each core role
      const initialValues = Object.keys(CORE_ROLES).reduce(
        (acc, role) => ({
          ...acc,
          [role]: {
            name: "",
            email: "",
            contact_number: "",
            affiliation: "",
            role: role,
          },
        }),
        {}
      );
      reset(initialValues);
      setAdditionalPractitioners([]); // Reset additional practitioners
    }
  }, [medicalTeam, reset]);

  const onSubmit = async (data: RoleMedicalTeamFormValues) => {
    // Get all practitioners that have any data filled in
    const changedPractitioners = Object.entries(data)
      .map(([role, practitioner]) => ({
        ...practitioner,
        role, // Ensure role is set
      }))
      .filter((practitioner) => {
        // Filter out completely empty practitioners
        const { id, role, ...fields } = practitioner;
        return Object.values(fields).some((value) => value !== "" && value !== null && value !== undefined);
      });

    if (changedPractitioners.length > 0) {
      updateMedicalTeam(changedPractitioners);
    }
  };

  const handleAddPractitioner = () => {
    setAdditionalPractitioners((prev) => {
      // First check if we've reached the limit
      if (prev.length >= MAX_ADDITIONAL_PRACTITIONERS) {
        toast.error("You have reached the maximum number of additional practitioners");
        return prev; // Don't add more if we've reached the limit
      }

      const newKey = `AdditionalPractitioner${prev.length + 1}`;
      // Initialize the new practitioner in the form
      setValue(newKey, {
        name: "",
        email: "",
        contact_number: "",
        affiliation: "",
        role: newKey,
      });
      return [...prev, newKey];
    });
  };

  const handleRemovePractitioner = (index: number) => {
    setAdditionalPractitioners((prev) => {
      const keyToRemove = prev[index];
      const practitionerToRemove = form.getValues()[keyToRemove];

      // If practitioner has an ID, delete from backend
      if (practitionerToRemove?.id) {
        deletePractitioner(practitionerToRemove.id);
      }

      // Remove from form state
      const formValues = { ...form.getValues() };
      delete formValues[keyToRemove];
      reset(formValues);

      return prev.filter((_, i) => i !== index);
    });
  };

  const renderPractitionerForm = (role: string, canDelete: boolean = false, index?: number) => (
    <div key={role} className="border-b dark:border-slate-800 pb-4">
      <PractitionerForm
        control={control}
        namePrefix={role}
        role={CORE_ROLES[role as keyof typeof CORE_ROLES] || "Additional Practitioner"}
        onDelete={canDelete ? () => handleRemovePractitioner(index!) : undefined}
        showDelete={canDelete}
      />
    </div>
  );

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="grid grid-cols-12 md:grid-cols-6 p-2 rounded-2xl gap-4 w-full">
      <CardContainer title={t("ravid.ravidSettings.medical.title")} classNames={{ body: "w-full h-full gap-1" }}>
        {Object.keys(CORE_ROLES)
          .slice(0, 3)
          .map((role) => renderPractitionerForm(role))}
      </CardContainer>
      {/* Ophthalmologist and Additional Practitioners Card */}
      <CardContainer title="Ophthalmologist and Additional Practitioners" classNames={{ body: "w-full h-full gap-1" }}>
        {/* Render Ophthalmologist */}
        {renderPractitionerForm("Ophthalmologist")}

        {/* Render Additional Practitioners */}
        {additionalPractitioners.map((key, index) => renderPractitionerForm(key, true, index))}

        {/* Add Practitioner Button */}
        <Button
          size="sm"
          variant="bordered"
          className="ml-auto w-fit relative text-white flex items-center gap-2"
          onPress={handleAddPractitioner}
          disabled={additionalPractitioners.length >= MAX_ADDITIONAL_PRACTITIONERS}
        >
          <Plus size={16} />
          Add Practitioner
        </Button>
      </CardContainer>

      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row justify-between col-span-12 sm:col-span-6 gap-4">
        {/* Disclaimer */}
        <span className="muted max-w-4xl mb-2 sm:mb-0">{t("ravid.ravidSettings.medical.disclaimer")}</span>
        <Button type="submit" size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
          {t("ravid.ravidSettings.medical.save")}
        </Button>
      </div>
    </form>
  );
};
