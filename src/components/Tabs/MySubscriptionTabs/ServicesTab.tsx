import ServiceCard from "@/components/Card/ServiceCard";
import useServices from "@/hooks/useServices";

export const ServicesTab = () => {
  const { data: services } = useServices();

  // Sort services by order property
  const sortedServices = [...(services || [])].sort((a, b) => (a.order || 0) - (b.order || 0));

  return (
    <div className="grid md:grid-cols-3 gap-4">
      {sortedServices.map((service) => (
        <ServiceCard
          key={service.id}
          serviceInfo={{
            title: service.name,
            benefits: service.features,
          }}
          priceInfo={{
            price: Number(service.price),
            priceUnit: "Analysis",
          }}
          onClick={() => {}}
          buttonText="Select"
        />
      ))}
    </div>
  );
};
