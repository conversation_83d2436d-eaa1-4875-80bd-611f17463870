import SubscriptionCards from "@/components/Tabs/MySubscriptionTabs/subComponents/SubscriptionCards";
import Tabs from "@/components/Tabs/TabComponent";
import { useGetBillings, useGetSubscriptionPlans } from "@/hooks/useTransactions";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { TabItem } from "../TabComponent";
import { BillingsTable } from "./subComponents/BillingsTable";

const tabs: TabItem[] = [
  {
    key: "monthly",
    title: "Monthly",
    content: null,
  },
  {
    key: "yearly",
    title: "Yearly",
    content: null,
    endIcon: <span className="bg-green-500 text-white text-xs px-2 py-0.5 rounded ml-2">Save 20%</span>,
  },
];

export const SubscriptionTab = () => {
  const [isYearly, setIsYearly] = useState(false);

  const { data: billingHistory, isLoading: isBillingLoading } = useGetBillings();
  const { data: subscriptionPlansData, isLoading: isPlansLoading } = useGetSubscriptionPlans();

  const isSubscribed = (amount: number) => {
    if (!billingHistory?.transactions) return false;
    return billingHistory.transactions.some((transaction: any) => transaction.amount === amount);
  };

  return (
    <div className="space-y-6 p-2 sm:p-4 mx-auto dark:bg-slate-950 rounded-xl">
      <BillingsTable />
      <div className="flex justify-center items-center mt-8">
        <Tabs
          tabs={tabs}
          selectedKey={isYearly ? "yearly" : "monthly"}
          onSelectionChange={(key) => setIsYearly(key === "yearly")}
          classNames={{
            tabList: "flex flex-wrap md:flex-nowrap border rounded-full dark:border-gray-800 border-gray-300",
            tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
            tabContent: "text-gray-700 dark:text-white wrap",
            cursor: "bg-blue-500",
          }}
        />
      </div>
      {isPlansLoading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-900 dark:text-white" />
        </div>
      ) : (
        <SubscriptionCards plans={subscriptionPlansData} isYearly={isYearly} isSubscribed={isSubscribed} />
      )}
      <div className="text-sm text-gray-400 mt-8 space-y-2">
        <p>
          * All subscriptions are billed monthly and can be canceled at any time. Your subscription will help support the platform and unlock premium features.
        </p>
        <p>
          ** DNA Analysis is based on a Pay for Service model, i.e. you pay for every time you request an analysis. As computational biology evolves so will be
          the understanding and the results of the analytical services.
        </p>
      </div>
    </div>
  );
};
