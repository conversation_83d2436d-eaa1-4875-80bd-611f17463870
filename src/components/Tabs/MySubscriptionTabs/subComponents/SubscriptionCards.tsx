"use client";
import type { SubscriptionPlan } from "@/hooks/useTransactions";
import { useGetUser } from "@/hooks/useUser";
import { useStore } from "@/store/store";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import ServiceCard from "../../../Card/ServiceCard";

interface SubscriptionCardsProps {
  plans: SubscriptionPlan[];
  isYearly: boolean;
  isSubscribed: (amount: number) => boolean;
}

const SubscriptionCards = ({ plans, isYearly, isSubscribed }: SubscriptionCardsProps) => {
  const { data: user } = useGetUser();
  const { setShowVerifyModal } = useStore();

  const router = useRouter();

  // Keeping handlePaymentLink for future Stripe integration
  const handlePaymentLink = async (price_id: string, short_name: string) => {
    try {
      if (short_name === "profile-verification") {
        router.push(`/my/${user?.user_id}/edit?tab=verification`);
        setShowVerifyModal(true);
      } else {
        // Temporarily disabled until Stripe integration
        toast.success("Payment functionality coming soon!");
        // const response = await paymentLink({ price_id, redirect_url: pathname });
        // const url = (response as any).url;
        // if (typeof url === "string") {
        //   window.location.href = url;
        // }
      }
    } catch (error) {
      console.error("Error handling payment link:", error);
      toast.error("Failed to create payment link");
    }
  };

  const currentPlans = plans
    ?.filter((plan) => plan.billing_cycle === "MONTHLY")
    .map((plan) => ({
      ...plan,
      price: isYearly ? (plan.price * 12 - (plan.price * 12 * 20) / 100).toFixed(2) : plan.price,
    }));

  // Sort plans by order property
  const sortedPlans = [...(currentPlans || [])].sort((a, b) => (a.order || 0) - (b.order || 0));

  return (
    <div>
      <h2 className="text-sm mb-4">Premium Features:</h2>
      <div className="grid md:grid-cols-4 gap-4 px-4 md:px-0">
        {sortedPlans.map((plan) => (
          <ServiceCard
            key={plan.id}
            serviceInfo={{
              title: plan.name,
              benefits: plan.features,
            }}
            priceInfo={{
              price: Number(plan.price),
              priceUnit: isYearly ? "year" : "month",
            }}
            onClick={() => handlePaymentLink(plan.id, plan.short_name || "")}
            status={{
              isActive: isSubscribed(Number(plan.price)),
              isDisabled: isSubscribed(Number(plan.price)),
            }}
            buttonText="Select"
          />
        ))}
      </div>
    </div>
  );
};

export default SubscriptionCards;
