import CustomTable from "@/components/Table";
import { useGetBillings } from "@/hooks/useTransactions";
import { cn } from "@/lib/utils";

const columns = [
  { key: "date", label: "Date", render: (item: any) => <span>{new Date(item.payload.status_transitions.paid_at * 1000).toLocaleDateString()}</span> },
  { key: "name", label: "Name", render: (item: any) => <span>{item.payload.customer_name}</span> },
  { key: "amount", label: "Amount", render: (item: any) => <span>${item.amount.toFixed(2)}</span> },
  {
    key: "status",
    label: "Status",
    render: (item: any) => (
      <span
        className={cn(
          "inline-flex rounded-full px-2 text-xs font-semibold leading-5",
          item.status === "success"
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
        )}
      >
        {item.status}
      </span>
    ),
  },
  {
    key: "action",
    label: "Action",
    render: (item: any) => (
      <button onClick={() => {}} className="text-red-500 hover:text-red-700 dark:text-red-500 dark:hover:text-red-300">
        Unsubscribe
      </button>
    ),
  },
];

export const BillingsTable = () => {
  const { data: billingHistory } = useGetBillings();
  return (
    <CustomTable
      data={billingHistory?.transactions}
      columns={columns}
      classNames={{
        td: "px-3 py-2 text-xs text-gray-900 dark:text-gray-300",
        th: "bg-gray-100 dark:bg-slate-800",
        tbody: "divide-y divide-gray-300 dark:divide-gray-700 bg-white dark:bg-slate-900",
      }}
      pagination={{
        totalItems: billingHistory?.transactions.length,
        rowsPerPage: 2,
        showControls: true,
      }}
    />
  );
};
