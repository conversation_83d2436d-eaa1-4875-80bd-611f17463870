// export a simple component that displays the License Manager Tab

"use client";

import Image from "next/image";
import { <PERSON><PERSON> } from "@heroui/react";
import { Input } from "@heroui/react";
import { AddUserDialog } from "./subComponents/AddUserDialog";
import { DeleteUserDialog } from "./subComponents/DeleteUserDialog";
import { Pencil, CheckCircle2, XCircle } from "lucide-react";
import { UserAvatar } from "./subComponents/UserAvatar";
import { UserBadges } from "./subComponents/UserBadges";
import { useState } from "react";

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  status: "Verified" | "Pending";
  aiAnalysis: boolean;
  dataStorage: string;
  activationPending: boolean;
}

// Temporary dummy data
const initialUsers: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/images/manage_user-1.png",
    status: "Verified",
    activationPending: false,
    aiAnalysis: true,
    dataStorage: "100 GB Data Storage",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/images/manage_user-2.png",
    status: "Verified",
    activationPending: false,
    aiAnalysis: true,
    dataStorage: "100 GB Data Storage",
  },
  {
    id: "3",
    name: "Jenny Johns",
    email: "<EMAIL>",
    avatar: "/images/manage_user-3.png",
    status: "Verified",
    aiAnalysis: true,
    dataStorage: "100 GB Data Storage",
    activationPending: false,
  },
  {
    id: "4",
    name: "John Stone",
    email: "<EMAIL>",
    avatar: "/images/manage_user-3.png",
    status: "Verified",
    activationPending: true,
    aiAnalysis: false,
    dataStorage: "100 GB Data Storage",
  },
];

interface UserListItemProps {
  user: User;
  onDelete: (id: string) => void;
  onEdit: (id: string) => void;
}

const UserListItem = ({ user, onDelete, onEdit }: UserListItemProps) => {
  const isVerified = !user.activationPending;

  return (
    <div className={`p-4 flex flex-col w-full border dark:border-slate-800 rounded-lg`}>
      <div className="flex justify-between md:items-start items-center w-full">
        {/* Left Section: Avatar and User Info */}
        <div className="flex items-center gap-3">
          <UserAvatar user={user} />
          <div className="flex flex-col gap-2">
            <span className={`text-sm ${!isVerified && "dark:text-gray-300"}`}>{isVerified ? user.name : user.email}</span>
            {!isVerified && (
              <div className="hidden md:flex">
                <span className="whitespace-nowrap px-3 py-1 text-white text-xs rounded-full bg-gradient-to-b from-gray-500 to-slate-700">
                  Activation Pending
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Actions */}
        <div className="flex items-center gap-3 lg:hidden">
          <Button isIconOnly variant="light" size="sm" className="text-gray-400" onPress={() => onEdit(user.id)}>
            <Pencil className="w-4 h-4" />
          </Button>
          <DeleteUserDialog userName={user.name} userId={user.id} onDelete={onDelete} variant="icon" />
        </div>

        {/* Desktop Section */}
        <div className="flex-1 hidden lg:flex items-center gap-4">
          <UserBadges user={user} />
          <div className="flex items-center gap-3">
            <Button size="sm" className="text-xs bg-[#0C1B3B] border border-gray-700 text-white hover:bg-blue-950" onPress={() => onEdit(user.id)}>
              Edit Services
            </Button>
            <DeleteUserDialog userName={user.name} userId={user.id} onDelete={onDelete} />
          </div>
        </div>
      </div>
    </div>
  );
};

const SearchAndAddSection = ({ onAddUser, onUploadCSV }: { onAddUser: (email: string) => void; onUploadCSV: () => void }) => {
  const [email, setEmail] = useState("");

  const handleAddUser = () => {
    if (email) {
      onAddUser(email);
      setEmail("");
    }
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
      <div className="flex w-[70%]">
        <Input
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          classNames={{
            input: "text-xs",
            inputWrapper: "bg-slate-100 dark:bg-slate-900 rounded-r-none",
          }}
          placeholder="Enter Email of New User"
          type="email"
        />
        <Button className="text-xs sm:px-20 bg-blue-600 text-white hover:bg-blue-700 flex-1 rounded-l-none" onPress={handleAddUser}>
          Add New User
        </Button>
      </div>
      <span className="text-xs flex items-center">OR</span>
      <Button className="bg-blue-600 text-white hover:bg-blue-700 px-4 text-xs" onPress={onUploadCSV}>
        Upload Batch in CSV format
      </Button>
    </div>
  );
};

export function LicenseManagerTab() {
  const [users, setUsers] = useState<User[]>(initialUsers);

  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter((user) => user.id !== userId));
  };

  const handleEditUser = (userId: string) => {
    // Implement edit functionality
    console.log("Editing user:", userId);
  };

  const handleAddUser = (email: string) => {
    // Implement add user functionality
    console.log("Adding user with email:", email);
  };

  const handleUploadCSV = () => {
    // Implement CSV upload functionality
    console.log("Uploading CSV");
  };

  const verifiedUsers = users.filter((user) => !user.activationPending);
  const pendingUsers = users.filter((user) => user.activationPending);

  return (
    <div className="p-1 rounded-2xl">
      <div className="space-y-4 px-4">
        <SearchAndAddSection onAddUser={handleAddUser} onUploadCSV={handleUploadCSV} />

        <div className="space-y-2">
          {verifiedUsers.map((user) => (
            <UserListItem key={user.id} user={user} onDelete={handleDeleteUser} onEdit={handleEditUser} />
          ))}
          {pendingUsers.map((user) => (
            <UserListItem key={user.id} user={user} onDelete={handleDeleteUser} onEdit={handleEditUser} />
          ))}
        </div>
      </div>
    </div>
  );
}
