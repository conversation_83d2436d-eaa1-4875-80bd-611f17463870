"use client";

import { useState } from "react";
import PricingCard from "./subComponents/PricingCard";
import AccessSelectCard from "./subComponents/AccessSelectCard";

export const LicenseTab = () => {

  return (
    <div className="space-y-8 md:space-y-10 p-4 md:p-8 min-h-screen ">
      {/* Header Section with disclaimer */}
      <div className="space-y-1">
        <div className="w-full text-center mb-4"></div>
        <h2 className="text-sm font-medium dark:text-white text-gray-900">Choose your seats</h2>
        <p className="text-blue-500 text-sm">
            <span>Seats give your team members access to all premium features of the R.A.V.I.D. platform</span>
          </p>
      </div>

   
     
        <>
          {/* Access Selection Card */}
          <div className="dark:bg-[#0A1528] bg-white m-auto rounded-lg p-4 md:p-8 dark:border-[#2C3B5C] border-gray-200 border max-w-3xl">
            <AccessSelectCard />
          </div>

          {/* Plans Section */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 pt-4">
            <PricingCard
              title="Basic"
              price="TBD"
              description={["Are a professional or part of a small team up to 10 seats", "Need extra storage for you or your team."]}
            />
            <PricingCard title="Pro" price="TBD" description={["Are a part of large team up to 100 seats", "Need extra AI Tokens for you or your team."]} />
            <PricingCard
              title="Premium"
              price="TBD"
              description={[
                "Are a part of larger team with more than 100 up to 400 seats",
                "Need extra storage for you or your team.",
                "Need extra AI Tokens for you or your team.",
              ]}
            />
          </div>
        </>

    
    </div>
  );
};
