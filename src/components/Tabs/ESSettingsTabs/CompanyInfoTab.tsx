import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@heroui/react";
import toast from "react-hot-toast";
import Image from "next/image";
import { useGetEnterpriseProfile, useUpdateEnterpriseProfile, useRemoveEnterpriseImage } from "@/hooks/useEnterpriseSolution";
import { EnterpriseFormValidation, EnterpriseFormType } from "@/lib/utils/validations";
import { FormInput, FormSelect, FormTextArea } from "@/components/Forms/commons";
import { languageSpokenOptions } from "@/config/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller } from "react-hook-form";
import { Input } from "@heroui/react";
import Spinner from "@/components/Spinner";
import LogoImageUpload from "@/components/LogoImageUpload";

export const CompanyInfoTab = () => {
  const { data: enterprise, isLoading } = useGetEnterpriseProfile(false);
  const { mutate: updateEnterpriseProfile, isPending: isUpdating } = useUpdateEnterpriseProfile();
  const { mutate: removeEnterpriseImage, isPending: isRemoving } = useRemoveEnterpriseImage();

  const form = useForm<EnterpriseFormType>({
    shouldUnregister: false,
    resolver: zodResolver(EnterpriseFormValidation),
    mode: "onBlur",
    values: {
      ...enterprise,
      contact_number: enterprise?.contact_number || "",
      language_spoken: enterprise?.language_spoken || "en",
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = form;

  // Update form values when enterprise data changes
  useEffect(() => {
    if (enterprise) {
      reset({
        ...enterprise,
        language_spoken: enterprise.language_spoken || "en",
      });
    }
  }, [enterprise, reset]);

  console.log(errors);

  const handleSubmitCallback = (data: EnterpriseFormType) => {
    updateEnterpriseProfile(data, {
      onSuccess: () => {
        toast.success("Enterprise profile updated successfully");
      },
      onError: () => {
        toast.error("Failed to update enterprise profile");
      },
    });
  };

  const handleDeleteLogo = () => {
    setValue("logo", null);
    removeEnterpriseImage();
  };

  // Add error logging to debug validation issues
  console.log("Form errors:", errors);

  return (
    <form id="company-info-form" onSubmit={handleSubmit(handleSubmitCallback)}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="p-4 border dark:border-slate-800 rounded-xl">
          <h2 className="text-sm mb-4">Company Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput placeholder="Company's Name" name="name" control={control} type="text" />
            <FormInput placeholder="Identification Number" name="identification_number" control={control} type="text" />
            <div>
              <Controller
                name="date_of_incorporation"
                control={control}
                render={({ field }) => {
                  const [dateType, setDateType] = useState("text");

                  return (
                    <div className="w-full">
                      <Input
                        classNames={{
                          input: "text-xs",
                          label: "text-xs text-gray-500",
                          inputWrapper: "dark:bg-slate-900",
                        }}
                        type={dateType}
                        placeholder="Date of Incorporation"
                        value={field.value || ""}
                        onFocus={() => setDateType("date")}
                        onBlur={(e) => {
                          field.onBlur();
                          if (!e.target.value) setDateType("text");
                        }}
                        onChange={field.onChange}
                      />
                      {errors.date_of_incorporation && <div className="text-red-600 text-xs mt-1">{errors.date_of_incorporation.message}</div>}
                    </div>
                  );
                }}
              />
            </div>
            <FormInput placeholder="State of Incorporation" name="state_of_incorporation" control={control} type="text" />
            <FormSelect className="col-span-2" placeholder="Select Language" name="language_spoken" control={control} options={languageSpokenOptions} />
          </div>
        </div>

        {/* Right Column */}
        <div className="p-4 border dark:border-slate-800 rounded-xl">
          <h2 className="text-sm mb-4">Address & Contact Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput placeholder="Contact Number" name="contact_number" control={control} type="tel" />
            <FormInput placeholder="Email" name="admin_email" control={control} type="email" />
            <FormInput placeholder="Address" name="location.address" control={control} type="text" />
            <FormInput placeholder="City" name="location.city" control={control} type="text" />
            <FormInput placeholder="Country" name="location.country" control={control} type="text" />
            <FormInput placeholder="Zip Code" name="location.zip_code" control={control} type="text" />
          </div>
        </div>
      </div>

      {/* Logo Upload and Additional Information Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {/* Upload Enterprise Logo Section */}
        <div className="space-y-3 bg-white dark:bg-[#030712] rounded-2xl p-3 md:p-4 border border-gray-200 dark:border-gray-700 h-fit">
          <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-600 pb-2">
            <h2 className="text-sm text-gray-900 dark:text-white">Upload Company Logo</h2>
          </div>
          <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg h-48 flex justify-center items-center">
            {isLoading || isRemoving ? (
              <Spinner />
            ) : enterprise?.logo ? (
              <>
                <Image src={enterprise.logo} alt="Enterprise Logo" width={128} height={128} className="rounded-full object-cover w-32 h-32" />
                <LogoImageUpload currentImage={enterprise.logo} onDelete={handleDeleteLogo} />
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Drag and drop or Upload from Files</p>
                <LogoImageUpload currentImage={enterprise?.logo} />
              </div>
            )}
          </div>
        </div>

        {/* Additional Information Section */}
        <div
          className="p-4 border dark:border-slate-800 
        
        rounded-xl"
        >
          <h2 className="text-sm mb-4">Company Profile / Website Information</h2>
          <div className="space-y-4">
            <FormTextArea customClassNames={{ input: "min-h-[100px]" }} placeholder="Company Profile Summary" name="about" control={control} />
            <FormInput placeholder="Website URL (To enable logo to be hyperlinked)" name="website" control={control} type="url" />
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-4 mt-8 px-4 pb-4">
        <Button
          size="sm"
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg flex items-center gap-2 w-full md:w-fit"
          disabled={isUpdating}
        >
          {isUpdating ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Saving...</span>
            </>
          ) : (
            <span>Save Changes</span>
          )}
        </Button>
      </div>
    </form>
  );
};
