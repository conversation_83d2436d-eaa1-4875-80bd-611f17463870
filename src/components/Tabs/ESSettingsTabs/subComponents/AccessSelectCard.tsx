import SpinnerLoading from "@/components/SpinnerLoading";
import SubmitButton from "@/components/SubmitButton";
import { useAddEnterpriseUser, useGetEnterpriseProfile, useListEnterpriseInvites } from "@/hooks/useEnterpriseSolution";
import { Button, Input } from "@heroui/react";
import { useState } from "react";

const AccessSelectCard = () => {
  const [selectedTab, setSelectedTab] = useState<"email" | "batch">("email");
  const [email, setEmail] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const { mutate: addEnterpriseUser, isPending: isAddingUser } = useAddEnterpriseUser();
  const { data: enterpriseProfile } = useGetEnterpriseProfile(false);
  const { data: enterpriseInvites, isLoading: isLoadingInvites } = useListEnterpriseInvites(enterpriseProfile?.id);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleAddUser = () => {
    addEnterpriseUser({ enterprise_id: enterpriseProfile?.id, email });
    setEmail("");
  };

  return (
    <div className="space-y-6">
      <div className="flex gap-2">
        <Button
          size="sm"
          className={`rounded-full ${
            selectedTab === "email" ? "bg-blue-600 text-white" : "bg-transparent dark:border-[#2C3B5C] border-gray-300 border dark:text-gray-300 text-gray-700"
          }`}
          onPress={() => setSelectedTab("email")}
        >
          Add by Email
        </Button>
        <Button
          size="sm"
          className={`rounded-full ${
            selectedTab === "batch" ? "bg-blue-600 text-white" : "bg-transparent dark:border-[#2C3B5C] border-gray-300 border dark:text-gray-300 text-gray-700"
          }`}
          onPress={() => setSelectedTab("batch")}
        >
          Upload Batch
        </Button>
      </div>

      {selectedTab === "email" ? (
        <>
          <div className="flex gap-2">
            <Input
              size="sm"
              placeholder="Enter Email of New User"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              classNames={{
                input: "text-xs",
                inputWrapper: "dark:bg-slate-950/50 bg-gray-50 flex-1",
              }}
            />
            <SubmitButton label="Add User" isLoading={isAddingUser} isLoadingValue="Adding..." onClick={handleAddUser} />
          </div>
          <div className="flex flex-col gap-2 text-sm dark:text-gray-300 text-gray-700">
            {isLoadingInvites ? (
              <SpinnerLoading />
            ) : enterpriseInvites?.length > 0 ? (
              enterpriseInvites?.map((invite: any) => (
                <div key={invite.id} className="p-3 border dark:border-[#2C3B5C] border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{invite.user.email}</div>
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${invite.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}
                    >
                      {invite.status}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">Assigned: {new Date(invite.assigned_at).toLocaleDateString()}</div>
                </div>
              ))
            ) : (
              <div className="text-center py-4">No invites yet</div>
            )}
          </div>
        </>
      ) : (
        <div className="space-y-4">
          <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed dark:border-[#2C3B5C] border-gray-300 rounded-lg dark:bg-slate-950/50 bg-gray-50">
            <input type="file" accept=".csv" onChange={handleFileChange} className="hidden" id="csv-upload" />
            <label htmlFor="csv-upload" className="cursor-pointer text-center space-y-2">
              <div className="text-3xl">📄</div>
              <div className="text-sm dark:text-gray-300 text-gray-700">{file ? file.name : "Drop your CSV file here or click to browse"}</div>
              <div className="text-xs text-gray-500">Only CSV files are supported</div>
            </label>
          </div>
          <Button size="sm" className="bg-blue-600 text-white px-4 w-full" isDisabled={!file}>
            Upload and Add Users
          </Button>
        </div>
      )}

      <p className="text-xs text-gray-500 italic">
        {selectedTab === "email"
          ? "**Add users one-by-one via enter the email of the users"
          : "**Upload a CSV file containing email addresses of multiple users"}
      </p>
    </div>
  );
};

export default AccessSelectCard;
