import { FC } from "react";
import { Card, CardBody } from "@heroui/card";
import { Button } from "@heroui/button";

interface PlanProps {
  title: string;
  description: string;
  seats?: string;
  monthlyPrice?: string;
  yearlyPrice?: string;
  supportText?: string;
  isCustom?: boolean;
}

interface AddOnCardProps {
  title: string;
  description: string;
  isCustom?: boolean;
}

const Plan = ({ title, description, seats, monthlyPrice, yearlyPrice, supportText, isCustom }: PlanProps) => (
  <Card className={`dark:bg-[#081124] bg-white h-52 border-[#2C3B5C] ${title === "Pro" ? "shadow-lg shadow-blue-500/20" : ""}`}>
    <CardBody className="p-4 space-y-3">
      <div className="space-y-1">
        <h1 className="font-medium dark:text-white text-gray-900">
          {title} {description}
        </h1>
        <div className="text-sm font-semibold dark:text-white text-gray-900">${monthlyPrice}</div>
      </div>
      {!isCustom ? (
        <div className="text-xs dark:text-gray-300 text-gray-900">
          <div>Choose Professional if you:</div>
          <ul className="space-y-1 mt-1 ml-4">
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Are a part of large team up to 100 seats</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Need extra AI Tokens for you or your team.</span>
            </li>
          </ul>
        </div>
      ) : (
        <Button size="sm" className="m-auto w-1/2 bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          Contact Us
        </Button>
      )}
    </CardBody>
  </Card>
);

const AddOnCard = ({ title, description, isCustom }: AddOnCardProps) => (
  <Card className={`w-32 h-24 dark:bg-[#081124] border-[#2C3B5C] ${isCustom ? "shadow-lg shadow-blue-500/20" : ""}`}>
    <CardBody className="flex flex-col items-center justify-center p-2 text-center">
      <h1>{title}</h1>
      <p className="text-center">{description}</p>
    </CardBody>
  </Card>
);

interface ActivePlanSectionProps {}

const ActivePlanSection: FC<ActivePlanSectionProps> = () => {
  return (
    <div className="space-y-6">
      <h2 className="text-base font-medium dark:text-white text-gray-900">Active Plan and Add - Ons :</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="md:col-span-1">
          <Plan title="Pro" description="Access" monthlyPrice="42" yearlyPrice="420" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <AddOnCard title="1 TB" description="Extra Storage Across License" />

          <AddOnCard title="1 Million" description="Tokens for AI Agent Gemini by Google" />

          <AddOnCard title="1 Million" description="Tokens for AI Agent Claude-Sonnet" />
        </div>
      </div>
    </div>
  );
};

export default ActivePlanSection;
