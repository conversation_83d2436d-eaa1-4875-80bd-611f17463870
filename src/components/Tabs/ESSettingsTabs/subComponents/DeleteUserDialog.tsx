"use client";

import { Button, useDisclosure } from "@heroui/react";
import CustomModal from "@/components/Modals/CustomModal";
import { Trash2 } from "lucide-react";
import { useState } from "react";

interface DeleteUserDialogProps {
  userName: string;
  userId: string;
  onDelete: (id: string) => void;
  variant?: "icon" | "button";
}

export function DeleteUserDialog({ userName, userId, onDelete, variant = "button" }: DeleteUserDialogProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleDelete = () => {
    onDelete(userId);
    setIsOpen(false);
  };

  return (
    <>
      {variant === "icon" ? (
        <Button isIconOnly variant="light" size="sm" className="text-gray-400" onPress={() => setIsOpen(true)}>
          <Trash2 className="w-4 h-4 text-red-500" />
        </Button>
      ) : (
        <Button size="sm" color="danger" onPress={() => setIsOpen(true)}>
          Delete User
        </Button>
      )}

      <CustomModal
        title="Delete User"
        body={<p>Are you sure you want to delete {userName}? This action cannot be undone.</p>}
        primaryButtonText="Delete"
        secondaryButtonText="Cancel"
        onPrimaryAction={handleDelete}
        onSecondaryAction={() => setIsOpen(false)}
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        primaryButtonColor="bg-red-700 hover:bg-red-800 text-white"
      />
    </>
  );
}
