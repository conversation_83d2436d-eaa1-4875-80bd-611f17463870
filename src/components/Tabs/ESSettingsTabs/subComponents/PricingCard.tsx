import { FC } from "react";

interface PricingCardProps {
  title: string;
  price: string;
  description: string[];
  isHighlighted?: boolean;
}

const PricingCard: FC<PricingCardProps> = ({ title, price, description, isHighlighted = false }) => {
  return (
    <div
      className={`rounded-lg dark:bg-[#0A1528] bg-white p-4 sm:p-6 flex flex-col space-y-4 ${
        isHighlighted ? "border border-blue-500" : "border dark:border-[#2C3B5C] border-gray-200"
      } transition-colors duration-200 h-full`}
    >
      <div className="space-y-1">
        <h2 className="text-sm font-medium dark:text-white text-gray-900">{title} Access</h2>
        <p className="text-sm font-bold dark:text-white text-gray-900">{price}</p>
      </div>

      <div className="space-y-2">
        <p className="text-sm dark:text-gray-300 text-gray-700">Choose {title} if you:</p>
        <ul className="space-y-2">
          {description.map((item, index) => (
            <li key={index} className="flex items-start gap-2 text-xs dark:text-gray-300 text-gray-600">
              <span className="text-xs mt-0.5 dark:text-gray-300 text-gray-600">•</span>
              <span>{item}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default PricingCard;
