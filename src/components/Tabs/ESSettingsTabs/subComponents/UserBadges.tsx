"use client";

import { User } from "../LicenseManagerTab";

interface UserBadgesProps {
  user: User;
}

export function UserBadges({ user }: UserBadgesProps) {
  return (
    <div className="flex flex-row items-center gap-2 flex-1 justify-center flex-wrap">
      <span
        className={`whitespace-nowrap px-3 py-1 ${
          user.status === "Verified" ? "bg-gradient-to-b from-blue-700 to-blue-900" : "bg-gray-700"
        } text-white text-xs rounded-full`}
      >
        {user.status}
      </span>

      {user.aiAnalysis && (
        <span className="whitespace-nowrap px-3 py-1 bg-gradient-to-b from-purple-800 to-purple-900 text-white text-xs rounded-full flex items-center gap-1">
          <svg width="16" height="16" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8.69544 4.49728C7.65841 3.45987 6.94861 1.6483 6.59636 0C6.24356 1.64864 5.53451 3.46059 4.49711 4.49799C3.46008 5.53468 1.64796 6.24448 0 6.59745C1.64864 6.9497 3.46042 7.65908 4.49745 8.69653C5.53447 9.73355 6.24427 11.5455 6.59707 13.1938C6.94932 11.5452 7.65892 9.73322 8.69577 8.69581C9.7328 7.65879 11.5449 6.94932 13.1929 6.59636C11.5446 6.2441 9.73284 5.53434 8.69544 4.49728Z"
              fill="url(#paint0_linear_1022_31082)"
            />
            <path
              d="M15.4311 12.8039C14.8121 12.1846 14.3873 11.1018 14.1769 10.1172C13.9661 11.1018 13.5428 12.1846 12.9231 12.8043C12.3034 13.4233 11.2208 13.8477 10.2363 14.0585C11.2211 14.2692 12.3034 14.6929 12.9231 15.3126C13.5428 15.932 13.9668 17.0148 14.1776 17.9994C14.388 17.0144 14.8121 15.932 15.4314 15.3123C16.0507 14.6929 17.1335 14.2689 18.1178 14.0578C17.1336 13.8473 16.0508 13.4233 15.4311 12.8039Z"
              fill="url(#paint1_linear_1022_31082)"
            />
            <defs>
              <linearGradient id="paint0_linear_1022_31082" x1="6.59644" y1="0" x2="6.59644" y2="13.1938" gradientUnits="userSpaceOnUse">
                <stop stopColor="#7D5191" />
                <stop offset="1" stopColor="#FFD52B" />
              </linearGradient>
              <linearGradient id="paint1_linear_1022_31082" x1="14.1771" y1="10.1172" x2="14.1771" y2="17.9994" gradientUnits="userSpaceOnUse">
                <stop stopColor="#7D5191" />
                <stop offset="1" stopColor="#FFD52B" />
              </linearGradient>
            </defs>
          </svg>
          AI Analysis
        </span>
      )}

      <span className="whitespace-nowrap px-3 py-1 bg-gradient-to-b from-[#5c5236] to-[#433c27] text-white text-xs rounded-full">{user.dataStorage}</span>
    </div>
  );
}
