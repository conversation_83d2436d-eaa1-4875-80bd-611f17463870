import { FC } from "react";
import { Button, Input } from "@heroui/react";

interface LicenseSummaryCardProps {
  licensedSeats: number;
  seatsInUse: number;
  onAssignSeats?: () => void;
  onAddMoreSeats?: () => void;
}

const LicenseSummaryCard: FC<LicenseSummaryCardProps> = ({ licensedSeats, seatsInUse, onAssignSeats, onAddMoreSeats }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-base font-medium dark:text-white text-gray-900">License & Seats Added</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <p className="text-sm dark:text-gray-300 text-gray-700">Licensed number of Seats:</p>
          <Input
            size="sm"
            value={licensedSeats.toString()}
            isReadOnly
            classNames={{
              input: "text-sm",
              inputWrapper: "dark:bg-slate-950/50 bg-gray-50",
            }}
          />
        </div>

        <div className="space-y-2">
          <p className="text-sm dark:text-gray-300 text-gray-700">Seats in use:</p>
          <div className="flex space-x-4">
            <Input
              size="sm"
              value={`${seatsInUse} out of ${licensedSeats}`}
              isReadOnly
              classNames={{
                input: "text-sm",
                inputWrapper: "dark:bg-slate-950/50 bg-gray-50 flex-grow",
              }}
            />
            <Button size="sm" color="primary" className="w-24" onPress={onAssignSeats}>
              Assign Seats
            </Button>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button
          size="sm"
          className="dark:bg-[#0A1528] bg-white border dark:border-[#2C3B5C] border-gray-200 dark:text-white text-gray-900"
          onPress={onAddMoreSeats}
          startContent={<PlusIcon />}
        >
          Add more seats
        </Button>
      </div>
    </div>
  );
};

const PlusIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 3.33334V12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M3.33331 8H12.6666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export default LicenseSummaryCard;
