"use client";

import Image from "next/image";
import { CheckCircle2, XCircle } from "lucide-react";
import { User } from "../LicenseManagerTab";

interface UserAvatarProps {
  user: User;
}

export function UserAvatar({ user }: UserAvatarProps) {
  return (
    <div className="relative">
      {!user.activationPending ? (
        <div className="relative border-2 rounded-full bg-gradient-to-b from-yellow-600 via-yellow-700 to-white p-[2px]">
          <div className="rounded-full overflow-hidden w-[48px] h-[48px]">
            <Image src={user.avatar} alt={user.name} width={48} height={48} className="w-full h-full object-cover" />
          </div>
        </div>
      ) : (
        <div className="relative border-2 rounded-full p-[2px]">
          <div className="overflow-hidden w-[48px] h-[48px] rounded-full bg-gray-300 flex items-center justify-center text-xl">
            {user.name.substring(0, 2).toUpperCase()}
          </div>
        </div>
      )}

      <div className="absolute -bottom-1 -right-1 lg:hidden">
        {!user.activationPending ? (
          <CheckCircle2 className="w-5 h-5 text-blue-700 bg-slate-900 rounded-full" />
        ) : (
          <XCircle className="w-5 h-5 text-red-700 bg-slate-900 rounded-full" />
        )}
      </div>
    </div>
  );
}
