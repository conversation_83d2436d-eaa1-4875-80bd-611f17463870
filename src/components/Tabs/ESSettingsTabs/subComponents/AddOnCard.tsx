import { FC } from "react";

interface AddOnCardProps {
  title: string;
  subtitle: string;
  icon?: React.ReactNode;
  isSelected?: boolean;
  onClick?: () => void;
}

const AddOnCard: FC<AddOnCardProps> = ({ title, subtitle, icon, isSelected = false, onClick }) => {
  return (
    <div
      className={`rounded-lg dark:bg-[#0A1528] bg-white p-4 border ${
        isSelected ? "border-blue-500" : "dark:border-[#2C3B5C] border-gray-200"
      } flex flex-col items-center justify-center text-center space-y-2 cursor-pointer hover:border-blue-500 transition-colors`}
      onClick={onClick}
    >
      {icon}
      <div className="space-y-1">
        <p className="text-sm font-medium dark:text-white text-gray-900">{title}</p>
        <p className="text-xs dark:text-gray-300 text-gray-600">{subtitle}</p>
      </div>
    </div>
  );
};

export const StorageIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M21 9V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path d="M3 9L12 2L21 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9 14V21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const AITokenGeminiIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 3L20 7.5V16.5L12 21L4 16.5V7.5L12 3Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12 12L20 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12 12V21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12 12L4 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const AITokenClaudeIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M21 16.5C21 18.9853 16.9706 21 12 21C7.02944 21 3 18.9853 3 16.5C3 14.0147 7.02944 12 12 12C16.9706 12 21 14.0147 21 16.5Z"
      stroke="currentColor"
      strokeWidth="1.5"
    />
    <path
      d="M21 12C21 14.4853 16.9706 16.5 12 16.5C7.02944 16.5 3 14.4853 3 12C3 9.51472 7.02944 7.5 12 7.5C16.9706 7.5 21 9.51472 21 12Z"
      stroke="currentColor"
      strokeWidth="1.5"
    />
    <path
      d="M21 7.5C21 9.98528 16.9706 12 12 12C7.02944 12 3 9.98528 3 7.5C3 5.01472 7.02944 3 12 3C16.9706 3 21 5.01472 21 7.5Z"
      stroke="currentColor"
      strokeWidth="1.5"
    />
  </svg>
);

export default AddOnCard;
