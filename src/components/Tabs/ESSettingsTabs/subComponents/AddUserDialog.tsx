"use client";

import { Checkbox, Input } from "@heroui/react";
import { Plus } from "lucide-react";
import { useState } from "react";
import CustomModal from "../../../Modals/CustomModal";

interface AddUserDialogProps {
  onAdd: (userData: { name: string; email: string; aiAnalysis: boolean; dataStorage: string }) => void;
}

// Separate component for the form content
const AddUserForm = ({
  formData,
  setFormData,
}: {
  formData: { name: string; email: string; aiAnalysis: boolean; dataStorage: string };
  setFormData: React.Dispatch<React.SetStateAction<{ name: string; email: string; aiAnalysis: boolean; dataStorage: string }>>;
}) => {
  return (
    <div className="space-y-4">
      <Input label="Name" placeholder="Enter user name" value={formData.name} onChange={(e) => setFormData({ ...formData, name: e.target.value })} />
      <Input
        label="Email"
        type="email"
        placeholder="Enter user email"
        value={formData.email}
        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
      />
      <Checkbox isSelected={formData.aiAnalysis} onValueChange={(value) => setFormData({ ...formData, aiAnalysis: value })}>
        Enable AI Analysis
      </Checkbox>
    </div>
  );
};

export function AddUserDialog({ onAdd }: AddUserDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    aiAnalysis: true,
    dataStorage: "100 GB Data Storage",
  });

  const handleSubmit = () => {
    onAdd(formData);
    setIsOpen(false);
    setFormData({
      name: "",
      email: "",
      aiAnalysis: true,
      dataStorage: "100 GB Data Storage",
    });
  };

  return (
    <CustomModal
      buttonOpenText="Add New User"
      icon={<Plus className="w-4 h-4" />}
      title="Add New User"
      body={<AddUserForm formData={formData} setFormData={setFormData} />}
      buttonText="Add User"
      onPress={handleSubmit}
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      // cancelButtonText="Cancel"
      // buttonColor="bg-primary hover:bg-primary-dark"
      className="text-xs bg-blue-600 text-white hover:bg-blue-700"
    />
  );
}
