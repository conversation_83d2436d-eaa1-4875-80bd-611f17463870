import { FC } from "react";
import { Card, CardBody } from "@heroui/card";

interface AddOnCardProps {
  title: string;
  description: string;
  isCustom?: boolean;
}

const AddOnCard = ({ title, description, isCustom }: AddOnCardProps) => (
  <Card className={`w-32 h-24 dark:bg-[#081124] border-[#2C3B5C] ${isCustom ? "shadow-lg shadow-blue-500/20" : ""}`}>
    <CardBody className="flex flex-col items-center justify-center p-2 text-center">
      <h2>{title}</h2>
      <p className="text-center">{description}</p>
    </CardBody>
  </Card>
);

interface AdditionalAddOnsSectionProps {}

const AdditionalAddOnsSection: FC<AdditionalAddOnsSectionProps> = () => {
  return (
    <div className="space-y-6 w-1/2">
      <h2 className="text-base font-medium dark:text-white text-gray-900">Get Add - Ons:</h2>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <AddOnCard title="1 TB" description="Extra Storage Across License" />

        <AddOnCard title="1 Million" description="Tokens for AI Agent Claude-Sonnet" />

        <AddOnCard title="1 Million" description="Tokens for AI Agent Gemini by Google" />
      </div>
    </div>
  );
};

export default AdditionalAddOnsSection;
