// export a simple component that displays the admin info

"use client";

import React, { useState, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { But<PERSON> } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { AdminFormType, AdminFormValidation } from "@/lib/utils/validations";
import { FormInput } from "@/components/Forms/commons";
import toast from "react-hot-toast";
import { useGetEnterpriseAdmin, useUpdateEnterpriseAdmin, useDeleteEnterpriseAdmin } from "@/hooks/useEnterpriseSolution";

export const AdminInfoTab = () => {
  const { data: enterpriseAdmin, isLoading: isAdminLoading } = useGetEnterpriseAdmin();
  const { mutate: updateEnterpriseAdmin } = useUpdateEnterpriseAdmin();
  const { mutate: deleteEnterpriseAdmin } = useDeleteEnterpriseAdmin();

  const form = useForm<AdminFormType>({
    resolver: zodResolver(AdminFormValidation),
    mode: "onSubmit",
    defaultValues: {
      admins: [],
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    getValues,
    setValue,
    watch,
    formState: { errors, isDirty, isValid },
  } = form;

  useEffect(() => {
    if (enterpriseAdmin && enterpriseAdmin.length > 0) {
      reset({ admins: enterpriseAdmin });
    } else {
      const defaultAdmins = [
        { name: "", email: "", phone_number: "", position: "technical" },
        { name: "", email: "", phone_number: "", position: "communications" },
      ];
      reset({ admins: defaultAdmins });
    }
  }, [enterpriseAdmin, reset]);

  const onSubmit = async (data: AdminFormType) => {
    updateEnterpriseAdmin(data, {
      onSuccess: () => {
        toast.success("Admin information updated successfully");
      },
      onError: () => {
        toast.error("Failed to update admin information");
      },
    });
  };

  const addAdmin = () => {
    const currentAdmins = getValues("admins");
    if (currentAdmins.length >= 4) {
      toast.error("You can only have up to 4 administrators");
      return;
    }
    const newAdmin = { name: "", email: "", phone_number: "", position: "" };
    setValue("admins", [...currentAdmins, newAdmin], { shouldValidate: false, shouldDirty: true, shouldTouch: true });
  };

  const handleRemoveAdmin = (id?: string) => {
    if (!id) return;
    deleteEnterpriseAdmin(id, {
      onSuccess: () => {
        toast.success("Admin information deleted successfully");
      },
      onError: () => {
        toast.error("Failed to delete admin information");
      },
    });
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {watch("admins")?.map((admin, index) => (
          <div key={admin.id || index} className="p-4 border dark:border-slate-800 rounded-xl space-y-4">
            <h2 className="text-sm mb-4">
              {admin.position === "technical"
                ? "Technical Administrator Information"
                : admin.position === "communications"
                ? "Communications Administrator Information"
                : "Additional Administrator"}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput placeholder="Administrator's Name" name={`admins.${index}.name`} control={control} />
              <FormInput placeholder="Work Email" name={`admins.${index}.email`} control={control} />
              <FormInput placeholder="Contact Number" name={`admins.${index}.phone_number`} control={control} />
              <FormInput
                placeholder="Role/Position"
                name={`admins.${index}.position`}
                control={control}
                disabled={admin.position === "technical" || admin.position === "communications"}
              />
              {admin.position !== "technical" && admin.position !== "communications" && (
                <Button size="sm" className="w-fit" color="danger" type="button" onPress={() => handleRemoveAdmin(admin.id)}>
                  Remove Admin
                </Button>
              )}
            </div>
          </div>
        ))}

        <div className="flex justify-center">
          <Button type="button" variant="flat" onPress={addAdmin}>
            <span className="mr-2">+</span> Add Admin
          </Button>
        </div>

        <div className="flex justify-end gap-4 mt-8 px-4 pb-4">
          <Button
            size="sm"
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg flex items-center gap-2 w-full md:w-fit"
            disabled={isAdminLoading}
          >
            {isAdminLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Saving...</span>
              </>
            ) : (
              <span>Save Changes</span>
            )}
          </Button>
        </div>
      </form>
    </FormProvider>
  );
};
