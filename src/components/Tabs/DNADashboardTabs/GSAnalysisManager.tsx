import { changeDNATab, TAB_IDS } from "@/components/Dashboards/DNADashboard";
import Disclaimer from "@/components/Disclaimer";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import { Table, TableHeader, TableRow, TableColumn, TableBody, TableCell, Button, CircularProgress, Card } from "@heroui/react";
import React from "react";
import { useAnalysisRuns, type AnalysisRun } from "@/hooks/dna-dashboard/useAnalysisRuns";

// Demo content for when no services are purchased
const analysisData = [
  {
    step: "Step 1",
    service: "Genome Sequencing Basic Analysis (GSBA)",
    date: "2025-04-11",
    status: "Not Started",
    action: "Initiate Analysis",
  },
  {
    step: "Step 2",
    service: "Genome Sequencing Basic Analysis (GSBA)",
    date: "2025-04-11",
    status: "In-Queue",
    action: "Initiated...",
  },
  {
    step: "Step 3",
    service: "Genome Sequencing Basic Analysis (GSBA)",
    date: "2025-04-11",
    status: "Ongoing",
    progress: 39.67,
  },
];

export const GSAnalysisManager = () => {
  const { analysisRuns, loading, error, refetch } = useAnalysisRuns();

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
      case "not started":
        return "bg-opacity-20";
      case "in-queue":
        return "bg-opacity-20 bg-blue-600";
      case "running":
      case "ongoing":
        return "bg-opacity-40 bg-yellow-400";
      case "completed":
        return "bg-opacity-40 bg-green-400";
      case "failed":
        return "bg-opacity-40 bg-red-400";
      default:
        return "bg-opacity-20";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "In-Queue";
      case "processing":
        return "Processing";
      case "completed":
        return "Completed";
      case "failed":
        return "Failed";
      default:
        return status;
    }
  };

  const getActionButton = (analysisRun: AnalysisRun) => {
    const status = analysisRun.status.toLowerCase();

    if (status === "pending") {
      return (
        <Button size="sm" isDisabled>
          Initiated...
        </Button>
      );
    } else if (status === "processing") {
      const progress = analysisRun.total_steps > 0 ? Math.round((analysisRun.current_step / analysisRun.total_steps) * 100) : 0;

      return (
        <div className="flex items-center justify-center">
          <CircularProgress aria-label="Loading..." color="warning" showValueLabel={true} size="lg" value={progress} />
        </div>
      );
    } else if (status === "completed") {
      return (
        <Button size="sm" color="success" variant="bordered">
          Completed
        </Button>
      );
    } else {
      return (
        <Button size="sm" color="primary">
          Initiate Analysis
        </Button>
      );
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const navigateToGSFileManager = () => {
    changeDNATab(TAB_IDS.GS_FILE_MANAGER);
  };

  const analysisFinalisedData = {
    service: "Genome Sequencing Basic Analysis (GSBA)",
    date: "2025-04-11, 12:45 P.M.",
    dateCompleted: "2025-04-12, 11:09 AM",
    status: "Ongoing",
    action: "Completed",
  };

  // Demo content component
  const renderDemoContent = () => (
    <div className="space-y-4">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <h2>My Genome Sequence (GS) Analysis Manager</h2>
          <Button size="sm" variant="ghost" onPress={() => refetch()}>
            Refresh
          </Button>
        </div>
        <p>Upload, store, share, use Enable AI Analysis and manage all your genome sequenced files here:</p>
      </div>
      {analysisData.map((analysis, index) => (
        <React.Fragment key={index}>
          <span className="text-xs blue-heading mt-2">{analysis.step}</span>
          <Card className="p-2 dark:bg-slate-900">
            <div className="w-full overflow-x-auto mb-6">
              <div className="min-w-[768px]">
                <Table
                  aria-label="Input files table"
                  classNames={{
                    base: "dark:bg-slate-900/40",
                    th: "dark:bg-slate-900/90 border-b dark:border-slate-800",
                    td: "py-1 px-3",
                  }}
                  className="rounded-lg border dark:border-gray-800 w-full"
                  removeWrapper
                >
                  <TableHeader>
                    <TableColumn>
                      <h2>File Name</h2>
                    </TableColumn>
                    <TableColumn>
                      <h2>Date</h2>
                    </TableColumn>
                    <TableColumn align="center">
                      <h2>Size</h2>
                    </TableColumn>
                    <TableColumn align="center">
                      <h2>Status</h2>
                    </TableColumn>
                  </TableHeader>

                  <TableBody>
                    <TableRow>
                      <TableCell>
                        <h2>john_doe_234657.bam</h2>
                      </TableCell>
                      <TableCell>
                        <h2>2025-03-12, 11:09 AM</h2>
                      </TableCell>
                      <TableCell>
                        <h2>80 GB</h2>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" onPress={navigateToGSFileManager}>Transferred from GS File Manager</Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>

            <div className="w-full overflow-x-auto mb-6">
              <div className="min-w-[768px]">
                <Table
                  aria-label="Services purchased table"
                  classNames={{
                    base: "dark:bg-slate-900/40",
                    th: "dark:bg-slate-900/90 border-b dark:border-slate-800",
                    td: "py-1 px-3",
                  }}
                  className="rounded-lg border dark:border-gray-800 w-full"
                  removeWrapper
                >
                  <TableHeader>
                    <TableColumn width="26%">
                      <h2>Services Purchased</h2>
                    </TableColumn>
                    <TableColumn width="27%" align="start">
                      <h2>Date Purchased</h2>
                    </TableColumn>
                    <TableColumn width="12%" align="start">
                      <h2>Status</h2>
                    </TableColumn>
                    <TableColumn align="center">
                      <h2>Actions</h2>
                    </TableColumn>
                  </TableHeader>

                  <TableBody>
                    <TableRow>
                      <TableCell>
                        <h2>{analysis.service}</h2>
                      </TableCell>
                      <TableCell>
                        <h2>{analysis.date}</h2>
                      </TableCell>
                      <TableCell>
                        <div className={`badge ${getStatusClass(analysis.status)}`}>{analysis.status}</div>
                      </TableCell>
                      <TableCell>
                        {analysis.status === "In-Queue" ? (
                          <Button size="sm" isDisabled className="">
                            {analysis.action}
                          </Button>
                        ) : analysis.status === "Processing" ? (
                          <div className="flex items-center justify-center">
                            <CircularProgress aria-label="Loading..." color="warning" showValueLabel={true} size="lg" value={analysis.progress || 0} />
                          </div>
                        ) : (
                          <Button size="sm" color="primary" className="">
                            {analysis.action}
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>
          </Card>
        </React.Fragment>
      ))}
      <h2 className="text-xs blue-heading">Step 4</h2>
      <Card className="p-2 dark:bg-slate-900">
        <div className="w-full overflow-x-auto mb-6 space-y-2">
          <div className="min-w-[768px]">
            <Table
              aria-label="Input files table"
              classNames={{
                base: "dark:bg-slate-900/40",
                th: "dark:bg-slate-900/90 border-b dark:border-slate-800",
                td: "py-2 px-3",
                tr: "border-b dark:border-slate-800 last:border-0",
              }}
              className="rounded-lg border dark:border-gray-800 w-full"
              removeWrapper
            >
              <TableHeader>
                <TableColumn width="30%">
                  <h2>File Name</h2>
                </TableColumn>
                <TableColumn>
                  <h2>Date</h2>
                </TableColumn>
                <TableColumn align="center">
                  <h2>Size</h2>
                </TableColumn>
                <TableColumn width="30%" align="center">
                  <h2>Status</h2>
                </TableColumn>
              </TableHeader>

              <TableBody>
                <TableRow>
                  <TableCell>
                    <h2>john_doe_234657.bam</h2>
                  </TableCell>
                  <TableCell>
                    <h2>2025-03-12, 11:09 AM</h2>
                  </TableCell>
                  <TableCell>
                    <h2>80 GB</h2>
                  </TableCell>
                  <TableCell>
                    <Button onPress={navigateToGSFileManager} size="sm" className="px-6">
                      Transfer back to GS File Manager
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <h2>R.A.V.I.D. Generated _36328.vcf</h2>
                  </TableCell>
                  <TableCell>
                    <h2>2025-04-12, 11:09 AM</h2>
                  </TableCell>
                  <TableCell>
                    <h2>5 GB</h2>
                  </TableCell>
                  <TableCell>
                    <Button onPress={navigateToGSFileManager} size="sm" className="px-6">
                      Transfer back to GS File Manager
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <h2>R.A.V.I.D. Generated_36328.json</h2>
                  </TableCell>
                  <TableCell>
                    <h2>2025-04-13, 12:00 AM</h2>
                  </TableCell>
                  <TableCell>
                    <h2>689 KB</h2>
                  </TableCell>
                  <TableCell>
                    <Button onPress={navigateToGSFileManager} size="sm" className="px-6">
                      Transfer back to GS File Manager
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <h2>R.A.V.I.D. Generated_363568.json</h2>
                  </TableCell>
                  <TableCell>
                    <h2>2025-04-13, 12:00 AM</h2>
                  </TableCell>
                  <TableCell>
                    <h2>20 KB</h2>
                  </TableCell>
                  <TableCell>
                    <Button onPress={navigateToGSFileManager} size="sm" className="px-6">
                      Transfer back to GS File Manager
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
          <div className="min-w-[768px] w-full overflow-x-auto">
            <Table
              aria-label="Service completion status table"
              classNames={{
                base: "dark:bg-slate-900/40",
                th: "dark:bg-slate-900/90 border-b dark:border-slate-800",
                td: "p-4",
                tr: "border-b dark:border-slate-800 last:border-0",
              }}
              className="rounded-lg border dark:border-gray-800 w-full"
              removeWrapper
            >
              <TableHeader>
                <TableColumn width="30%">
                  <h2>Service Purchased</h2>
                </TableColumn>
                <TableColumn align="start">
                  <h2>Date Purchased</h2>
                </TableColumn>
                <TableColumn align="center">
                  <h2>Date Completed</h2>
                </TableColumn>
                <TableColumn width="30%" align="center">
                  <h2>Status</h2>
                </TableColumn>
              </TableHeader>

              <TableBody>
                <TableRow>
                  <TableCell>
                    <h2>{analysisFinalisedData.service}</h2>
                  </TableCell>
                  <TableCell>
                    <h2>{analysisFinalisedData.date}</h2>
                  </TableCell>
                  <TableCell>
                    <h2>{analysisFinalisedData.dateCompleted}</h2>
                  </TableCell>
                  <TableCell>
                    <Button size="sm" color="success" variant="bordered" className="px-6">
                      {analysisFinalisedData.action}
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </Card>
    </div>
  );

  if (loading) {
    return (
      <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
        <Disclaimer />
        <ImportantNoticeDropdown />
        <div className="flex justify-center items-center py-8">
          <CircularProgress aria-label="Loading analysis runs..." size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
        <Disclaimer />
        <ImportantNoticeDropdown />
        <div className="flex justify-center items-center py-8 space-y-4">
          <Card className="p-4 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800">
            <p className="text-red-600 dark:text-red-400 mb-4">Error loading analysis runs: {error}</p>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
      <Disclaimer />
      <ImportantNoticeDropdown />
      
      {analysisRuns.length === 0 ? (
        // Show demo content when no services purchased
        renderDemoContent()
      ) : (
        // Show actual analysis runs data
        <div className="space-y-4">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <h2>My Genome Sequence (GS) Analysis Manager</h2>
                <Button size="sm" variant="ghost" onPress={() => refetch()}>
                Refresh
              </Button>
            </div>
            <p>Upload, store, share, use Enable AI Analysis and manage all your genome sequenced files here:</p>
          </div>

          {analysisRuns.map((analysisRun, index) => (
            <div key={analysisRun.id}>
              <span className="text-xs blue-heading mt-2">Analysis Run {index + 1}</span>
              <Card className="p-2 dark:bg-slate-900">
                {/* Input Files Table */}
                <div className="w-full overflow-x-auto mb-6">
                  <div className="min-w-[768px]">
                    <Table
                      aria-label="Input files list"
                      classNames={{
                        base: "dark:bg-slate-900/40",
                        th: "dark:bg-slate-900/90 border-b dark:border-slate-800",
                        td: "py-1 px-3",
                      }}
                      className="rounded-lg border dark:border-gray-800 w-full"
                      removeWrapper
                    >
                      <TableHeader>
                        <TableColumn>
                          <h2>File Name</h2>
                        </TableColumn>
                        <TableColumn>
                          <h2>Date</h2>
                        </TableColumn>
                        <TableColumn align="center">
                          <h2>Size</h2>
                        </TableColumn>
                        <TableColumn align="center">
                          <h2>Status</h2>
                        </TableColumn>
                      </TableHeader>

                      <TableBody>
                        {analysisRun.input_files
                          .filter((file) => !file.is_result)
                          .map((file) => (
                            <TableRow key={file.id}>
                              <TableCell>
                                <h2>{file.filename}</h2>
                              </TableCell>
                              <TableCell>
                                <h2>{formatDate(analysisRun.created_at)}</h2>
                              </TableCell>
                              <TableCell>
                                <h2>{formatFileSize(file.file_size)}</h2>
                              </TableCell>
                              <TableCell>
                                <Button size="sm" onPress={navigateToGSFileManager}>
                                  Transferred from GS File Manager
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                {/* Analysis Status Table */}
                <div className="w-full overflow-x-auto mb-6">
                  <div className="min-w-[768px]">
                    <Table
                      aria-label="Analysis service status"
                      classNames={{
                        base: "dark:bg-slate-900/40",
                        th: "dark:bg-slate-900/90 border-b dark:border-slate-800",
                        td: "py-1 px-3",
                      }}
                      className="rounded-lg border dark:border-gray-800 w-full"
                      removeWrapper
                    >
                      <TableHeader>
                        <TableColumn width="26%">
                          <h2>Services Purchased</h2>
                        </TableColumn>
                        <TableColumn width="27%" align="start">
                          <h2>Date Purchased</h2>
                        </TableColumn>
                        <TableColumn width="12%" align="start">
                          <h2>Status</h2>
                        </TableColumn>
                        <TableColumn align="center">
                          <h2>Actions</h2>
                        </TableColumn>
                      </TableHeader>

                      <TableBody>
                        <TableRow>
                          <TableCell>
                            <h2>Genome Sequencing Basic Analysis (GSBA)</h2>
                          </TableCell>
                          <TableCell>
                            <h2>{formatDate(analysisRun.created_at)}</h2>
                          </TableCell>
                          <TableCell>
                            <div className={`badge ${getStatusClass(analysisRun.status)}`}>{getStatusLabel(analysisRun.status)}</div>
                          </TableCell>
                          <TableCell>{getActionButton(analysisRun)}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </div>

                {/* Result Files Table - Show only if analysis is completed and has result files */}
                {analysisRun.status.toLowerCase() === "completed" && analysisRun.input_files.some((file) => file.is_result) && (
                  <div className="w-full overflow-x-auto mb-6 space-y-2">
                    <h3 className="text-sm font-semibold">Generated Results:</h3>
                    <div className="min-w-[768px]">
                      <Table
                        aria-label="Analysis result files"
                        classNames={{
                          base: "dark:bg-slate-900/40",
                          th: "dark:bg-slate-900/90 border-b dark:border-slate-800",
                          td: "py-2 px-3",
                          tr: "border-b dark:border-slate-800 last:border-0",
                        }}
                        className="rounded-lg border dark:border-gray-800 w-full"
                        removeWrapper
                      >
                        <TableHeader>
                          <TableColumn width="30%">
                            <h2>File Name</h2>
                          </TableColumn>
                          <TableColumn>
                            <h2>Date</h2>
                          </TableColumn>
                          <TableColumn align="center">
                            <h2>Size</h2>
                          </TableColumn>
                          <TableColumn width="30%" align="center">
                            <h2>Status</h2>
                          </TableColumn>
                        </TableHeader>

                        <TableBody>
                          {analysisRun.input_files
                            .filter((file) => file.is_result)
                            .map((file) => (
                              <TableRow key={file.id}>
                                <TableCell>
                                  <h2>{file.filename}</h2>
                                </TableCell>
                                <TableCell>
                                  <h2>{formatDate(analysisRun.updated_at)}</h2>
                                </TableCell>
                                <TableCell>
                                  <h2>{formatFileSize(file.file_size)}</h2>
                                </TableCell>
                                <TableCell>
                                  <Button onPress={navigateToGSFileManager} size="sm" className="px-6">
                                    Transfer back to GS File Manager
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
