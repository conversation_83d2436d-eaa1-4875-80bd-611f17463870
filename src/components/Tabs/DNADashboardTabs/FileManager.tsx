"use client";

import Disclaimer from "@/components/Disclaimer";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import { useMyFiles } from "@/hooks/dna-dashboard/use-file-manager";
import { useFileProcessing } from "@/hooks/dna-dashboard/useFileProcessing";
import { useFileSelection } from "@/hooks/dna-dashboard/useFileSelection";
import { Button } from "@heroui/button";
import { useDisclosure } from "@heroui/react";
import { useState } from "react";
import FileUploadModal from "./subComponents/FileUpload";
import { FileManagerHeader } from "./subComponents/FileManagerHeader";
import { FileTable } from "./subComponents/FileTable";
import { Pagination } from "./subComponents/Pagination";
import { FileManagerFooter } from "./subComponents/FileManagerFooter";

export const FileManager = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [currentPage, setCurrentPage] = useState(1);
  const { data: files, isLoading, error } = useMyFiles();

  const { currentFiles, totalPages } = useFileProcessing(files, currentPage);

  const { selectedKeys, handleSelectionChange, isValidSelection, navigateToDNADashboard } = useFileSelection(currentFiles, currentPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
      <Disclaimer />
      <ImportantNoticeDropdown />
      <FileManagerHeader />

      <div className="w-full overflow-x-auto">
        <div className="sm:min-w-[768px]">
          <FileTable files={currentFiles} selectedKeys={selectedKeys} onSelectionChange={handleSelectionChange} isLoading={isLoading} error={error} />

          <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />

          {/* Continue button */}
          <div className="flex justify-end mt-4">
            <Button
              onPress={navigateToDNADashboard}
              size="sm"
              color={isValidSelection() ? "primary" : "default"}
              disabled={!isValidSelection()}
              className={!isValidSelection() ? "opacity-50 cursor-not-allowed" : ""}
            >
              Continue
            </Button>
          </div>
        </div>
      </div>

      <FileManagerFooter onUploadClick={onOpen} />

      <FileUploadModal
        isOpen={isOpen}
        onClose={onClose}
        onFileSelect={(file: File) => {
          console.log("Selected file:", file);
          // Add your file handling logic here
        }}
      />
    </div>
  );
};
