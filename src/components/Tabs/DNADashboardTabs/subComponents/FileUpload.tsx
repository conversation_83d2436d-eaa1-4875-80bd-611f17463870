import { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, Button, Radio, RadioGroup, Progress } from "@heroui/react";
import { FileHeart, Trash2, UploadIcon, AlertCircle } from "lucide-react";
import { useDnaFileUpload } from "@/hooks/dna-dashboard/use-dna-file-upload";
import { toast } from "react-hot-toast";

interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFileSelect: (file: File) => void;
}

const FileUploadModal = ({ isOpen, onClose, onFileSelect }: FileUploadModalProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileType, setFileType] = useState<"FASTQ" | "BAM" | "VCF">("FASTQ");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const isCancelledRef = useRef(false);

  const uploadMutation = useDnaFileUpload();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (100GB limit)
      const MAX_FILE_SIZE = 100 * 1024 * 1024 * 1024; // 100GB in bytes
      if (file.size > MAX_FILE_SIZE) {
        toast.error("File size exceeds 100GB limit");
        return;
      }

      setSelectedFile(file);
      onFileSelect(file);
      setUploadProgress(0);
      setIsUploading(false);
      isCancelledRef.current = false;
    }
  };

  const handleDeselectFile = () => {
    setSelectedFile(null);
    setUploadProgress(0);
    setIsUploading(false);
    isCancelledRef.current = false;
  };

  const handleCancelUpload = () => {
    isCancelledRef.current = true;
    uploadMutation.cancelUpload();
    setIsUploading(false);
    setUploadProgress(0);
    toast.error("Upload cancelled");
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);
      isCancelledRef.current = false;

      const result = await uploadMutation.mutateAsync({
        file: selectedFile,
        fileType: fileType,
        onProgress: (progress) => {
          setUploadProgress(progress);
        },
      });

      // Only show success if not cancelled
      if (!isCancelledRef.current) {
        toast.success("File uploaded successfully");
        onClose();
        setSelectedFile(null);
        setUploadProgress(0);
      }

      setIsUploading(false);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to upload file. Please try again.";

      // Don't show error toast if upload was cancelled
      if (!errorMessage.includes("cancelled") && !isCancelledRef.current) {
        toast.error(errorMessage);
      }

      setIsUploading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="dark:bg-slate-950" size="lg" backdrop="blur">
      <ModalContent>
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="space-y-2">
            <h2 className="text-sm font-semibold">Upload files</h2>
            <p className="text-xs text-gray-400">Select and upload the DNA files of your choice</p>
          </div>

          {/* File Type Selection */}
          <div className="space-y-2">
            <p className="text-xs">Choose the file type you are uploading:</p>
            <RadioGroup
              value={fileType}
              onValueChange={(value) => setFileType(value as "FASTQ" | "BAM" | "VCF")}
              orientation="horizontal"
              className="gap-4"
              isDisabled={isUploading}
            >
              <Radio
                value="FASTQ"
                classNames={{
                  label: "text-xs",
                  wrapper: "text-xs",
                }}
              >
                FASTQ Files
              </Radio>
              <Radio
                value="BAM"
                classNames={{
                  label: "text-xs",
                  wrapper: "text-xs",
                }}
              >
                BAM File
              </Radio>
              <Radio
                value="VCF"
                classNames={{
                  label: "text-xs",
                  wrapper: "text-xs",
                }}
              >
                VCF File
              </Radio>
            </RadioGroup>
          </div>

          {/* Upload Area */}
          <div className="dark:bg-slate-900 rounded-lg p-6">
            <div className="flex flex-col items-center justify-center w-full min-h-[200px] border-2 border-dashed border-gray-600 rounded-lg">
              {selectedFile ? (
                <div className="flex flex-col items-center justify-center w-full p-4 relative">
                  <div className="flex items-center justify-between w-full space-x-2 text-gray-400 hover:bg-slate-800 p-2 rounded-lg transition-colors duration-200">
                    <div className="w-full flex items-center space-x-2">
                      <FileHeart className="w-6 h-6" />
                      <div className="w-full">
                        <p className="text-xs font-medium">{selectedFile.name}</p>
                        <p className="text-xs">({(selectedFile.size / (1024 * 1024)).toFixed(2)} MB)</p>
                        {isUploading && (
                          <div className="mt-2 w-full">
                            <div className="flex items-center justify-between mb-2">
                              <Progress
                                value={uploadProgress}
                                className="flex-1 mr-3"
                                size="sm"
                                color="primary"
                                showValueLabel={true}
                                aria-label="Upload progress"
                              />
                              <Button size="sm" color="danger" variant="flat" onPress={handleCancelUpload} className="px-3 py-1 text-xs">
                                Cancel
                              </Button>
                            </div>
                            <div className="flex items-center gap-2 mt-2 p-2 bg-blue-500/10 rounded-lg border border-blue-500/20">
                              <AlertCircle className="w-4 h-4 text-blue-500" />
                              <p className="text-xs text-blue-500">Please don't refresh the page while the file is uploading</p>
                            </div>
                            <p className="text-xs mt-1 text-gray-400">Uploading file...</p>
                          </div>
                        )}
                      </div>
                    </div>
                    {!isUploading && (
                      <Trash2
                        className="w-4 h-4 cursor-pointer hover:text-red-500 transition-colors duration-200"
                        onClick={handleDeselectFile}
                        aria-label="Remove selected file"
                      />
                    )}
                  </div>
                </div>
              ) : (
                <label htmlFor="dropzone-file" className="flex flex-col items-center justify-center w-full h-full cursor-pointer">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <UploadIcon className="w-12 h-12 mb-4 text-gray-400" />
                    <p className="mb-2 text-sm text-gray-400">Choose files or drag & drop it here</p>
                    <p className="text-xs text-gray-500">Upload your Whole Genome Sequenced Files. Maximum file size: 100GB</p>
                  </div>
                  <input
                    id="dropzone-file"
                    type="file"
                    className="hidden"
                    accept={fileType === "FASTQ" ? ".fastq,.fq,.fq.gz" : `.${fileType.toLowerCase()}`}
                    onChange={handleFileChange}
                    disabled={isUploading}
                  />
                </label>
              )}
            </div>
          </div>

          {/* Important Note */}
          <div className="text-xs text-gray-400">
            <span className="font-semibold">Important Note:</span> When uploading FASTQ files (.fastq, .fq, or .fq.gz), please ensure you include both R1 and R2
            files to a pair for analysis.
          </div>

          {/* Upload Button */}
          <div className="flex justify-end">
            <Button size="sm" color="primary" className="bg-blue-600" isDisabled={!selectedFile || isUploading} isLoading={isUploading} onPress={handleUpload}>
              {isUploading ? "Uploading..." : "Upload"}
            </Button>
          </div>
        </div>
      </ModalContent>
    </Modal>
  );
};

export default FileUploadModal;
