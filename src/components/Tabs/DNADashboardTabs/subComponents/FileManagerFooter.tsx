import { Button } from "@heroui/button";
import { Plus } from "lucide-react";

interface FileManagerFooterProps {
  onUploadClick: () => void;
}

export const FileManagerFooter = ({ onUploadClick }: FileManagerFooterProps) => {
  return (
    <>
      <p className="text-justify text-[#fff2c0] text-xs font-medium p-2">
        Upload your Whole Genome Sequenced Files. Supported formats: FASTQ (.fastq, .fq, .fq.gz), BAM, VCF. Maximum size: 100GB
      </p>
      <div className="flex flex-col sm:flex-row gap-2 justify-between px-2">
        <span className="muted max-w-4xl">
          Please note that the typical genome sequenced file requires approximately 80GB to 100GB of storage, we therefore recommend you have at a minimum of
          100GB storage. You can purchase additional storage to continue uploading and for the storage & safekeeping of all your genome sequenced & related
          files seamlessly here.
        </span>
        <Button onPress={onUploadClick} size="sm" className="text-xs bg-blue-500 hover:bg-blue-600 text-white">
          <Plus className="w-4 h-4" />
          Upload
        </Button>
      </div>
    </>
  );
};
