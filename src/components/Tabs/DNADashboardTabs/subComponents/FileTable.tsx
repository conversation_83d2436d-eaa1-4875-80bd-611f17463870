import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";
import { FileTableProps } from "@/hooks/dna-dashboard/file-manager-types";
import { FileActions } from "./FileActions";

export const FileTable = ({ files, selectedKeys, onSelectionChange, isLoading, error }: FileTableProps) => {
  if (isLoading) {
    return <div className="text-center py-8 text-gray-500">Loading files...</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">Error loading files: {error.message}</div>;
  }

  return (
    <Table
      aria-label="DNA files table"
      selectionMode="multiple"
      selectedKeys={selectedKeys}
      onSelectionChange={onSelectionChange}
      classNames={{
        th: "dark:bg-slate-900",
        td: "py-2",
        tr: "hover:bg-gray-50 dark:hover:bg-gray-800/50",
      }}
      className="rounded-lg p-1 border dark:border-gray-800 overflow-x-scroll w-full"
      removeWrapper
    >
      <TableHeader>
        <TableColumn>
          <h2>File Name</h2>
        </TableColumn>
        <TableColumn>
          <h2>Date</h2>
        </TableColumn>
        <TableColumn className="hidden md:table-cell">
          <h2>Size</h2>
        </TableColumn>
        <TableColumn align="center">
          <h2>Actions</h2>
        </TableColumn>
      </TableHeader>

      <TableBody>
        {files.length > 0 ? (
          files.map((file) => (
            <TableRow key={file.id}>
              <TableCell className="font-medium">
                <div className="flex flex-col">
                  <h2 className="text-xs">{file.fileName}</h2>
                  <span className="text-gray-400 text-xs md:hidden">{file.date}</span>
                </div>
              </TableCell>
              <TableCell className="text-gray-400 text-xs hidden md:table-cell">
                <h2 className="text-xs">{file.date}</h2>
              </TableCell>
              <TableCell>
                <h2 className="text-xs">{file.size}</h2>
              </TableCell>
              <TableCell align="center">
                <FileActions file={file} />
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={4}>
              <div className="text-center py-4 text-gray-500">No files on this page</div>
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};
