"use client";

import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { <PERSON>, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, TooltipProps, XAxis, YAxis } from "recharts";
import { ChartAnalysisButton } from "./ChartAnalysisButton";

// Updated chart data for zygosity distribution
const chartData = [
  { name: "Heterozygous", count: 1.5, category: "Heterozygous (All)" },
  { name: "Homozygous", count: 2.0, category: "Homozygous Alt (All)" },
];

const chartConfig = {
  count: {
    label: "Count",
    color: "hsl(217, 91%, 60%)", // Blue color for heterozygous
    altColor: "hsl(25, 95%, 53%)", // Brown color for homozygous
  },
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-slate-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow-md text-xs">
        <p className="font-medium">{label}</p>
        <p style={{ color: payload[0].color }}>Count: {payload[0].value}</p>
      </div>
    );
  }
  return null;
};

export function BarSimple() {
  return (
    <Card className="p-2 bg-transparent border border-gray-200 dark:border-slate-900">
      <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
        <div className="flex justify-between items-center w-full">
          <h4 className="text-sm font-medium">Zygosity Distribution (All Variants)</h4>
          <ChartAnalysisButton chartData={chartData.map((item) => ({ name: item.name, value: item.count }))} />
        </div>
      </CardHeader>
      <CardBody className="overflow-visible py-2">
        <div className="p-4 bg-gray-50 dark:bg-slate-900 rounded-2xl">
          <div className="w-full h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 40 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" horizontal={true} vertical={false} />
                <XAxis dataKey="name" tickLine={false} axisLine={false} tick={{ fill: "#9CA3AF", fontSize: 10 }} height={60} interval={0} tickMargin={15} />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tick={{ fill: "#9CA3AF", fontSize: 10 }}
                  domain={[0, 2.5]}
                  label={{
                    value: "Count",
                    angle: -90,
                    position: "insideLeft",
                    style: { fill: "#9CA3AF", fontSize: 12 },
                  }}
                />
                <Tooltip content={<CustomTooltip />} cursor={false} />
                <Bar
                  dataKey="count"
                  radius={[4, 4, 0, 0]}
                  barSize={40}
                  shape={(props: any) => {
                    const { fill, x, y, width, height, name } = props;
                    return (
                      <rect
                        x={x}
                        y={y}
                        width={width}
                        height={height}
                        fill={name === "Heterozygous" ? chartConfig.count.color : chartConfig.count.altColor}
                        rx={4}
                        ry={4}
                      />
                    );
                  }}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center gap-6 mt-4">
            {chartData.map((item, index) => (
              <div key={item.name} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{
                    backgroundColor: index === 0 ? chartConfig.count.color : chartConfig.count.altColor,
                  }}
                />
                <span className="dark:text-gray-300 text-gray-900 text-xs">{item.category}</span>
              </div>
            ))}
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
