import { AnalysisIcon } from "@/components/icons";
import { Button } from "@heroui/button";
import React from "react";

interface StatItemProps {
  label: string;
  value: number;
}

const StatItem: React.FC<StatItemProps> = ({ label, value }) => (
  <div className="flex flex-col items-center border border-gray-200 dark:border-slate-900 justify-center p-4 dark:bg-slate-900 rounded-lg">
    <span className="text-md font-bold text-gray-900 dark:text-white">{value.toLocaleString()}</span>
    <span className="text-xs text-gray-400 mt-1">{label}</span>
  </div>
);

export const GSBASummary = () => {
  return (
    <div className="w-full mb-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4">
        <h2 className="text-sm font-medium text-gray-900 dark:text-white">Genome Sequencing Basic Analysis (GSBA) - Summary</h2>
        <Button
          size="sm"
          className="text-xs gap-1 text-orange-300 bg-[#7C389B] hover:bg-[#7C389B] dark:hover:bg-[#5f2c76] px-2 py-1 w-auto ml-auto"
          startContent={<AnalysisIcon />}
        >
          Enable AI Analysis
        </Button>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3 sm:gap-4">
        <StatItem label="Nonsynonymous SNV" value={9889} />
        <StatItem label="Startloss" value={23} />
        <StatItem label="Stopgain" value={94} />
        <StatItem label="Stoploss" value={10} />
        <StatItem label="Synonymous SNV" value={10745} />
        <StatItem label="Unknown" value={118} />
      </div>
    </div>
  );
};
