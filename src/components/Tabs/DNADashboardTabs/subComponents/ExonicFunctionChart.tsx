"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, CardHeader } from "@heroui/react";
import { useEffect, useState } from "react";
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, TooltipProps, XAxis, YAxis } from "recharts";
import { ChartAnalysisButton } from "./ChartAnalysisButton";

type DataPoint = {
  name: keyof typeof colors;
  value: number;
};

const chartData: DataPoint[] = [
  { name: "Nonsynonymous SNV", value: 9889 },
  { name: "Synonymous SNV", value: 10745 },
  { name: "Stoploss", value: 498 },
  { name: "Stopgain", value: 394 },
  { name: "Startloss", value: 983 },
];

const colors = {
  Unknown: "hsl(0, 0%, 55%)",
  "Synonymous SNV": "hsl(217, 91%, 60%)",
  Stoploss: "hsl(0, 0%, 30%)",
  Stopgain: "hsl(300, 45%, 55%)",
  Startloss: "hsl(0, 0%, 20%)",
  "Nonsynonymous SNV": "hsl(25, 95%, 53%)",
} as const;

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-slate-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow-md text-xs items-center justify-center">
        <p className="font-medium text-gray-900 dark:text-gray-100">{label}</p>
        <p className="text-gray-600 dark:text-gray-300">Count: {payload[0].value}</p>
      </div>
    );
  }
  return null;
};

export const ExonicFunctionChart = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <Card className="p-2 px-4 bg-transparent border border-gray-200 dark:border-slate-900">
      <CardHeader className="pb-0 pt-2 px-2 sm:px-4 flex-col items-start">
        <div className="flex justify-between items-center w-full">
          <h4 className="text-xs sm:text-sm font-medium">Exonic Function Breakdown</h4>
          <ChartAnalysisButton chartData={chartData.map((item) => ({ name: item.name, value: item.value }))} />
        </div>
      </CardHeader>
      <CardBody className="overflow-visible py-2 px-1 sm:px-2">
        <div className="p-1 sm:p-2 bg-gray-50 dark:bg-slate-900 rounded-2xl">
          <div className="h-[250px] sm:h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                layout="vertical"
                margin={{
                  top: 10,
                  right: isMobile ? 5 : 15,
                  left: isMobile ? 5 : 10,
                  bottom: 5,
                }}
              >
                <CartesianGrid horizontal={false} strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                <XAxis
                  type="number"
                  tickLine={false}
                  axisLine={false}
                  tick={{ fill: "#9CA3AF", fontSize: isMobile ? 8 : 10 }}
                  tickFormatter={(value) => (isMobile ? `${(value / 1000).toFixed(0)}k` : value.toString())}
                />
                <YAxis
                  type="category"
                  dataKey="name"
                  tickLine={false}
                  axisLine={false}
                  tick={{ fill: "#9CA3AF", fontSize: isMobile ? 7 : 9 }}
                  width={isMobile ? 60 : 90}
                  tickFormatter={(value) => {
                    if (isMobile) {
                      const abbreviations: Record<string, string> = {
                        "Nonsynonymous SNV": "Nonsyn",
                        "Synonymous SNV": "Syn",
                        Stoploss: "Stop-",
                        Stopgain: "Stop+",
                        Startloss: "Start-",
                      };
                      return abbreviations[value] || value;
                    }
                    return value;
                  }}
                />
                <Tooltip content={<CustomTooltip />} cursor={false} />
                <Bar
                  dataKey="value"
                  radius={[0, 4, 4, 0]}
                  fill="#6B8CD9"
                  shape={(props: any) => {
                    const { fill, x, y, width, height, name } = props;
                    return <rect x={x} y={y} width={width} height={height} fill={colors[name as keyof typeof colors]} rx={4} ry={4} />;
                  }}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};
