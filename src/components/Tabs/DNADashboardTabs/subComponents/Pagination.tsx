import { Button } from "@heroui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { PaginationProps } from "@/hooks/dna-dashboard/file-manager-types";

export const Pagination = ({ currentPage, totalPages, onPageChange }: PaginationProps) => {
  return (
    <div className="flex justify-center items-center gap-2 mt-4">
      <Button size="sm" variant="light" isIconOnly onPress={() => onPageChange(currentPage - 1)} disabled={currentPage === 1}>
        <ChevronLeft className="w-4 h-4" />
      </Button>

      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
        <Button
          key={page}
          size="sm"
          variant={currentPage === page ? "solid" : "light"}
          className={currentPage === page ? "bg-blue-500 text-white" : ""}
          onPress={() => onPageChange(page)}
        >
          {page}
        </Button>
      ))}

      <Button size="sm" variant="light" isIconOnly onPress={() => onPageChange(currentPage + 1)} disabled={currentPage === totalPages}>
        <ChevronRight className="w-4 h-4" />
      </Button>
    </div>
  );
};
