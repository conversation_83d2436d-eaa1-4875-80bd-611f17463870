"use client";

import { <PERSON>, CardBody, CardHeader } from "@heroui/react";
import { <PERSON>, Bar<PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, TooltipProps, XAxis, YAxis } from "recharts";
import { ChartAnalysisButton } from "./ChartAnalysisButton";

interface ZygosityData {
  name: string;
  value: number;
}

interface PredictiveBarChartProps {
  data?: ZygosityData[];
}

const defaultData: ZygosityData[] = [
  { name: "Heterozygous (Pathogenic)", value: 70 },
  { name: "Homozygous Alt (Pathogenic)", value: 25 },
];

const colors = {
  "Heterozygous (Pathogenic)": "hsl(217, 91%, 60%)",
  "Homozygous Alt (Pathogenic)": "hsl(25, 95%, 53%)",
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-slate-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow-md text-xs items-center justify-center">
        <p className="font-medium text-gray-900 dark:text-gray-100">{label}</p>
        <p className="text-gray-600 dark:text-gray-300">Count: {payload[0].value}</p>
      </div>
    );
  }
  return null;
};

export function ZygosityDistributionChart({ data = defaultData }: PredictiveBarChartProps) {
  return (
    <Card className="p-2 bg-transparent border border-gray-200 dark:border-slate-900">
      <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
        <div className="flex justify-between items-center w-full">
          <h4 className="text-sm font-medium">Zygosity Distribution (Pathogenic Variants)</h4>
          <ChartAnalysisButton chartData={data.map((item) => ({ name: item.name, value: item.value }))} />
        </div>
      </CardHeader>
      <CardBody className="overflow-visible py-2">
        <div className="p-4 bg-gray-50 dark:bg-slate-900 rounded-2xl">
          <div className="w-full h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 40 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" horizontal={true} vertical={false} />
                <XAxis dataKey="name" tickLine={false} axisLine={false} tick={{ fill: "#9CA3AF", fontSize: 10 }} height={60} interval={0} tickMargin={15} />
                <YAxis tickLine={false} axisLine={false} tick={{ fill: "#9CA3AF", fontSize: 10 }} domain={[0, 80]} />
                <Tooltip content={<CustomTooltip />} cursor={false} />
                <Bar
                  dataKey="value"
                  radius={[4, 4, 0, 0]}
                  barSize={40}
                  shape={(props: any) => {
                    const { fill, x, y, width, height, name } = props;
                    return <rect x={x} y={y} width={width} height={height} fill={colors[name as keyof typeof colors]} rx={4} ry={4} />;
                  }}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center gap-6 mt-4">
            {Object.entries(colors).map(([name, color]) => (
              <div key={name} className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: color }} />
                <span className="text-xs text-gray-400">{name}</span>
              </div>
            ))}
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
