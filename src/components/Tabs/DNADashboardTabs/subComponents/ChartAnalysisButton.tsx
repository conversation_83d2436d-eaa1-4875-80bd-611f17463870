"use client";

import { AnalysisIcon } from "@/components/icons";
import { useChartDataStore } from "@/store/chart-data";
import { But<PERSON> } from "@heroui/react";

interface ChartAnalysisButtonProps {
  chartData: Array<{ name: string; value: number }>;
  className?: string;
}

export const ChartAnalysisButton = ({ chartData, className = "" }: ChartAnalysisButtonProps) => {
  const { openAiChat, chartDataStore } = useChartDataStore();

  return (
    <Button isIconOnly variant="light" size="sm" isDisabled={chartDataStore.length > 0} onPress={() => openAiChat(chartData)} className={className}>
      <AnalysisIcon className="w-5 h-5 sm:w-6 sm:h-6 text-gray-500 dark:text-gray-900 dark:bg-gray-900 rounded-full p-1 cursor-pointer" />
    </Button>
  );
};
