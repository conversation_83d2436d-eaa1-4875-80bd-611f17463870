"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, CardHeader } from "@heroui/card";
import { <PERSON>, <PERSON><PERSON><PERSON>, Tooltip, TooltipProps } from "recharts";
import { ChartAnalysisButton } from "./ChartAnalysisButton";

const chartData = [
  {
    browser: "Conflicting Classifications of Pathogenicity",
    value: 98,
    fill: "#4666FF",
  },
  { browser: "Other classifications", value: 2, fill: "#FF8042" },
];

const chartConfig = {
  edge: {
    label: "Conflicting Classifications of Pathogenicity",
    color: "#4666FF",
  },
  other: {
    label: "Others",
    color: "#FF8042",
  },
};

// Custom tooltip component
const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-slate-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow-md text-xs">
        {payload.map((entry, index) => (
          <p key={index} style={{ color: entry.color }}>
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export function ClinicalSignificanceChart() {
  return (
    <Card className="p-2 bg-transparent border border-gray-200 dark:border-slate-900">
      <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
        <div className="flex justify-between items-center w-full">
          <h4 className="text-sm font-medium">Clinical Significance Breakdown</h4>
          <ChartAnalysisButton chartData={chartData.map((item) => ({ name: item.browser, value: item.value }))} />
        </div>
      </CardHeader>
      <CardBody className="overflow-hidden py-2">
        <div className="grid grid-cols-1 md:grid-cols-2 items-start gap-4 dark:bg-slate-900 rounded-2xl p-3">
          <div className="space-y-4 flex justify-center items-center flex-col">
            <p className="text-xs">
              The donut chart is for visualization purposes of the users, there are other clinical significance variants present for analysis but, their
              presence is close to negligible in the sample for now.
            </p>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[#4666FF]" />
                <span className="dark:text-gray-300 text-gray-900 text-xs text-wrap w-full">Conflicting Classifications of Pathogenicity</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[#FF8042]" />
                <span className="dark:text-gray-300 text-gray-900 text-xs">Others</span>
              </div>
            </div>
          </div>
          <div className="flex-1 flex justify-center items-center">
            <div className="max-h-[250px] w-full flex flex-col justify-center items-center">
              <PieChart width={250} height={200}>
                <Tooltip content={<CustomTooltip />} />
                <Pie data={chartData} dataKey="value" nameKey="browser" innerRadius={40} outerRadius={80} strokeWidth={0} label={false} labelLine={false} />
              </PieChart>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
