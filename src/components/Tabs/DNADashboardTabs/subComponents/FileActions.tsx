import { But<PERSON> } from "@heroui/button";
import { ArrowDownToLine, Mail, Trash2 } from "lucide-react";
import { MdAddToDrive } from "react-icons/md";
import { FileActionsProps } from "@/hooks/dna-dashboard/file-manager-types";

export const FileActions = ({ file }: FileActionsProps) => {
  return (
    <div className="space-x-2 flex justify-center">
      <Button
        as="a"
        href={file.uploaded_file?.file_url}
        target="_blank"
        rel="noopener noreferrer"
        size="sm"
        variant="light"
        className="!bg-transparent !p-0 !min-w-0 !h-auto"
        title="Download"
        isDisabled={!file.uploaded_file?.file_url}
      >
        <ArrowDownToLine className="w-4 h-4" />
      </Button>

      <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent" title="Email">
        <Mail className="w-4 h-4" />
      </Button>

      <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent" title="Add to Drive">
        <MdAddToDrive className="w-4 h-4" />
      </Button>

      <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent" title="Delete">
        <Trash2 className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );
};
