import ServiceCard from "@/components/Card/ServiceCard";
import { useFileValidation } from "@/hooks/dna-dashboard/useFileValidation";

interface ServiceGridProps {
  services: Service[] | undefined;
  selectedFiles: Array<{ id: string | number; fileName: string }>;
  selectedServiceIds: string[];
  onServiceClick: (serviceId: string, title: string) => void;
}

export const ServiceGrid = ({ services, selectedFiles, selectedServiceIds, onServiceClick }: ServiceGridProps) => {
  const { hasValidFiles, hasVcfFile, shouldLockBasicAnalysis } = useFileValidation(selectedFiles);

  return (
    <div className="block sm:grid sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6" aria-label="Service selection grid">
      {services?.map((service: Service, index: number) => {
        const isBasicAnalysisService = index === 0;
        const isLocked = isBasicAnalysisService && shouldLockBasicAnalysis;

        return (
          <ServiceCard
            key={service.id}
            serviceInfo={{
              title: service.name,
              benefits: service.features,
              button_text: isLocked && selectedServiceIds.includes(service.id) ? "Required" : "Select",
            }}
            priceInfo={{
              price: Number(service.price),
              discounted_price: Number(service.discounted_price),
              discount_description:
                service.active_promotions?.find((promotion: PromotionFormValues) => promotion.promotion_type === "ONLINE")?.description || null,
              priceUnit: "Analysis",
            }}
            status={{
              isDisabled: selectedFiles.length === 0 || !hasValidFiles || (index === 0 && hasVcfFile) || (isLocked && selectedServiceIds.includes(service.id)),
              isSelected: selectedFiles.length > 0 && selectedServiceIds.includes(service.id),
            }}
            onClick={() => onServiceClick(service.id, service.name)}
          />
        );
      })}
    </div>
  );
};
