import Disclaimer from "@/components/Disclaimer";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import { CancerVariantModal } from "@/components/Modals/CancerVariantModal";
import { PromoCodeModal } from "@/components/Modals/PromoCodeModal";
import { useDnaServicesState } from "@/hooks/dna-dashboard/useDnaServicesState";
import { useModalHandlers } from "@/hooks/dna-dashboard/useModalHandlers";
import { useServiceClickHandler } from "@/hooks/dna-dashboard/useServiceClickHandler";
import { useServiceSelection } from "@/hooks/dna-dashboard/useServiceSelection";
import { useServices } from "@/hooks/useServices";
import { Button } from "@heroui/react";
import { useEffect } from "react";
import { ServiceGrid } from "./subComponents/ServiceGrid";

// Helper function to check if a file is a FASTQ file
const isFastqFile = (fileName: string): boolean => {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith(".fastq") || lowerName.endsWith(".fq") || lowerName.endsWith(".fq.gz");
};

export const DnaServices = () => {
  const { data: services } = useServices();

  const { selectedFiles, isPromoModalOpen, setIsPromoModalOpen, isCancerModalOpen, setIsCancerModalOpen } = useDnaServicesState(services);

  const { selectedServiceIds, cancerServiceDetails, handleServiceSelection, setCancerServiceDetails, setSelectedServiceIds } = useServiceSelection(
    services,
    selectedFiles
  );

  const { handleCancerModalConfirm, handleCancerModalClose, handleContinue, handlePromoCodeConfirm, isCheckoutLoading } = useModalHandlers(
    selectedServiceIds,
    cancerServiceDetails,
    setCancerServiceDetails,
    setSelectedServiceIds,
    setIsCancerModalOpen,
    setIsPromoModalOpen,
    services
  );

  const { handleServiceClick } = useServiceClickHandler(
    services,
    selectedFiles,
    selectedServiceIds,
    handleServiceSelection,
    setCancerServiceDetails,
    setIsCancerModalOpen
  );

  // Auto-select GSBA if 2+ FASTQ files are present or if a BAM file is present
  useEffect(() => {
    if (selectedFiles.length > 0 && services && services.length > 0) {
      const fastqFiles = selectedFiles.filter((file) => isFastqFile(file.fileName));
      const bamFiles = selectedFiles.filter((file) => file.fileName.endsWith(".bam"));

      if (fastqFiles.length >= 2 || bamFiles.length > 0) {
        const gsbaService = services[0];
        setSelectedServiceIds([gsbaService.id]);
      }
    }
  }, [selectedFiles, services, setSelectedServiceIds]);

  return (
    <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
      <Disclaimer />
      <ImportantNoticeDropdown />

      <ServiceGrid services={services} selectedFiles={selectedFiles} selectedServiceIds={selectedServiceIds} onServiceClick={handleServiceClick} />

      {selectedServiceIds.length > 0 && (
        <div className="md:col-span-3 flex justify-end mt-4">
          <Button onPress={handleContinue} size="sm" color="primary" isLoading={isCheckoutLoading}>
            Continue
          </Button>
        </div>
      )}

      {!sessionStorage.getItem("selectedDNAFiles") && (
        <div className="p-4 col-span-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <p className="text-xs text-blue-600 dark:text-blue-400 font-medium text-center">
            Important: You must select at least one file from the File Manager before accessing these services. Please return to the File Manager tab to make
            your selection.
          </p>
        </div>
      )}

      <CancerVariantModal isOpen={isCancerModalOpen} onClose={handleCancerModalClose} onConfirm={handleCancerModalConfirm} />

      <PromoCodeModal
        isOpen={isPromoModalOpen}
        onClose={() => setIsPromoModalOpen(false)}
        onConfirm={handlePromoCodeConfirm}
        selectedServices={selectedServiceIds.map((id) => {
          const service = services?.find((s) => s.id === id);
          return {
            id,
            name: service?.name || "",
            ...(id === cancerServiceDetails.serviceId && {
              genes: cancerServiceDetails.genes,
              depth: cancerServiceDetails.depth,
            }),
          };
        })}
      />
    </div>
  );
};
