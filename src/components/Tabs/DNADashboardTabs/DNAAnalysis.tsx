"use client";

import AIChat from "@/components/AIChat";
import Disclaimer from "@/components/Disclaimer";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import Tabs from "@/components/Tabs/TabComponent";
import { predictiveData } from "@/config/constants";
import { useChartDataStore } from "@/store/chart-data";
import { Card, CardBody } from "@heroui/card";
import { ClinicalSignificanceChart } from "./subComponents/ClinicalSignificanceChart";
import { ExonicFunctionChart } from "./subComponents/ExonicFunctionChart";
import { GSBASummary } from "./subComponents/GSBASummary";
import { BarSimple } from "./subComponents/ZygosityDistributionAllChart";
import { ZygosityDistributionChart } from "./subComponents/ZygosityDistributionPathogenicChart";

export const DNAAnalysis = () => {
  const { closeAiChat, aiChatOpen, chartDataStore } = useChartDataStore();

  const tabs = [
    {
      key: "file1",
      title: "R.A.V.I.D. Generated_36328.json",
      content: (
        <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
          <GSBASummary />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ExonicFunctionChart />

            <ClinicalSignificanceChart />

            <ZygosityDistributionChart data={predictiveData} />

            <BarSimple />
          </div>
        </div>
      ),
    },
    {
      key: "file2",
      title: "R.A.V.I.D. Generated_234957.json",
      content: (
        <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
          <GSBASummary />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ExonicFunctionChart />

            <ClinicalSignificanceChart />

            <ZygosityDistributionChart data={predictiveData} />

            <BarSimple />
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
      <Disclaimer />
      <ImportantNoticeDropdown />

      <div className="pl-2">
        <h1 className="text-xs font-bold">My DNA Insights Dashboard</h1>
        <p className="text-xs text-foreground-500">Your personal genomics data exploration tool</p>
      </div>

      <Tabs tabs={tabs} />

      <Card className="mt-8 p-2 bg-gray-50 dark:bg-slate-900">
        <CardBody className="px-4 py-3">
          <p className="text-xs leading-relaxed text-gray-700 dark:text-gray-400">
            <span className="font-semibold">Disclaimer:</span> Please note that all the information displayed here is subject to computational and algorithimic
            errors. There is also a strong probability of false positives. It is highly recommended that you understand the values and information displayed
            with your trusted doctor/ care provider and your legal guardian if under the legal age as established in your country of residence and/ or
            citizenship. By using this information you assume all liability of disclosing your confidential information.
          </p>
        </CardBody>
      </Card>

      <AIChat isOpen={aiChatOpen} onClose={() => closeAiChat()} chartData={chartDataStore} type="dna" />
    </div>
  );
};
