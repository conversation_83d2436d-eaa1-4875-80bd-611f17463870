import Disclaimer from "@/components/Disclaimer";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import { Button, Checkbox, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";
import { ArrowDownToLine, FolderDown, Mail, MailOpen, Trash, Trash2 } from "lucide-react";
import { MdAddToDrive } from "react-icons/md";

interface CollaborationMessage {
  id: string;
  sender: string;
  subject: string;
  badge: string;
}

const collaborationData: CollaborationMessage[] = [
  {
    id: "1",
    sender: "Dana-Farber Cancer Institute",
    subject: "Call for Review for Exploratory Analysis",
    badge: "new",
  },
  {
    id: "2",
    sender: "NYU Langone Genome Technology Center",
    subject: "Offer for Review for Collaborative Research",
    badge: "new",
  },
  {
    id: "3",
    sender: "National Human Genome Research Institute",
    subject: "Public Policy Information",

    badge: "old",
  },
];

export const Collaborations = () => {
  return (
    <div className="space-y-4 bg-white dark:bg-slate-950 px-2">
      <Disclaimer />
      <ImportantNoticeDropdown />
      <div className="my-3 p-2 border-b border-gray-200 dark:border-gray-800">
        <h2>Inbox Messages For Collaborations</h2>
      </div>
      <div className="flex flex-col items-center max-w-7xl mx-auto gap-2">
        <MailCard />
      </div>
    </div>
  );
};

const MailCard = () => {
  return (
    <div className="w-full overflow-x-auto">
      <div className="min-w-[768px]">
        <Table
          aria-label="Collaborations Table"
          removeWrapper
          className="rounded-lg p-1 border dark:border-gray-800 w-full"
          classNames={{
            th: "dark:bg-slate-900",
            td: "py-2",
            tr: "hover:bg-gray-50 dark:hover:bg-gray-800/50",
          }}
          selectionMode="multiple"
          disableAnimation={true}
          isStriped
        >
          <TableHeader>
            <TableColumn>
              <h2 className="font-bold">Sender</h2>
            </TableColumn>
            <TableColumn>
              <h2 className="font-bold">Subject</h2>
            </TableColumn>
            <TableColumn align="start">
              <h2 className="font-bold">Actions</h2>
            </TableColumn>
          </TableHeader>
          <TableBody>
            {collaborationData.map((message) => (
              <TableRow key={message.id}>
                <TableCell>
                  <h2>{message.sender}</h2>
                </TableCell>
                <TableCell className="flex items-center gap-2">
                  {message.badge === "new" && <span className="text-xs text-black bg-yellow-400 px-1 rounded-lg">NEW</span>}
                  <h2>{message.subject}</h2>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1 sm:gap-2">
                    <Button
                      as="a"
                      target="_blank"
                      rel="noopener noreferrer"
                      size="sm"
                      variant="light"
                      className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start"
                      title="Download"
                    >
                      <ArrowDownToLine className="w-4 h-4" />
                    </Button>

                    <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                      <Mail className="w-4 h-4" />
                    </Button>

                    <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                      <MdAddToDrive className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="light" className="!p-0 !min-w-0 !h-auto !bg-transparent !justify-start">
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </Button>
                    <Button
                      size="sm"
                      variant="light"
                      className="!p-1 !min-w-0 !h-auto ml-10 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700"
                    >
                      <svg className="w-4 h-4" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M8.69544 4.49728C7.65841 3.45987 6.94861 1.6483 6.59636 0C6.24356 1.64864 5.53451 3.46059 4.49711 4.49799C3.46008 5.53468 1.64796 6.24448 0 6.59745C1.64864 6.9497 3.46042 7.65908 4.49745 8.69653C5.53447 9.73355 6.24427 11.5455 6.59707 13.1938C6.94932 11.5452 7.65892 9.73322 8.69577 8.69581C9.7328 7.65879 11.5449 6.94932 13.1929 6.59636C11.5446 6.2441 9.73284 5.53434 8.69544 4.49728Z"
                          fill="url(#paint0_linear_1022_31082)"
                        />
                        <path
                          d="M15.4311 12.8039C14.8121 12.1846 14.3873 11.1018 14.1769 10.1172C13.9661 11.1018 13.5428 12.1846 12.9231 12.8043C12.3034 13.4233 11.2208 13.8477 10.2363 14.0585C11.2211 14.2692 12.3034 14.6929 12.9231 15.3126C13.5428 15.932 13.9668 17.0148 14.1776 17.9994C14.388 17.0144 14.8121 15.932 15.4314 15.3123C16.0507 14.6929 17.1335 14.2689 18.1178 14.0578C17.1336 13.8473 16.0508 13.4233 15.4311 12.8039Z"
                          fill="url(#paint1_linear_1022_31082)"
                        />
                        <defs>
                          <linearGradient id="paint0_linear_1022_31082" x1="6.59644" y1="0" x2="6.59644" y2="13.1938" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#7D5191" />
                            <stop offset="1" stopColor="#FFD52B" />
                          </linearGradient>
                          <linearGradient id="paint1_linear_1022_31082" x1="14.1771" y1="10.1172" x2="14.1771" y2="17.9994" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#7D5191" />
                            <stop offset="1" stopColor="#FFD52B" />
                          </linearGradient>
                        </defs>
                      </svg>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};