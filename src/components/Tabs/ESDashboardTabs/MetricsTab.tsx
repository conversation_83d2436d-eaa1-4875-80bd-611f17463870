"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";
import {
  CustomBarTooltip,
  DonutChart,
  MobileHealthDonut,
  SimpleAreaChart,
  StorageAreaChart,
  monthlyUsageData,
  userMetrics,
  storageData,
  prescriptionData,
  StoragePieChart,
  PrescriptionBarChart,
} from "./subComponents/charts";

import { Card, CardBody, CardHeader } from "@heroui/card";
import { Progress } from "@heroui/progress";

export const MetricsTab = () => {
  return (
    <div className="p-2 space-y-4">
      {/* Top Row - 2 Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* User Licenses Card */}
        <Card className="bg-card border dark:border-slate-800">
          <CardHeader className="text-sm">
            <h1>User Licenses</h1>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-3 gap-4">
              {userMetrics.map((metric, index) => (
                <div
                  key={index}
                  className="bg-[#173961] rounded-lg p-4 h-[120px] flex flex-col justify-between bg-no-repeat bg-right bg-[url('/images/metric_mask.png')] bg-cover bg-[rgba(37, 99, 235, 0.2)] text-white"
                >
                  <div className="text-white text-2xl sm:text-3xl font-bold">{metric.value}</div>
                  <div className="text-xs h-[30px] max-w-[70px] leading-4">{metric.label}</div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* AI Analysis Usage - DNA Analysis Card */}
        <Card className="bg-card border dark:border-slate-800">
          <CardHeader className="text-sm">
            <h1>AI Analysis Usage - DNA Analysis</h1>
          </CardHeader>
          <CardBody>
            <div className="flex flex-col md:flex-row justify-between gap-8 md:gap-4">
              {/* Daily Usage Column */}
              <div className="flex-1 space-y-2 md:border-r-2 border-border pr-2">
                <h1>Daily Usage</h1>
                <div className="flex flex-col gap-4 items-center">
                  <div className="h-[150px] w-[150px]">
                    <DonutChart />
                  </div>
                  <div className="flex gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 rounded-sm bg-[#173961]"></div>
                      <span className="text-muted-foreground text-xs whitespace-nowrap">Used Tokens</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 rounded-sm bg-[#B1B4BF]"></div>
                      <span className="text-muted-foreground text-xs whitespace-nowrap">Remaining Tokens</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Monthly Usage Column */}
              <div className="flex-1 space-y-2 flex flex-col justify-between min-h-[200px]">
                <h1>Monthly Usage</h1>
                <div className="h-[150px]">
                  <SimpleAreaChart data={monthlyUsageData} />
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Middle Row - Data Storage Trends */}
      <Card className="bg-card border dark:border-slate-800">
        <CardHeader>
          <h1>Data Storage Trends</h1>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Total Storage Card */}
            <Card className="bg-card shadow-lg md:border-r-2 border-border rounded-none">
              <CardHeader>
                <h1>Total Storage</h1>
              </CardHeader>
              <CardBody>
                <div className="w-full min-h-[200px] flex flex-col md:flex-row items-center justify-between gap-4">
                  <div className="h-[150px] w-[150px]">
                    <StoragePieChart />
                  </div>
                  <div className="flex flex-col space-y-4">
                    <h1 className="text-2xl md:text-4xl font-bold break-words">
                      367 <span className="text-xs md:text-sm text-muted-foreground whitespace-normal">/1000 GB Used Over Time</span>
                    </h1>
                    <div className="flex flex-row sm:flex-col gap-4 text-xs">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-sm bg-[#173961] flex-shrink-0"></div>
                        <span className="text-muted-foreground">Storage in Use</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-sm bg-[#B1B4BF] flex-shrink-0"></div>
                        <span className="text-muted-foreground">Storage Remaining</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Average Storage Usage Card */}
            <Card className="bg-card shadow-lg">
              <CardHeader>
                <h1>Average Storage Usage (Monthly)</h1>
              </CardHeader>
              <CardBody>
                <div className="h-[200px]">
                  <StorageAreaChart />
                </div>
              </CardBody>
            </Card>
          </div>
        </CardBody>
      </Card>
     

      {/* Bottom Row - 3 cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* AI Analysis Usage - Diagnosis */}
        <Card className="bg-card border dark:border-slate-800">
          <CardHeader className="text-sm">
            <h1>AI Analysis Usage - Diagnosis</h1>
          </CardHeader>
          <CardBody className="space-y-4">
            <p>No. of Files Analyzed:</p>
            <div className="text-2xl font-bold">
              27 <span className="text-xs text-muted-foreground">Files</span>
            </div>
            <div>
              <p className="mb-2">Daily Token Usage</p>
              <Progress
                value={82}
                className="h-2"
                classNames={{
                  indicator: "bg-[#173961]",
                  track: "bg-[#B1B4BF]",
                }}
              />
            </div>
            <p>
              Token Limit Renews on:
              <span className="text-white"> After Mid-night</span>
            </p>
          </CardBody>
        </Card>

        {/* AI Analysis Usage - Mobile Health */}
        <Card className="bg-card border dark:border-slate-800">
          <CardHeader>
            <h1>AI Analysis Usage - Mobile Health</h1>
          </CardHeader>
          <CardBody>
            <div className="flex md:flex-col lg:flex-row items-center gap-8">
              <div className="h-[150px] w-[150px]">
                <MobileHealthDonut />
              </div>
              <div className="space-y-4">
                <div>
                  <p>Wearable Data Analyzed:</p>
                  <div className="flex items-center">
                    <span className="text-2xl font-bold mr-2">7</span>
                    <p>Data Points</p>
                  </div>
                </div>
                <div className="flex flex-col gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-sm bg-[#173961]"></div>
                    <span className="text-muted-foreground">Used Tokens</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-sm bg-[#B1B4BF]"></div>
                    <span className="text-muted-foreground">Remaining Tokens</span>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* AI Analysis Usage - Prescriptions */}
        <Card className="bg-card border dark:border-slate-800">
          <CardHeader>
            <h1>
              AI Analysis Usage - Prescriptions{" "}
            <p className="mt-2 text-muted-foreground">Daily token distribution</p>
            </h1>
          </CardHeader>
          <CardBody>
            <div className="h-[200px] flex flex-col">
              <PrescriptionBarChart />
              <div className="flex justify-center gap-6 text-xs mt-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-sm bg-[#173961]"></div>
                  <span className="text-muted-foreground">Used Tokens</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-sm bg-[#B1B4BF]"></div>
                  <span className="text-muted-foreground">Remaining Tokens</span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default MetricsTab;
