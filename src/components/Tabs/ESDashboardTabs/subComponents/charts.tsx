"use client";

import { ArrowUp } from "lucide-react";
import { PieChart, Pie, Cell, AreaChart, Area, ResponsiveContainer, Tooltip, XAxis, BarChart, Bar, YAxis, CartesianGrid } from "recharts";
import { Card, CardBody, Tooltip as NextTooltip } from "@heroui/react";

// Data Arrays
export const userMetrics = [
  { value: "38", label: "Total Users" },
  { value: "38%", label: "License Utilization" },
  { value: "12/38", label: "User Login per Day" },
];

export const monthlyUsageData = [
  { month: "Jan", value: 80 },
  { month: "Feb", value: 95 },
  { month: "Mar", value: 85 },
  { month: "Apr", value: 100 },
  { month: "May", value: 90 },
  { month: "Jun", value: 120 },
  { month: "Jul", value: 110 },
];

export const dailyUsageData = [
  { name: "Used", value: 74 },
  { name: "Remaining", value: 26 },
];

export const mobileHealthData = [
  { name: "Used", value: 68 },
  { name: "Remaining", value: 32 },
];

export const COLORS = ["#173961", "#B1B4BF"];

export const monthlyStorageData = [
  { month: "JAN", value: 250 },
  { month: "FEB", value: 280 },
  { month: "MAR", value: 251 },
  { month: "APR", value: 450 },
  { month: "MAY", value: 900 },
];

export const storageData = [
  { name: "Storage in Use", value: 36.7 },
  { name: "Storage Remaining", value: 63.3 },
];

// Types for chart data
interface ChartDataEntry {
  name?: string;
  value: number;
  month?: string;
}

// Updated Custom Tooltips using NextUI Card
export const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <Card className="border-none bg-slate-900/80">
        <CardBody className="p-2">
          <p className="text-xs font-medium text-white">{label}</p>
          <p className="text-xs text-blue-400">{`${payload[0].value}/150 Used`}</p>
        </CardBody>
      </Card>
    );
  }
  return null;
};

export const CustomPieTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <Card className="border-none bg-slate-900/80">
        <CardBody className="p-2">
          <p className="text-xs font-medium text-white">{`${payload[0].value}%`}</p>
        </CardBody>
      </Card>
    );
  }
  return null;
};

export const CustomBarTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <Card className="border-none bg-slate-900/80">
        <CardBody className="p-2">
          <p className="text-xs text-blue-400">{`${payload[0].value + payload[1].value}/60`}</p>
        </CardBody>
      </Card>
    );
  }
  return null;
};

export const CustomStorageTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <Card className="border-none bg-slate-900/80">
        <CardBody className="p-2">
          <p className="text-xs font-medium text-white">
            {`${payload[0].value} GB`}
            <ArrowUp className="w-4 h-4 inline-block text-green-500" />
          </p>
        </CardBody>
      </Card>
    );
  }
  return null;
};

export const DonutChart = () => {
  return (
    <Card className="bg-transparent shadow-none">
      <CardBody className="p-0">
        <div className="relative w-32 h-30">
          <PieChart width={128} height={130}>
            <Pie
              data={dailyUsageData}
              cx={64}
              cy={64}
              innerRadius={30}
              outerRadius={58}
              fill="#173961"
              paddingAngle={0}
              dataKey="value"
              strokeWidth={0}
              nameKey="name"
            >
              {dailyUsageData.map((entry: ChartDataEntry, index: number) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} stroke="none" />
              ))}
            </Pie>
            <Tooltip content={<CustomPieTooltip />} cursor={false} />
          </PieChart>
        </div>
      </CardBody>
    </Card>
  );
};

export const SimpleAreaChart = ({ data }: { data: ChartDataEntry[] }) => (
  <Card className="bg-transparent shadow-none w-full">
    <CardBody className="p-0">
      <ResponsiveContainer width="100%" height={120}>
        <AreaChart data={data} margin={{ top: 5, right: 10, left: 0, bottom: 0 }}>
          <defs>
            <linearGradient id="colorUsage" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#173961" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#173961" stopOpacity={0} />
            </linearGradient>
          </defs>
          <XAxis dataKey="month" tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
          <Tooltip content={<CustomTooltip />} cursor={{ stroke: "#666", strokeWidth: 1 }} />
          <Area type="monotone" dataKey="value" stroke="#173961" fillOpacity={1} fill="url(#colorUsage)" />
        </AreaChart>
      </ResponsiveContainer>
    </CardBody>
  </Card>
);

export const MobileHealthDonut = () => {
  return (
    <Card className="bg-transparent shadow-none">
      <CardBody className="p-0">
        <div className="relative w-32 h-30">
          <PieChart width={128} height={130}>
            <Pie
              data={mobileHealthData}
              cx={64}
              cy={64}
              innerRadius={30}
              outerRadius={58}
              fill="#173961"
              paddingAngle={0}
              dataKey="value"
              strokeWidth={0}
              nameKey="name"
            >
              {mobileHealthData.map((entry: ChartDataEntry, index: number) => (
                <Cell key={`cell-${index}`} fill={index === 0 ? "#173961" : "#B1B4BF"} stroke="none" />
              ))}
            </Pie>
            <Tooltip content={<CustomPieTooltip />} cursor={false} />
          </PieChart>
        </div>
      </CardBody>
    </Card>
  );
};

export const StorageAreaChart = () => (
  <Card className="bg-transparent shadow-none">
    <CardBody className="p-0">
      <div className="relative h-[200px]">
        <NextTooltip content="AI Analysis Launched" placement="top">
          <div className="absolute left-[58%] bottom-[15%] flex flex-col items-center z-10">
            <p className="text-[8px] text-white mt-2 whitespace-nowrap">AI Analysis Launched</p>
          </div>
        </NextTooltip>
        <ResponsiveContainer width="100%" height={200}>
          <AreaChart data={monthlyStorageData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="colorStorage" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#173961" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#173961" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="0" stroke="#2A3441" strokeOpacity={0.4} />
            <XAxis dataKey="month" tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} domain={[0, 1000]} ticks={[100, 250, 500, 750, 1000]} />
            <Tooltip content={<CustomStorageTooltip />} cursor={{ stroke: "#666", strokeWidth: 1 }} />
            <Area type="monotone" dataKey="value" stroke="#173961" fillOpacity={1} fill="url(#colorStorage)" />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </CardBody>
  </Card>
);

export const prescriptionData = [
  { day: "Mon", usedTokens: 30, remainingTokens: 20 },
  { day: "Tue", usedTokens: 35, remainingTokens: 25 },
  { day: "Wed", usedTokens: 25, remainingTokens: 20 },
  { day: "Thu", usedTokens: 20, remainingTokens: 15 },
  { day: "Fri", usedTokens: 15, remainingTokens: 10 },
  { day: "Sat", usedTokens: 20, remainingTokens: 10 },
  { day: "Sun", usedTokens: 10, remainingTokens: 15 },
];

export const StoragePieChart = () => {
  return (
    <Card className="bg-transparent shadow-none">
      <CardBody className="p-0">
        <div className="flex-shrink-0 flex justify-center">
          <PieChart width={160} height={160}>
            <Pie
              data={storageData}
              cx="50%"
              cy="50%"
              innerRadius={0}
              outerRadius={65}
              fill="#173961"
              paddingAngle={0}
              dataKey="value"
              strokeWidth={0}
              nameKey="name"
              startAngle={90}
              endAngle={-270}
            >
              {storageData.map((entry: ChartDataEntry, index: number) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} stroke="none" />
              ))}
            </Pie>
            <Tooltip content={<CustomPieTooltip />} cursor={false} />
          </PieChart>
        </div>
      </CardBody>
    </Card>
  );
};

export const PrescriptionBarChart = () => (
  <Card className="bg-transparent shadow-none">
    <CardBody className="p-0">
      <ResponsiveContainer width="100%" height={160}>
        <BarChart data={prescriptionData} margin={{ top: 10, right: 0, left: -20, bottom: 0 }} barSize={20}>
          <XAxis dataKey="day" tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
          <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} tickFormatter={(value) => `${value}k`} />
          <Tooltip content={<CustomBarTooltip />} cursor={{ fill: "transparent" }} />
          <CartesianGrid strokeDasharray="0" stroke="#2A3441" strokeOpacity={0.4} vertical={false} />
          <Bar dataKey="usedTokens" stackId="a" fill="#173961" radius={[4, 4, 0, 0]} />
          <Bar dataKey="remainingTokens" stackId="a" fill="#B1B4BF" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </CardBody>
  </Card>
);
