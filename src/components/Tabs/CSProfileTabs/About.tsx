import { Button } from "@heroui/button";
import Image from "next/image";

export const About = () => {
  return (
    <div className="flex flex-col md:flex-row gap-8 py-6 px-4 rounded-xl shadow-sm">
      <div className="w-full md:w-3/5">
        <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mt-6">
          This year marks the 23rd time that Bascom Palmer Eye Institute is ranked the #1 eye hospital in the country as published in U.S. News & World Report's
          Best Hospitals issue. Ophthalmology Times ranked Bascom Palmer among the top two hospitals in the U.S. for our overall, clinical care, and
          ophthalmology residency programs. Additionally, many of our doctors are listed in Castle Connolly's America's Top Doctors.
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mt-4">
          In addition to providing excellent eye care to more than 300,000 patients annually at our five patient care centers in Miami, Palm Beach Gardens,
          Naples, Plantation, and Coral Gables at the Lennar Foundation Medical Center, our faculty members conduct innovative biomedical research and present
          progressive, industry-leading academic programs to ophthalmologists and other physicians in the community and worldwide.
        </p>
      </div>

      <div className="w-full md:w-2/5">
        <div className="relative h-[400px] md:h-[450px] lg:h-[500px] flex flex-col md:grid md:place-items-center">
          {/* First image - top right */}
          <div className="absolute md:top-0 md:right-0 w-60 h-40 sm:w-64 sm:h-44 md:w-72 md:h-48 transition-all duration-300 hover:scale-105">
            <div className="relative w-full h-full rounded-lg overflow-hidden shadow-lg">
              <Image src="/images/c1.png" alt="clinic" fill className="object-cover rounded-lg" />
              <div className="absolute inset-0 bg-blue-600/10 hover:bg-blue-600/0 transition-all duration-300"></div>
            </div>
          </div>

          {/* Second image - center for mobile, middle for larger screens */}
          <div className="absolute top-1/4 right-1/4 w-56 h-40 sm:w-60 sm:h-44 md:w-64 md:h-44 z-10 transition-all duration-300 hover:scale-105">
            <div className="relative w-full h-full rounded-lg overflow-hidden shadow-lg">
              <Image src="/images/c2.png" alt="clinic" fill className="object-cover rounded-lg" />
              <div className="absolute inset-0 bg-blue-600/10 hover:bg-blue-600/0 transition-all duration-300"></div>
            </div>
          </div>

          {/* Third image - bottom left on large screens, centered for mobile */}
          <div className="absolute bottom-6 left-4 w-52 h-36 sm:w-56 sm:h-40 md:bottom-12 md:right-1/2 z-20 transition-all duration-300 hover:scale-105">
            <div className="relative w-full h-full rounded-lg overflow-hidden shadow-lg">
              <Image src="/images/c3.png" alt="clinic" fill className="object-cover rounded-lg" />
              <div className="absolute inset-0 bg-blue-600/10 hover:bg-blue-600/0 transition-all duration-300"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
