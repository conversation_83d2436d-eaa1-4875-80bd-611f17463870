import { Button } from "@heroui/react";
import Image from "next/image";

export const OurDoctors = () => {
  return (
    <div className="py-6 px-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[1, 2, 3, 4, 5, 6].map((doctor) => (
        <div key={doctor} className="rounded-xl p-4 bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800/50 transition-colors duration-200">
          <div className="flex flex-col items-center gap-4">
            <div className="relative">
              <div className="w-24 h-24 rounded-full overflow-hidden ring-1 ring-purple-500 ring-offset-1 ring-offset-white dark:ring-offset-[#1a1b1e]">
                <Image src="/images/doctor-1.png" alt="Doctor" width={96} height={96} className="object-cover w-full h-full" />
              </div>
            </div>

            <div className="flex-1 w-full text-center">
              <h4 className="text-xs font-medium text-gray-900 dark:text-white">Dr<PERSON> <PERSON>, MD PhD</h4>
              <div className="flex items-center justify-center gap-1 mt-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg key={star} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
                <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">(150)</span>
              </div>

              <div className="flex flex-wrap justify-center gap-2 mt-3">
                <span className="px-3 py-1 text-xs bg-gray-100 dark:bg-[#25262b] text-gray-800 dark:text-gray-200 rounded-full">Ophthalmologist</span>
                <span className="px-3 py-1 text-xs bg-gray-100 dark:bg-[#25262b] text-gray-800 dark:text-gray-200 rounded-full">Eye Surgery</span>
                <span className="px-3 py-1 text-xs bg-gray-100 dark:bg-[#25262b] text-gray-800 dark:text-gray-200 rounded-full">Pediatric Care</span>
              </div>

              <div className="mt-4 ">
                <Button
                  size="sm"
                  className="w-fit px-4 py-2 text-xs bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 font-medium"
                >
                  Book Appointment
                </Button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
