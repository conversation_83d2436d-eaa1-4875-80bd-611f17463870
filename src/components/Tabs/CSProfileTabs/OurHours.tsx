import { Card, CardBody } from "@heroui/card";

export const OurHours = () => {
  return (
    <Card className="bg-transparent border border-gray-800 rounded-lg">
      <CardBody className="p-6 space-y-6">
        <h2 className="text-sm font-medium text-white">Our Hours</h2>

        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <span className="text-xs text-white">Monday - Friday</span>
            <span className="text-xs text-white">9:00 AM - 5:00 PM</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-xs text-white">Saturday</span>
            <span className="text-xs text-white">10:00 AM - 2:00 PM</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-xs text-white">Sunday</span>
            <span className="text-xs text-white">Closed</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};
