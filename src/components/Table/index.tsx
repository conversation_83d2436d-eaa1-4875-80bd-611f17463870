import { Pagination, PaginationProps, Table, TableBody, TableCell, TableColumn, TableHeader, TableProps, TableRow } from "@heroui/react";
import React from "react";

type ColumnDef<T> = {
  key: string;
  label: string;
  align?: "left" | "center" | "right";
  render?: (item: T) => React.ReactNode;
  width?: string;
  className?: string;
};

interface PaginationOptions extends Omit<PaginationProps, "total"> {
  rowsPerPage?: number;
  totalItems?: number;
}

interface CustomTableProps<T> extends TableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  pagination?: PaginationOptions | false;
}

const CustomTable = <T extends Record<string, any>>({ data, columns, pagination = false, ...tableProps }: CustomTableProps<T>) => {
  const { rowsPerPage = 5, totalItems = 0 } = pagination === false ? { rowsPerPage: 5, totalItems: 0 } : pagination;
  const [page, setPage] = React.useState(1);
  const pages = Math.ceil(totalItems / rowsPerPage);
  const items = React.useMemo(() => {
    if (pagination === false) return data;

    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return data?.slice(start, end);
  }, [page, data, pagination, rowsPerPage]);

  const renderCell = (column: ColumnDef<T>, item: T) => {
    if (column.render) {
      return column.render(item);
    }
    return item[column.key];
  };

  return (
    <Table
      removeWrapper
      className="rounded-lg p-1 border dark:border-gray-800 min-w-full overflow-scroll sm:overflow-auto"
      bottomContent={
        pagination !== false && pages > 1 ? (
          <div className="flex w-full justify-center">
            <Pagination page={page} total={pages} onChange={(page) => setPage(page)} {...pagination} />
          </div>
        ) : null
      }
      {...tableProps}
    >
      <TableHeader>
        {columns.map((column) => (
          <TableColumn key={column.key}>{column.label}</TableColumn>
        ))}
      </TableHeader>
      <TableBody>
        {items?.map((item, index) => (
          <TableRow key={index}>
            {columns.map((column) => (
              <TableCell key={`row-${index}-${column.key}`}>{renderCell(column, item)}</TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default CustomTable;
