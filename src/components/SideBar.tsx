"use client";

import { SIDEBAR_ITEMS, getNavigationStructure } from "@/config/constants";
import { useLogout } from "@/hooks/useUser";
import { cn } from "@/lib/utils";
import { useStore } from "@/store/store";
import { Accordion, AccordionItem } from "@heroui/accordion";
import { Button } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { BadgeCheck, BarChart, Home, LogOut, MessageSquare } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Fragment, useState } from "react";
import { useTranslation } from "react-i18next";

// Add this custom indicator component
const RightArrowIcon = () => (
  <svg className="w-4 h-4 dark:text-white transition-transform" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const DownArrowIcon = () => (
  <svg className="w-4 h-4 dark:text-white transition-transform" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

interface SideBarProps {
  onClose?: () => void;
  isMobile?: boolean;
}

const SideBar = ({ onClose, isMobile }: SideBarProps) => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const { user } = useStore();
  const { mutateAsync: logout } = useLogout();

  // Add shared state for both accordions
  const [expandedSection, setExpandedSection] = useState<string | null>(() => {
    if (pathname?.includes("/my/cs/")) {
      return "clinic-solutions";
    }
    if (pathname?.includes("/my/es/")) {
      return "enterprise-solutions";
    }
    if (
      pathname?.includes("/appointments") ||
      pathname?.includes("/administration") ||
      pathname?.includes("/message") ||
      pathname?.includes("/notifications") ||
      pathname?.includes("/payments") ||
      pathname?.includes("/telemedicine")
    ) {
      return "public-profile";
    }
    return null;
  });

  const handleLogout = async () => {
    try {
      await logout();
      onClose?.();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Get the navigation structure
  const { mainNavItems, accordionSections, additionalNavItems } = getNavigationStructure(user, t);

  // Split accordion sections for correct ordering
  const clinicAndEnterpriseSections = accordionSections.filter((section) => section.id === "clinic-solutions" || section.id === "enterprise-solutions");
  const publicProfileSection = accordionSections.find((section) => section.id === "public-profile");

  return (
    <Card
      className={cn(
        "h-full border dark:border-gray-800 shadow-xl dark:bg-slate-950 border-gray-200 w-full relative md:max-h-[calc(100vh-100px)]",
        pathname?.includes("/user/") && !user && "hidden"
      )}
    >
      <CardBody className="p-0">
        {/* User Profile Card */}
        <section
          className={cn(
            "rounded-lg",
            {
              "bg-gradient-to-r from-yellow-400 to-white p-[1px]": user?.paid_for_verification,
            },
            { "p-[2px]": user?.is_id_verified }
          )}
        >
          <div className="border-b dark:bg-slate-950 bg-white dark:border-gray-800 border-gray-200 p-4 rounded-xl">
            <div className="flex flex-col space-y-2 items-start">
              <h3 className="text-xs">R.A.V.I.D. # upon DNA confirmation</h3>
              <>
                <div className="flex w-full flex-col items-start gap-2 dark:bg-gray-800 rounded-lg p-2">
                  <div className="flex items-center gap-2">
                    <h2 className="text-xs">{user?.first_name ? `${user.first_name} ${user.last_name}` : user?.email}</h2>
                    {user?.is_id_verified && <BadgeCheck size={16} className="text-yellow-400" />}
                  </div>
                  <span className="text-xs text-blue-500">UID #{user?.user_id}</span>
                  {user?.enterprise_member_role === "member" && (
                    <>
                      <h2 className="text-xs">Enterprise Solution User</h2>
                      <span className="text-xs text-blue-500">ES #{user?.euid}</span>
                    </>
                  )}
                </div>
              </>
            </div>
          </div>
        </section>

        {/* Navigation */}
        <nav className="flex-1 py-2 px-1 space-y-1 overflow-y-auto" style={{ maxHeight: isMobile ? "calc(100vh - 250px)" : "auto" }}>
          {/* My Wallet Button */}
          <Button variant="bordered" color="success" className="w-full text-green-500 justify-start text-xs">
            My Wallet
          </Button>

          {/* Clinic and Enterprise Solutions Accordions */}
          <Accordion
            showDivider={false}
            className="px-0 gap-1"
            selectedKeys={expandedSection ? new Set([expandedSection]) : new Set([])}
            onSelectionChange={(keys) => {
              const key = Array.from(keys)[0]?.toString();
              setExpandedSection(key === expandedSection ? null : key);
            }}
            selectionMode="single"
            itemClasses={{
              base: "py-0",
              title: "font-normal text-medium",
              trigger: "p-2 data-[hover=true]:bg-blue-600 rounded-lg h-12 flex justify-between group",
              indicator: "text-medium text-black",
            }}
          >
            {clinicAndEnterpriseSections.map((section) => (
              <AccordionItem
                key={section.id}
                aria-label={section.title}
                indicator={({ isOpen }) => (isOpen ? <RightArrowIcon /> : <DownArrowIcon />)}
                title={
                  <div className="flex items-center ml-1 space-x-3 text-xs">
                    <span className="dark:text-gray-200 text-gray-800 group-hover:text-white">{section.title}</span>
                  </div>
                }
                className="px-0 border-b-1 p-1 dark:border-gray-800 border-gray-200"
              >
                {user?.cuid && section.id === "clinic-solutions" && (
                  <div className="flex items-center bg-white/50 dark:bg-slate-800/50 rounded p-1 mb-2 ml-4">
                    <p className="text-xs text-orange-600 dark:text-orange-400">CS # {user?.cuid}</p>
                  </div>
                )}
                {user?.euid && section.id === "enterprise-solutions" && (
                  <div className="flex items-center bg-white/50 dark:bg-slate-800/50 rounded p-1 mb-2 ml-4">
                    <p className="text-xs text-orange-600 dark:text-orange-400">ES # {user?.euid}</p>
                  </div>
                )}
                <div className="flex flex-col space-y-1 pl-8">
                  {section.items.map((item) => (
                    <Fragment key={item.id}>
                      <Link
                        href={item.href || ""}
                        onClick={onClose}
                        className={`flex hover:bg-gray-600 hover:text-white items-center p-3 rounded-lg transition-colors ${
                          pathname === item.href ? "bg-blue-600 text-white" : "hover:bg-gray-100 dark:text-gray-200 text-gray-700"
                        } ${item?.color}`}
                      >
                        <span className="text-xs">{item.label}</span>
                      </Link>
                      {item.separator && <div className="border-t border-gray-200 dark:border-gray-800 mt-2 mb-2"></div>}
                    </Fragment>
                  ))}
                </div>
              </AccordionItem>
            ))}
          </Accordion>

          {/* Main Navigation Items */}
          {mainNavItems.map((item) => (
            <Link
              key={item.id}
              href={item.href || ""}
              onClick={onClose}
              className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                pathname === item.href ? "text-blue-50 bg-blue-600" : "hover:bg-blue-600 hover:text-white dark:text-gray-200 text-gray-700"
              }`}
            >
              <span className="text-xs">{item.label}</span>
            </Link>
          ))}

          {/* Public Profile Accordion */}
          {publicProfileSection && (
            <Accordion
              showDivider={false}
              className="px-0 gap-1"
              selectedKeys={expandedSection === "public-profile" ? new Set(["public-profile"]) : new Set([])}
              onSelectionChange={(keys) => {
                const key = Array.from(keys)[0]?.toString();
                setExpandedSection(key === expandedSection ? null : key);
              }}
              selectionMode="single"
              itemClasses={{
                base: "py-0",
                title: "font-normal text-medium",
                trigger: "p-2 data-[hover=true]:bg-blue-600 py-3 rounded-lg  flex justify-between group",
                indicator: "text-medium text-black",
              }}
            >
              <AccordionItem
                key={publicProfileSection.id}
                aria-label={publicProfileSection.title}
                indicator={({ isOpen }) => (isOpen ? <RightArrowIcon /> : <DownArrowIcon />)}
                title={
                  <div className="flex items-center ml-1 space-x-3 text-xs">
                    <span className="dark:text-gray-200 text-gray-800 group-hover:text-white">{publicProfileSection.title}</span>
                  </div>
                }
                className="px-0 p-1"
              >
                <div className="flex flex-col space-y-1 pl-8">
                  {publicProfileSection.items.map((item) => (
                    <Fragment key={item.id}>
                      <Link
                        href={item.href || ""}
                        onClick={onClose}
                        className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                          pathname?.split("?")[0] === (item.href || "").split("?")[0]
                            ? "text-blue-50 bg-blue-600"
                            : "hover:bg-blue-600 hover:text-white dark:text-gray-200 text-gray-700"
                        }`}
                      >
                        <span className="text-xs">{item.label}</span>
                      </Link>
                      {item.separator && <div className="border-t border-gray-200 dark:border-gray-800 mt-2 mb-2"></div>}
                    </Fragment>
                  ))}
                </div>
              </AccordionItem>
            </Accordion>
          )}

          {/* Additional Navigation Items */}
          {additionalNavItems.map((item) => (
            <Fragment key={item.id}>
              {item.separator && <div className="border-t border-gray-200 dark:border-gray-800"></div>}
              <Link
                href={item.href || ""}
                onClick={onClose}
                className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  pathname === item.href ? "text-blue-50 bg-blue-600" : "hover:bg-blue-600 hover:text-white dark:text-gray-200 text-gray-700"
                }`}
              >
                <span className="text-xs">{item.label}</span>
              </Link>
            </Fragment>
          ))}

          {/* Sign Out Button (Mobile Only) */}
          {isMobile && (
            <Link
              href=""
              onClick={handleLogout}
              className={`flex hover:bg-gray-600 hover:text-white items-center space-x-3 px-4 py-3 rounded-lg transition-colors`}
            >
              <span className="text-xs">{t(SIDEBAR_ITEMS.SIGN_OUT)}</span>
            </Link>
          )}
        </nav>
      </CardBody>
    </Card>
  );
};

export default SideBar;
