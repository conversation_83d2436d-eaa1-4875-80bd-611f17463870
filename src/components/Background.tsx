"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

export default function Background() {
  const { theme, resolvedTheme } = useTheme();
  const [isClient, setIsClient] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Preload the image
    const img = new Image();
    img.src = "/images/bg.png";
    img.onload = () => setImageLoaded(true);
    img.onerror = (e) => console.error("Error loading background image:", e);
  }, []);

  if (!isClient) {
    return null; // Return null on server-side
  }

  const isDark = theme === "dark" || resolvedTheme === "dark";

  return (
    <>
      {isDark && (
        <div
          className="fixed inset-0 w-full h-full bg-cover bg-center pointer-events-none"
          style={{
            backgroundImage: imageLoaded ? "url('/images/bg.png')" : "none",
            opacity: "0.35",
            backgroundColor: !imageLoaded ? "#000" : "transparent",
            zIndex: -1,
          }}
        />
      )}
    </>
  );
}
