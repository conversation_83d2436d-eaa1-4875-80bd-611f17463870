"use client";
import { Logo } from "@/components/icons";
import ThemeSwitch from "@/components/theme-switch";
import { siteConfig } from "@/config/site";
import { useGetUser, useLogout } from "@/hooks/useUser";
import { useStore } from "@/store/store";
import { Drawer, DrawerContent } from "@heroui/drawer";
import { Dropdown, DropdownItem, DropdownMenu, DropdownSection, DropdownTrigger } from "@heroui/dropdown";
import { Image } from "@heroui/image";
import { Link } from "@heroui/link";
import { NavbarBrand, NavbarContent, NavbarItem, Navbar as NextUINavbar } from "@heroui/navbar";
import {
  Button,
  Modal,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalHeader,
  Skeleton,
  useDisclosure,
  Tabs,
  Tab,
  Card,
  CardBody,
  TableHeader,
} from "@heroui/react";
import { cn, link as linkStyles } from "@heroui/theme";
import { useQueryClient } from "@tanstack/react-query";
import clsx from "clsx";
import { LogOut, Menu, QrCode, X } from "lucide-react";
import NextLink from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import LanguageToggle from "./LanguageToggle";
import QRCodeGenerator from "./QRCodeGenerator";
import SideBar from "./SideBar";
import SimpleQRCode from "./SimpleQRCode";
export const Navbar = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const { mutateAsync: logout, isPending } = useLogout();
  const { setUser } = useStore();
  const { data: user } = useGetUser();
  const isLoading = false;
  const pathname = usePathname();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { isOpen: isQROpen, onOpen: onQROpen, onClose: onQRClose } = useDisclosure();
  const qrRef = useRef<HTMLDivElement>(null);
  const [qrError, setQrError] = useState(false);
  const [useSimpleQR, setUseSimpleQR] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const { user2, isShared, setIsShared } = useStore();
  const user2_email = localStorage.getItem("user2_email");
  const user2_name = localStorage.getItem("user2_name");
  const defaultUser = localStorage.getItem("default_user");
  const defaultUserEmail = JSON.parse((defaultUser as string) || "{}").email;
  const queryClient = useQueryClient();
  const handleLogout = async () => {
    try {
      await logout();
      setUser(null);
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const handleQROpen = () => {
    setQrError(false);
    onQROpen();
  };

  const handleQRClose = () => {
    if (qrRef.current) {
      qrRef.current.innerHTML = "";
    }
    setToken(null);
    onQRClose();
  };

  const handleQRError = (hasError: boolean) => {
    setQrError(hasError);
    if (hasError) {
      setUseSimpleQR(true);
    }
  };

  const sharedFunction = async () => {
    // store default user
    localStorage.setItem("default_user", JSON.stringify(user));
    setIsShared(true);
    queryClient.invalidateQueries({ queryKey: ["user"] });
    await router.push(`/my/${user?.user_id}/edit`, { scroll: false });
  };

  const defaultUserFunction = async () => {
    setIsShared(false);
    queryClient.invalidateQueries({ queryKey: ["user"] });
    await router.replace(`/my/${user?.user_id}/home`, { scroll: false });
  };

  const rt = localStorage.getItem("rT");

  return (
    <>
      <NextUINavbar maxWidth="2xl" position="sticky" className="p-1" isBordered>
        <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
          <NavbarBrand as="li">
            <NextLink className="flex justify-start items-center p-3" href={user ? `/my/${user?.user_id}/home` : "/signin"}>
              <Logo />
            </NextLink>
            <NextLink className="flex justify-start items-center p-3 mt-2" target="_blank" href={user?.enterprise_website ? user?.enterprise_website : "/"}>
              {user?.euid && rt && user?.enterprise_logo && <Image src={user?.enterprise_logo} alt="euid" width={50} height={50} className="object-contain" />}
            </NextLink>
          </NavbarBrand>
        </NavbarContent>

        <NavbarContent className="hidden sm:flex basis-1/5 sm:basis-full" justify="end">
          <NavbarItem className="hidden lg:flex gap-6 justify-start ml-1">
            <ul className="hidden md:flex gap-6 justify-start ml-1">
              {siteConfig.navItems.map((item) => (
                <NavbarItem key={item.href}>
                  <Link
                    className={clsx(
                      linkStyles({ color: "foreground" }),
                      "text-xs hover:scale-105 transition-all duration-500",
                      pathname === item.href ? "text-blue-500" : ""
                    )}
                    href={item.href}
                  >
                    {t(`ravid.nav.${item.label}`)}
                  </Link>
                </NavbarItem>
              ))}
            </ul>
          </NavbarItem>
          <NavbarItem className="hidden lg:flex gap-5 ">
            <ThemeSwitch />
          </NavbarItem>
          <NavbarItem className="hidden lg:flex gap-2 w-36">
            <LanguageToggle />
          </NavbarItem>

          {user && user ? (
            <NavbarItem className="hidden lg:flex">
              <Dropdown>
                <DropdownTrigger>
                  <button className="w-fit p-0 rounded-full max-w-fit focus:outline-none">
                    {user && (
                      <>
                        {user.private_profile_picture ? (
                          <Image className="w-10 h-10 rounded-full object-cover" src={user.private_profile_picture} alt="profile" width={32} height={32} />
                        ) : (
                          <div className="rounded-full w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 cursor-pointer flex items-center justify-center text-white font-semibold">
                            {user?.first_name && user?.last_name
                              ? `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase()
                              : user?.first_name
                              ? `${user.first_name.slice(0, 2)}`.toUpperCase()
                              : user?.email
                              ? `${user.email.slice(0, 2)}`.toUpperCase()
                              : ""}
                          </div>
                        )}
                      </>
                    )}
                  </button>
                </DropdownTrigger>

                <DropdownMenu disabledKeys={["new", "dis"]} aria-label="Static Actions" className="min-w-[240px]">
                  <DropdownItem onPress={() => router.push(`/my/${user?.user_id}/edit`)} showDivider key="manage" className="py-2">
                    <h2>Manage your R.A.V.I.D. account</h2>
                  </DropdownItem>

                  <DropdownItem showDivider key="profile" onPress={() => router.push(`/my/${user?.user_id}/edit`)} className="py-2">
                    <div
                      className={cn("flex flex-col gap-2 rounded-lg", {
                        "bg-slate-800/50 p-2": !pathname.includes("/my/es") && !pathname.includes("/my/cs"),
                      })}
                    >
                      <div className="flex items-center gap-2">
                        <div className="h-8 w-8 flex-shrink-0 rounded-full overflow-hidden">
                          {user?.private_profile_picture ? (
                            <Image src={user.private_profile_picture} alt={user.first_name} className="h-full w-full object-cover" width={32} height={32} />
                          ) : (
                            <div className="h-full w-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-semibold">
                              {`${user?.first_name?.charAt(0)}${user?.last_name?.charAt(0)}`.toUpperCase()}
                            </div>
                          )}
                        </div>
                        <div className="flex-grow min-w-0">
                          <p className="text-sm font-medium text-gray-700 dark:text-slate-300 truncate">{user?.first_name}</p>
                          <p className="text-xs text-gray-500 dark:text-slate-400 truncate">{user?.email}</p>
                        </div>
                      </div>
                      {!pathname.includes("/my/es") && !pathname.includes("/my/cs") && (
                        <div className="flex items-center justify-between gap-2 px-2">
                          <div className="flex-grow">
                            <p className="text-xs text-gray-500 dark:text-slate-400">Storage used: 12% of 30 GB</p>
                            <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full mt-1">
                              <div className="w-[12%] h-full bg-blue-500 rounded-full"></div>
                            </div>
                          </div>
                          <Link href="#" className="text-xs text-white whitespace-nowrap border border-blue-900 p-[6px] rounded-md">
                            Manage
                          </Link>
                        </div>
                      )}
                    </div>
                  </DropdownItem>

                  {/* Clinic profile */}
                  {user && (user as any).cuid && (
                    <DropdownItem
                      showDivider
                      key="clinic"
                      onPress={() => {
                        router.push(`/my/cs/${(user as any).cuid}/edit`);
                      }}
                    >
                      <div
                        className={cn("flex flex-col gap-2 rounded-lg", {
                          "bg-slate-800/50 p-2": pathname.includes("/my/cs") && !pathname.includes("/my/es"),
                        })}
                      >
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 flex-shrink-0 rounded-full overflow-hidden bg-slate-800">
                            {(user as any).clinic_logo ? (
                              <Image src={(user as any).clinic_logo} alt="CS logo" className="rounded-full w-8 h-8 cursor-pointer" width={32} height={32} />
                            ) : (
                              <div className="h-full w-full flex items-center justify-center text-white font-semibold">CS</div>
                            )}
                          </div>
                          <div className="flex-grow min-w-0">
                            <p className="text-xs font-medium text-gray-700 dark:text-slate-300 truncate">{(user as any).clinic_name || "Clinic"}</p>
                            <p className="text-[10px] text-gray-500 dark:text-slate-400 truncate">{user?.email}</p>
                          </div>
                        </div>
                        {pathname.includes("/my/cs") && (
                          <div className="flex items-center justify-between gap-2 px-2">
                            <div className="flex-grow">
                              <p className="text-xs text-gray-500 dark:text-slate-400">Storage used: 12% of 30 GB</p>
                              <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full mt-1">
                                <div className="w-[12%] h-full bg-blue-500 rounded-full"></div>
                              </div>
                            </div>
                            <Link href="#" className="text-xs text-white whitespace-nowrap border border-blue-900 p-[6px] rounded-md">
                              Manage
                            </Link>
                          </div>
                        )}
                      </div>
                    </DropdownItem>
                  )}

                  {/* Enterprise profile */}
                  {user && (user as any).euid && (
                    <DropdownItem
                      showDivider
                      key="enterprise"
                      onPress={() => {
                        router.push(`/my/es/${(user as any).euid}/edit`);
                      }}
                    >
                      <div
                        className={cn("flex flex-col gap-2 rounded-lg", {
                          "bg-slate-800/50 p-2": pathname.includes("/my/es"),
                        })}
                      >
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 flex-shrink-0 rounded-full overflow-hidden bg-slate-800">
                            {(user as any).enterprise_logo ? (
                              <Image src={(user as any).enterprise_logo} alt="ES logo" className="rounded-full w-8 h-8 cursor-pointer" width={32} height={32} />
                            ) : (
                              <div className="h-full w-full flex items-center justify-center text-white font-semibold">ES</div>
                            )}
                          </div>
                          <div className="flex-grow min-w-0">
                            <p className="text-xs font-medium text-gray-700 dark:text-slate-300 truncate">{(user as any).enterprise_name || "Enterprise"}</p>
                            <p className="text-[10px] text-gray-500 dark:text-slate-400 truncate">{user?.email}</p>
                          </div>
                        </div>
                        {pathname.includes("/my/es") && (
                          <div className="flex items-center justify-between gap-2 px-2">
                            <div className="flex-grow">
                              <p className="text-xs text-gray-500 dark:text-slate-400">Storage used: 12% of 30 GB</p>
                              <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full mt-1">
                                <div className="w-[12%] h-full bg-blue-500 rounded-full"></div>
                              </div>
                            </div>
                            <Link href="#" className="text-xs text-white whitespace-nowrap border border-blue-900 p-[6px] rounded-md">
                              Manage
                            </Link>
                          </div>
                        )}
                      </div>
                    </DropdownItem>
                  )}

                  {!isShared && (
                    <DropdownItem onPress={sharedFunction} showDivider key="shared" className="py-2">
                      <h2>Shared Account {user2_email}</h2>
                    </DropdownItem>
                  )}

                  {isShared && (
                    <DropdownItem onPress={defaultUserFunction} showDivider key="default" className="py-2">
                      <h2>Default Account {defaultUserEmail}</h2>
                    </DropdownItem>
                  )}

                  <DropdownItem key="logout" onPress={handleLogout} className="py-2">
                    <div className="flex items-center gap-2 mx-1">
                      <LogOut className="h-4 w-4 text-gray-500  dark:text-slate-400" />
                      <span className="text-xs  text-gray-700  dark:text-slate-300">Sign out</span>
                    </div>
                  </DropdownItem>

                  <DropdownSection
                    className="flex items-center justify-between"
                    items={[
                      {
                        key: "privacy",
                        label: "Privacy Policy",
                        href: "/privacy",
                      },
                      {
                        key: "terms",
                        label: "Terms of Service",
                        href: "/terms",
                      },
                    ]}
                  >
                    {(item) => (
                      <DropdownItem key={item.key} onPress={() => router.push(item.href)} className="w-1/2 px-3 inline-flex">
                        <span className="text-xs p-2 whitespace-nowrap">{item.label}</span>
                      </DropdownItem>
                    )}
                  </DropdownSection>
                </DropdownMenu>
              </Dropdown>
            </NavbarItem>
          ) : null}

          {isLoading && (
            <div className="flex items-center justify-center">
              <Skeleton className="w-8 h-8 rounded-full" />
            </div>
          )}
        </NavbarContent>

        <NavbarContent className="lg:hidden basis-1" justify="end">
          <ThemeSwitch />
          {user && (
            <button className="p-2" onClick={handleQROpen}>
              <QrCode size={24} />
            </button>
          )}
          {user && (
            <button className="p-2" onClick={() => setIsDrawerOpen(!isDrawerOpen)}>
              {isDrawerOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          )}
        </NavbarContent>

        {/* Drawer for Mobile Menu */}
        <Drawer className="block lg:hidden" isOpen={isDrawerOpen} onOpenChange={setIsDrawerOpen} placement="left" size="xs">
          <DrawerContent>
            <div className="h-full overflow-y-auto">
              <SideBar onClose={() => setIsDrawerOpen(false)} isMobile={true} />
            </div>
          </DrawerContent>
        </Drawer>
      </NextUINavbar>

      {/* QR Code Modal */}
      <Modal
        isOpen={isQROpen}
        onClose={handleQRClose}
        classNames={{
          base: "dark:bg-[#060C1B]",
          backdrop: "bg-black/50 backdrop-blur-sm",
        }}
      >
        <ModalContent>
          <ModalBody>
            <Tabs
              variant="underlined"
              classNames={{
                cursor: "bg-blue-500",
                tab: "text-gray-700 text-xs dark:text-white shadow-sm w-fit",
                tabContent: "text-gray-700 dark:text-white wrap",
                tabList: "flex flex-wrap md:flex-nowrap",
              }}
              aria-label="QR Code Options"
              className="w-full"
            >
              <Tab key="emergency" title="Emergency QR Code">
                <div className="flex justify-center w-full">
                  <p>Scan this QR Code to share your Personal Information</p>
                </div>
                {useSimpleQR ? (
                  <SimpleQRCode onError={handleQRError} onTokenGenerated={setToken} />
                ) : (
                  <>
                    <QRCodeGenerator qrRef={qrRef} onError={handleQRError} isModalOpen={isQROpen} onTokenGenerated={setToken} />
                    {!qrError && (
                      <div
                        ref={qrRef}
                        id="qr-code-container"
                        className="flex justify-center items-center w-full border-gray-300 dark:border-gray-700 rounded-lg p-2"
                      />
                    )}
                  </>
                )}
                {qrError && !useSimpleQR && (
                  <div className="text-center text-red-500 p-4">Unable to generate styled QR code. Switching to simple version...</div>
                )}
                {!qrError && user?.user_id && token && (
                  <div className="flex justify-end w-full">
                    <Link
                      target="_blank"
                      href={`${process.env.NEXT_PUBLIC_APP_URL}/share/${user.user_id}?token=${token}`}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      Open Link
                    </Link>
                  </div>
                )}
              </Tab>
              <Tab key="rcard" title="R-Card">
                <div className="flex justify-center w-full">
                  <p>Scan this QR Code to share your Ravid-Name-Card</p>
                </div>
                {useSimpleQR ? (
                  <SimpleQRCode onError={handleQRError} onTokenGenerated={setToken} />
                ) : (
                  <>
                    <QRCodeGenerator
                      qrRef={qrRef}
                      onError={handleQRError}
                      isModalOpen={isQROpen}
                      onTokenGenerated={setToken}
                      url={`${process.env.NEXT_PUBLIC_APP_URL}/share/${user?.user_id}/ravid-card`}
                    />
                    {!qrError && (
                      <div
                        ref={qrRef}
                        id="qr-code-container"
                        className="flex justify-center items-center w-full border-gray-300 dark:border-gray-700 rounded-lg p-2"
                      />
                    )}
                  </>
                )}
                {qrError && !useSimpleQR && (
                  <div className="text-center text-red-500 p-4">Unable to generate styled QR code. Switching to simple version...</div>
                )}
              </Tab>
            </Tabs>
          </ModalBody>
          <ModalFooter className="border-t dark:border-slate-800">
            <Button size="sm" className="bg-red-700 hover:bg-red-800 text-white text-xs" onPress={handleQRClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};
