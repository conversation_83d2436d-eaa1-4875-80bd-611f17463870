import CustomModal from "./CustomModal";
import FormInput from "@/components/Forms/commons/input";
import MultiSelect from "@/components/Forms/commons/multi-select";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { FormSelect } from "../Forms/commons";
import useGeneSearch from "@/hooks/useGeneSearch";

interface CancerVariantModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: { genes: string[]; depth: string }) => void;
}

interface FormData {
  genes: string[];
  depth: string;
  batchInput?: string;
}

// Simplified cancer types and their associated genes
const CANCER_TYPES = [
  {
    name: "Breast Cancer",
    description: "Cancer that forms in breast tissue",
    genes: ["BRCA1", "BRCA2", "TP53", "PTEN", "ATM", "CHEK2", "PALB2"],
  },
  {
    name: "Colorectal Cancer",
    description: "Cancer of the colon or rectum",
    genes: ["APC", "MLH1", "MSH2", "MSH6", "PMS2", "EPCAM"],
  },
  {
    name: "Lung Cancer",
    description: "Cancer that begins in the lungs",
    genes: ["EGFR", "ALK", "KRAS", "ROS1", "BRAF", "MET"],
  },
  {
    name: "Prostate Cancer",
    description: "Cancer in prostate gland",
    genes: ["BRCA1", "BRCA2", "ATM", "CHEK2", "PALB2", "HOXB13"],
  },
  {
    name: "Pancreatic Cancer",
    description: "Cancer of the pancreas",
    genes: ["BRCA1", "BRCA2", "PALB2", "ATM", "CDKN2A", "PRSS1"],
  },
];

// Common genes that appear in multiple cancers
const COMMON_CANCER_GENES = [
  { key: "BRCA1", label: "BRCA1 - Breast/Ovarian Cancer Gene 1" },
  { key: "BRCA2", label: "BRCA2 - Breast/Ovarian Cancer Gene 2" },
  { key: "TP53", label: "TP53 - Tumor Protein p53" },
  { key: "PTEN", label: "PTEN - Phosphatase and Tensin Homolog" },
  { key: "ATM", label: "ATM - Ataxia Telangiectasia Mutated" },
  { key: "CHEK2", label: "CHEK2 - Checkpoint Kinase 2" },
  { key: "PALB2", label: "PALB2 - Partner And Localizer Of BRCA2" },
  { key: "MLH1", label: "MLH1 - MutL Homolog 1" },
  { key: "MSH2", label: "MSH2 - MutS Homolog 2" },
  { key: "KRAS", label: "KRAS - KRAS Proto-oncogene" },
  { key: "BRAF", label: "BRAF - B-Raf Proto-oncogene" },
];

type TabType = "search" | "batch" | "cancer-types";

export const CancerVariantModal = ({ isOpen, onClose, onConfirm }: CancerVariantModalProps) => {
  const [activeTab, setActiveTab] = useState<TabType>("cancer-types");
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const { data: searchResults, isLoading } = useGeneSearch(searchTerm);

  const { control, handleSubmit, setValue, watch } = useForm<FormData>({
    defaultValues: {
      genes: [],
      depth: "100",
      batchInput: "",
    },
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        const genes = text
          .split(/[\n,]/)
          .map((gene) => gene.trim())
          .filter(Boolean);
        setValue("genes", genes);
      };
      reader.readAsText(file);
    }
  };

  const handleBatchInput = (input: string) => {
    const genes = input
      .split(/[\n,]/)
      .map((gene) => gene.trim())
      .filter(Boolean);
    setValue("genes", genes);
  };

  const handleTypeSelect = (cancerType: typeof CANCER_TYPES[0]) => {
    setSelectedType(cancerType.name);
    setValue("genes", [...cancerType.genes]);
  };

  const onSubmit = (data: FormData) => {
    onConfirm({
      genes: data.genes,
      depth: data.depth,
    });
  };

  const TabButton = ({ tab, label }: { tab: TabType; label: string }) => (
    <button
      onClick={() => setActiveTab(tab)}
      className={`px-4 py-2 text-sm font-medium transition-colors rounded-lg
        ${activeTab === tab ? "bg-blue-600 text-white" : "text-gray-300 hover:text-white"}`}
    >
      {label}
    </button>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case "search":
        return (
          <div className="space-y-2">
            <label className="text-xs text-foreground">Search Genes:</label>
            <MultiSelect
              name="genes"
              control={control}
              options={
                searchResults?.map((gene) => ({
                  key: gene.Gene,
                  label: gene.CancerGenes,
                })) || []
              }
              placeholder="Type to search genes..."
              isLoading={isLoading}
              onInputChange={(value) => setSearchTerm(value)}
            />
            {searchTerm && !isLoading && (!searchResults || searchResults.length === 0) && <div className="text-sm text-gray-400">No genes found</div>}
          </div>
        );
      case "batch":
        return (
          <div className="space-y-4">
            <div>
              <label className="text-xs text-foreground block mb-2">Upload Gene List:</label>
              <input
                type="file"
                accept=".txt,.csv"
                onChange={handleFileUpload}
                className="block w-full text-sm text-gray-300
                  file:mr-4 file:py-2 file:px-4 file:rounded-lg
                  file:border-0 file:text-sm file:font-medium
                  file:bg-blue-600 file:text-white
                  hover:file:bg-blue-700"
              />
            </div>
            <div>
              <label className="text-xs text-foreground block mb-2">Or Paste Genes:</label>
              <textarea
                className="w-full h-32 p-2 rounded-lg bg-slate-900 border-gray-700 border text-sm text-gray-300"
                placeholder="Paste genes (comma or newline separated)"
                onChange={(e) => handleBatchInput(e.target.value)}
              />
            </div>
          </div>
        );
      case "cancer-types":
        return (
          <div className="space-y-4">
            <div className="text-xs text-gray-400">Select a cancer type to include its associated genes:</div>
            <div className="grid grid-cols-2 gap-4">
              {CANCER_TYPES.map((type) => (
                <button
                  key={type.name}
                  onClick={() => handleTypeSelect(type)}
                  className={`p-4 border border-gray-700 rounded-lg text-left transition-colors
                    ${selectedType === type.name ? "bg-blue-600/20 border-blue-500" : "hover:bg-slate-900"}`}
                >
                  <h3 className="font-medium text-sm text-gray-300">{type.name}</h3>
                  <p className="text-xs text-gray-500 mt-1 line-clamp-2">{type.description}</p>
                  <p className="text-xs text-gray-400 mt-2">{type.genes.length} associated genes</p>
                </button>
              ))}
            </div>
          </div>
        );
    }
  };

  const FormContent = (
    <div className="space-y-6">
      <div className="flex space-x-2">
        <TabButton tab="cancer-types" label="Cancer Types" />
        <TabButton tab="search" label="Search" />
        <TabButton tab="batch" label="Batch Input" />
      </div>

      <div className="h-64 overflow-y-auto mt-4">{renderTabContent()}</div>

      <div className="space-y-2">
        <label className="text-xs text-foreground">Selected Genes:</label>
        <div className="flex-1 outline-none p-2 min-w-24 bg-slate-900 text-xs border border-gray-700 rounded-lg">
          {watch("genes").length} genes selected
          {selectedType && <span className="ml-2 text-gray-500">from {selectedType}</span>}
        </div>
      </div>

      <div className="space-y-2">
        <label className="text-xs text-foreground">Sequencing Depth:</label>
        <FormSelect
          name="depth"
          required
          control={control}
          options={[
            { key: "100", label: "100" },
            { key: "200", label: "200" },
            { key: "300", label: "300" },
            { key: "400", label: "400" },
            { key: "500", label: "500" },
          ]}
          placeholder="Enter depth here"
        />
      </div>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onOpenChange={(open) => !open && onClose()}
      title="Cancer Variant Customized Analysis (CVCA)"
      body={FormContent}
      primaryButtonText="Confirm"
      secondaryButtonText="Cancel"
      onPrimaryAction={handleSubmit(onSubmit)}
      size="lg"
    />
  );
};
