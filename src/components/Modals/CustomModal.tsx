"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useDisclosure } from "@heroui/react";
import { Loader2 } from "lucide-react";

type CustomModalProps = {
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl";
  title: string;
  body: string | React.ReactNode;
  icon?: React.ReactNode;
  buttonOpenText?: string;
  buttonText?: string;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  onPrimaryAction?: () => void;
  onSecondaryAction?: () => void;
  onPress?: () => void;
  primaryButtonColor?: string;
  secondaryButtonColor?: string;
  isLoading?: boolean;
  isDisabled?: boolean;
  isPending?: boolean;
  className?: string;
  isOpen?: boolean;
  showCancelButton?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
};

const CustomModal = ({
  size = "lg",
  title,
  body,
  icon,
  buttonOpenText,
  buttonText,
  primaryButtonText,
  secondaryButtonText,
  onPrimaryAction,
  onSecondaryAction,
  onPress,
  primaryButtonColor,
  secondaryButtonColor,
  isLoading,
  isDisabled,
  isPending,
  className,
  isOpen: externalIsOpen,
  onOpenChange: externalOnOpenChange,
  showCancelButton,
}: CustomModalProps) => {
  const { isOpen: internalIsOpen, onOpen, onOpenChange: internalOnOpenChange } = useDisclosure();

  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const onOpenChange = externalOnOpenChange || internalOnOpenChange;
  const openButtonText = buttonOpenText || buttonText;

  const handlePrimary = () => {
    if (onPrimaryAction) {
      onPrimaryAction();
    } else if (onPress) {
      onPress();
    }
  };

  const handleSecondary = (onClose: () => void) => {
    if (onSecondaryAction) {
      onSecondaryAction();
    } else {
      onClose();
    }
  };

  return (
    <>
      {openButtonText && (
        <Button onPress={onOpen} className={className}>
          {icon ? icon : openButtonText}
        </Button>
      )}
      <Modal backdrop="blur" size={size} className="dark:bg-slate-950" isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent className="rounded-lg">
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-2">
                <h1>{title}</h1>
              </ModalHeader>
              <ModalBody>{typeof body === "string" ? <p>{body}</p> : body}</ModalBody>
              <ModalFooter className={`flex gap-2 ${secondaryButtonText ? "justify-between" : "justify-end"}`}>
                {secondaryButtonText && (
                  <Button variant="bordered" onPress={() => handleSecondary(onClose)} size="sm" className={secondaryButtonColor}>
                    {secondaryButtonText}
                  </Button>
                )}
                {primaryButtonText && (
                  <Button
                    onPress={handlePrimary}
                    size="sm"
                    className={primaryButtonColor ? primaryButtonColor : "bg-primary hover:bg-primary-dark text-white"}
                    isLoading={isPending}
                    isDisabled={isPending || isDisabled}
                  >
                    {isLoading ? <Loader2 className="w-4 h-4" /> : primaryButtonText}
                  </Button>
                )}
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default CustomModal;
