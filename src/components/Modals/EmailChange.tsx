"use client";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON>,
  useDisclosure,
  Input,
} from "@heroui/react";
import { useChangeEmail } from "@/hooks/settings-dashboard/useChangeEmail";
import { useState } from "react";

const EmailChange = () => {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const {
    mutate: changeEmail,
    isPending,
    isSuccess,
    isError,
  } = useChangeEmail();
  const [email, setEmail] = useState("");
  return (
    <>
      <Button size="sm" color="primary" variant="light" onPress={onOpen}>
        Change
      </Button>
      <Modal
        backdrop="blur"
        size="lg"
        className="dark:bg-slate-950"
        isOpen={isOpen}
        onOpenChange={onOpenChange}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-2">
                <h1>Change Email Address</h1>
                <p>
                  Once you change your email, you will be logged out and need to
                  log back in with your new email address.
                </p>
              </ModalHeader>
              <ModalBody>
                <Input
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  classNames={{
                    input: "text-xs",
                    label: "text-xs text-gray-500 ",
                    inputWrapper: "dark:bg-slate-900",
                  }}
                  type="email"
                  placeholder="Enter your new email address"
                />
              </ModalBody>
              <ModalFooter>
                <Button
                  size="sm"
                  className="bg-red-700 hover:bg-red-800 text-white text-xs"
                  variant="light"
                  onPress={onClose}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  color="primary"
                  isLoading={isPending}
                  isDisabled={isPending}
                  onPress={() => changeEmail(email)}
                >
                  Change
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default EmailChange;
