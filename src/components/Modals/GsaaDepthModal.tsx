import CustomModal from "./CustomModal";
import { FormSelect } from "../Forms/commons";
import { useForm } from "react-hook-form";

interface GsaaDepthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: { depth: string }) => void;
}

interface FormData {
  depth: string;
}

export const GsaaDepthModal = ({ isOpen, onClose, onConfirm }: GsaaDepthModalProps) => {
  const { control, handleSubmit } = useForm<FormData>({
    defaultValues: {
      depth: "200",
    },
  });

  const onSubmit = (data: FormData) => {
    onConfirm({
      depth: data.depth,
    });
  };

  const FormContent = (
    <div className="space-y-6">
      <div className="space-y-2">
        <label className="text-xs text-foreground">Sequencing Depth:</label>
        <FormSelect
          name="depth"
          required
          control={control}
          options={[
            { key: "200", label: "200" },
            { key: "300", label: "300" },
            { key: "400", label: "400" },
            { key: "500", label: "500" },
          ]}
          placeholder="Select sequencing depth"
        />
      </div>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onOpenChange={(open) => !open && onClose()}
      title="Genome Sequencing Advanced Analysis (GSAA)"
      body={FormContent}
      primaryButtonText="Confirm"
      secondaryButtonText="Cancel"
      onPrimaryAction={handleSubmit(onSubmit)}
      size="sm"
    />
  );
};
