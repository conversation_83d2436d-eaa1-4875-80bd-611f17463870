"use client";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  useDisclosure,
  Input,
} from "@heroui/react";
import { useGetUser } from "@/hooks/useUser";
import { useChangePassword } from "@/hooks/settings-dashboard/useChangePassword";

const PasswordChange = () => {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const {
    mutate: changePassword,
    isPending,
    isSuccess,
    isError,
  } = useChangePassword();
  const { data: user } = useGetUser();
  return (
    <>
      <Button size="sm" color="primary" variant="light" onPress={onOpen}>
        Change
      </Button>
      <Modal
        backdrop="blur"
        size="lg"
        className="dark:bg-slate-950"
        isOpen={isOpen}
        onOpenChange={onOpenChange}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-2">
                <h1>Change Password</h1>
                <p>We'll send a password reset link to your email address.</p>
              </ModalHeader>
              <ModalBody>
                <Input
                  value={user?.email}
                  disabled
                  classNames={{
                    input: "text-xs",
                    label: "text-xs text-gray-500 ",
                    inputWrapper: "dark:bg-slate-900",
                  }}
                  type="email"
                  placeholder="Enter your email address"
                />
              </ModalBody>
              <ModalFooter>
                <Button
                  size="sm"
                  color="primary"
                  isLoading={isPending}
                  isDisabled={isPending}
                  onPress={() => changePassword(user?.email || "")}
                >
                  Send Reset Link
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default PasswordChange;
