"use client";
import { Button, useDisclosure } from "@heroui/react";
import CustomModal from "./CustomModal";
import { useDeleteAccount } from "@/hooks/settings-dashboard/useDeleteAccount";

const deleteAccountBody = (
  <>
  <p>Are you sure you want to delete your R.A.V.I.D. account?</p>
  <p>If you proceed, your entire data and account information will be permanently deleted, including but not limited to:</p>
  <ul className="list-disc list-inside text-xs text-gray-400">
    <li>All AI Analysis, Data & Files & that have been generated, received, uploaded, saved across the R.A.V.I.D. platform.</li>
    <li>All Subscription plans</li>
    <li>All Credits and Monetary units of value that are stored on potentially to be received in the Wallet</li>
    <li>All Communications</li>
    <li>All Collaboration access</li>
    <li>All third party access & credentials that you have linked to this R.A.V.I.D. account.</li>
  </ul>
  <p>This action cannot be undone.</p>
</>
);

const DeleteAccount = () => {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const { mutateAsync: deleteAccount, isPending } = useDeleteAccount();

  const handleDeleteAccount = async () => {
    await deleteAccount();
  };

  return (
    <>
      <Button size="sm" className="bg-red-700 w-full sm:w-auto hover:bg-red-800 text-white text-xs" onPress={onOpen}>
        Delete
      </Button>
      <CustomModal
        title="Delete RAVID Account"
        body={deleteAccountBody}
        primaryButtonText="Delete"
        primaryButtonColor="bg-red-700 hover:bg-red-800 text-white"
        onPrimaryAction={handleDeleteAccount}
        isPending={isPending}
        isDisabled={isPending}
        isOpen={isOpen}
        onOpenChange={onOpenChange}
      />
    </>
  );
};

export default DeleteAccount;
