import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Input } from "@heroui/react";
import { useState, useCallback } from "react";
import { useVerifyPromoCode } from "@/hooks/useServices";
import debounce from "lodash/debounce";

interface PromoCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (promoCodes: Record<string, string>) => void;
  selectedServices: Array<{
    id: string;
    name: string;
    genes?: string[];
    depth?: string;
  }>;
}

interface PromoValidationState {
  isValid: boolean;
  message: string;
  isValidating?: boolean;
}

export const PromoCodeModal = ({ isOpen, onClose, onConfirm, selectedServices }: PromoCodeModalProps) => {
  const [promoCodes, setPromoCodes] = useState<Record<string, string>>({});
  const [validationStates, setValidationStates] = useState<Record<string, PromoValidationState>>({});
  const [isLoading, setIsLoading] = useState(false);
  const verifyPromoCode = useVerifyPromoCode();

  const debouncedVerifyPromoCode = useCallback(
    debounce(async (serviceId: string, code: string) => {
      if (!code) {
        setValidationStates((prev) => ({
          ...prev,
          [serviceId]: { isValid: true, message: "" },
        }));
        return;
      }

      try {
        const response = await verifyPromoCode.mutateAsync({
          code,
          service_ids: [serviceId],
        });

        setValidationStates((prev) => ({
          ...prev,
          [serviceId]: {
            isValid: true,
            message: "Valid promo code",
            isValidating: false,
          },
        }));
      } catch (error) {
        setValidationStates((prev) => ({
          ...prev,
          [serviceId]: {
            isValid: false,
            message: "Invalid promo code",
            isValidating: false,
          },
        }));
      }
    }, 500),
    []
  );

  const handlePromoCodeChange = (serviceId: string, code: string) => {
    setPromoCodes((prev) => ({
      ...prev,
      [serviceId]: code,
    }));

    setValidationStates((prev) => ({
      ...prev,
      [serviceId]: {
        ...prev[serviceId],
        isValidating: true,
        message: code ? "Verifying..." : "",
      },
    }));

    debouncedVerifyPromoCode(serviceId, code);
  };

  const handleConfirm = async () => {
    const validPromoCodes = Object.entries(promoCodes).reduce((acc, [serviceId, code]) => {
      const validation = validationStates[serviceId];
      if (validation?.isValid && code) {
        acc[serviceId] = code;
      }
      return acc;
    }, {} as Record<string, string>);

    try {
      setIsLoading(true);
      await onConfirm(validPromoCodes);
      onClose();
    } catch (error) {
      console.error("Error during checkout:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal backdrop="blur" size="lg" className="dark:bg-slate-950" isOpen={isOpen} onOpenChange={(open) => !open && onClose()}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-2">
              <h1>Enter Promo Codes</h1>
              <p className="text-sm text-gray-400">Enter promo codes for your selected services (optional)</p>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                {selectedServices.map((service) => (
                  <div key={service.id} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <label className="text-sm">{service.name}</label>
                      {service.genes && (
                        <span className="text-xs text-gray-400">
                          {service.genes.length} genes, {service.depth}x depth
                        </span>
                      )}
                    </div>
                    <div className="space-y-1">
                      <Input
                        value={promoCodes[service.id] || ""}
                        onChange={(e) => handlePromoCodeChange(service.id, e.target.value)}
                        placeholder="Enter promo code (optional)"
                        className="w-full"
                        color={validationStates[service.id]?.isValid === false ? "danger" : "default"}
                        classNames={{
                          input: "text-sm",
                          inputWrapper: `dark:bg-slate-900 ${
                            validationStates[service.id]?.isValid === true && promoCodes[service.id] ? "border-green-500" : ""
                          }`,
                        }}
                      />
                      {validationStates[service.id]?.message && (
                        <p
                          className={`text-xs ${
                            validationStates[service.id]?.isValidating
                              ? "text-gray-400"
                              : validationStates[service.id]?.isValid
                              ? "text-green-500"
                              : "text-red-500"
                          }`}
                        >
                          {validationStates[service.id]?.message}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ModalBody>
            <ModalFooter>
              <Button size="sm" variant="bordered" onPress={onClose}>
                Cancel
              </Button>
              <Button
                size="sm"
                color="primary"
                onPress={handleConfirm}
                isLoading={isLoading}
                isDisabled={isLoading || Object.values(validationStates).some((state) => state?.isValidating)}
              >
                {isLoading ? "Processing..." : "Continue"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
