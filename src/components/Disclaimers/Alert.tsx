"use client";
import React, { useEffect, useState } from "react";

const Alert = ({ alert }: { alert: TabAlert }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Check localStorage when component mounts
    const dismissedAlerts = JSON.parse(localStorage.getItem("dismissedAlerts") || "{}");
    const alertDismissalInfo = dismissedAlerts[alert.id];

    // If this alert was dismissed
    if (alertDismissalInfo) {
      const dismissedAt = new Date(alertDismissalInfo.dismissedAt);
      // Use the most recent timestamp available, fallback to current time if none exists
      const lastUpdateTimestamp = alert.updatedAt || alert.updated_at || alert.createdAt || alert.created_at || new Date().toISOString();
      const alertLastUpdated = new Date(lastUpdateTimestamp);

      // Show alert if it was updated after being dismissed
      setIsVisible(dismissedAt < alertLastUpdated);
    }
  }, [alert]);

  const handleClose = () => {
    setIsVisible(false);

    // Get current dismissed alerts
    const dismissedAlerts = JSON.parse(localStorage.getItem("dismissedAlerts") || "{}");

    // Add or update this alert's dismissal info
    dismissedAlerts[alert.id] = {
      dismissedAt: new Date().toISOString(),
      message: alert.message, // Store message to help with debugging if needed
    };

    localStorage.setItem("dismissedAlerts", JSON.stringify(dismissedAlerts));
  };

  if (!isVisible) return null;

  return (
    <div className="flex justify-center mb-4">
      <div className="relative inline-flex items-center">
        <span className="bg-gradient-to-b from-[#a59039] via-[#61562b] to-[#564606] text-white dark:text-white text-xs font-medium px-3 py-1 rounded-full">
          {alert?.message}
        </span>
        <button
          onClick={handleClose}
          className="absolute -top-1 -right-2 bg-white/40 text-white rounded-xl w-4 h-4 flex items-center justify-center text-xs hover:bg-red-600 hover:text-white focus:outline-none"
          aria-label="Close"
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default Alert;
