"use client";

import { useState } from "react";
import { Card, CardBody } from "@heroui/card";
import Link from "next/link";

const ImportantNoticeDropdown = () => {
  const [isDisclaimerExpanded, setIsDisclaimerExpanded] = useState(false);

  return (
    <Card className="border border-gray-200 bg-transparent dark:border-gray-700">
      <CardBody className="p-2">
        <div
          className="flex items-center justify-between cursor-pointer"
          onClick={() => setIsDisclaimerExpanded(!isDisclaimerExpanded)}
        >
          <span className="red-heading">
            Important Notice
          </span>
          <svg
            className={`w-5 h-5 transition-transform ${
              isDisclaimerExpanded ? "rotate-180" : ""
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>

        {isDisclaimerExpanded && (
          <div className="mt-4 space-y-4 text-xs text-gray-600 dark:text-gray-300">
            <p className="text-xs text-justify dark:text-gray-300">
              As we are yet in the development stage of the{" "}
              <span className="font-bold">R.A.V.I.D.</span> platform, we are
              currently testing algorithms to return back a completed
              personalized{" "}
              <Link
                href="/dna/analysis"
                className="text-blue-600 dark:text-blue-500 underline"
              >
                DNA analysis
              </Link>{" "}
              from raw{" "}
              <Link
                href="/genome/sequencing/formats"
                className="text-blue-600 dark:text-blue-500 underline"
              >
                genome sequencing formats.
              </Link>{" "}
            </p>
            <p className="text-xs text-justify dark:text-gray-300">
              The R.A.V.I.D. platform will be enabled to assist you along with
              your clinician and/ or care provider with a few critical
              computational and technological{" "}
              <Link
                href="/genome/sequencing/steps"
                className="text-blue-600 dark:text-blue-500 underline"
              >
                steps
              </Link>{" "}
              (aided by third party algorithms) to enable you to get your own
              individualized{" "}
              <Link
                href="/dna/analysis"
                className="text-blue-600 dark:text-blue-500 underline"
              >
                DNA analysis
              </Link>
              .
            </p>
            <p className="text-xs text-justify dark:text-gray-300">
              In the future on this page, you will be able to view your own{" "}
              <Link
                href="/dna"
                className="text-blue-600 dark:text-blue-500 underline"
              >
                DNA
              </Link>{" "}
              Dashboard. Below is a visual representation of what to expect with
              the rapid advances of technology.
            </p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default ImportantNoticeDropdown;
