"use client";

import React, { useState, useEffect } from "react";

import Link from "next/link";
// import useAuthStore from "@/store/store";
import { useGetUser } from "@/hooks/useUser";
import { Switch } from "@heroui/switch";
interface TermsOfUseProps {
  showAI?: boolean;
  onTermsToggle?: (checked: boolean) => void;
}

const TermsOfUse = ({ showAI = false, onTermsToggle }: TermsOfUseProps) => {
  const [isDisclaimerExpanded, setIsDisclaimerExpanded] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);

  const { data: user } = useGetUser();
  useEffect(() => {
    // Load initial state from localStorage
    const storedValue = localStorage.getItem("termsAccepted");
    setIsTermsAccepted(storedValue === "true");
  }, []);

  const handleTermsToggle = (checked: boolean) => {
    setIsTermsAccepted(checked);
    localStorage.setItem("termsAccepted", checked.toString());
    onTermsToggle?.(checked);
  };

  return (
    <div className="border border-gray-200 bg-transparent dark:border-gray-700 rounded-lg p-2">
      <div className="flex justify-between items-center">
        <div className="flex-1">
          <div className={`flex ${isDisclaimerExpanded ? "flex-col" : "items-center"} gap-2`}>
            <h4 className="font-medium text-xs text-foreground whitespace-nowrap">Terms of Use:</h4>
            {!isDisclaimerExpanded ? (
              <>
                <span className="muted hidden md:block">By using this or any AI agent, product or service across the R.A.V.I.D. platform, you...</span>
                <button onClick={() => setIsDisclaimerExpanded(true)} className="text-blue-500 hover:underline inline text-xs">
                  Show more
                </button>
              </>
            ) : (
              <span className="muted">
                By using this or any AI agent, product or service across the R.A.V.I.D. platform, you agree to these Terms of Use. You acknowledge that the AI
                is provided 'as is,' and we make no warranties regarding its accuracy, reliability, or suitability for any purpose. To the fullest extent
                permitted by law, we disclaim all liability for any damages, losses, or consequences arising from your use of this service. You use any AI
                agents, products or services at your own risk. Please refer to{" "}
                <Link href={`/my/permissions`} className="text-blue-500 hover:underline inline text-xs">
                  Permissions
                </Link>{" "}
                for additional setting features.{" "}
                <button onClick={() => setIsDisclaimerExpanded(false)} className="text-blue-500 hover:underline inline text-xs">
                  Show less
                </button>
              </span>
            )}
          </div>
        </div>
        <div className="flex flex-col items-center gap-2 space-x-2 ml-4">
          {showAI && <span className="text-sm font-medium">AI</span>}
          <Switch color="success" size="sm" checked={isTermsAccepted} onChange={() => handleTermsToggle(isTermsAccepted)} />
        </div>
      </div>
    </div>
  );
};

export default TermsOfUse;
