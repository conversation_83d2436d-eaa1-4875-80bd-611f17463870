"use client";
import { ads } from "@/config/constants";
import { Card } from "@heroui/react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";

const Ads = () => {
  const router = useRouter();
  const pathname = usePathname();
  // const handleAdClick = (service: string) => {
  //   localStorage.setItem("directToService", service);
  //   router.push(`${pathname.split("/").slice(0, -1).join("/")}/preventive-services?t=${Date.now()}`);
  // };

  const promotedProfile = ads.find((ad) => ad.banner.text === "Promoted Profile");
  const otherAds = ads.filter((ad) => ad.banner.text !== "Promoted Profile");

  return (
    <div className="space-y-4 w-[180px]">
      {/* Promoted Services Container */}
      <div className="space-y-4 bg-slate-100 dark:bg-slate-900 p-2 rounded-lg">
        <div className="text-xs text-center bg-slate-950 text-white p-2 rounded-lg mb-4">{otherAds[0]?.banner.text}</div>
        {otherAds.map((ad, index) => (
          <Ad ad={ad} index={index} key={index} />
        ))}
      </div>

      {/* Promoted Profile Container */}
      {promotedProfile && (
        <div className="bg-white dark:bg-slate-900 p-2 rounded-lg">
          <div className="text-xs text-center bg-slate-950 text-white p-2 rounded-lg mb-4">{promotedProfile.banner.text}</div>
          <Ad ad={promotedProfile} index={0} key={0} />
        </div>
      )}
    </div>
  );
};

const Ad = ({ ad, index }: { ad: any; index: number }) => {
  return (
    <Card
      key={index}
      className="p-2 dark:bg-slate-950 cursor-pointer hover:shadow-lg transition-shadow relative overflow-hidden"
      // onPress={() => handleAdClick(ad.service)}
    >
      <div className="relative h-64 rounded-lg overflow-hidden">
        <div className="absolute inset-0 h-[50%] w-full">
          <Image src={ad.image} alt={ad.title} layout="fill" objectFit="cover" className="opacity-80" quality={100} />
        </div>
        <div className="px-1 flex flex-col h-full justify-end items-center">
          <h2 className="text-[12px]">{ad.title}</h2>
          <span className="text-white text-[10px] p-1 rounded-lg">{ad.description}</span>
        </div>
      </div>
    </Card>
  );
};

export default Ads;
