"use client";
import React, { useState } from "react";
import { CloudUpload, Trash2 } from "lucide-react";
import { But<PERSON> } from "@heroui/react";
import { useUploadEnterpriseImages } from "@/hooks/useEnterpriseSolution";
import { toast } from "react-hot-toast";
import { useQueryClient } from "@tanstack/react-query";

interface LogoImageUploadProps {
  currentImage?: string | null;
  onUpload?: (url: string) => void;
  onDelete?: () => void;
}

const LogoImageUpload: React.FC<LogoImageUploadProps> = ({ currentImage, onUpload, onDelete }) => {
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const { mutate: uploadEnterpriseImages, isPending } = useUploadEnterpriseImages();
  const queryClient = useQueryClient();

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type !== "image/jpeg" && file.type !== "image/jpg" && file.type !== "image/png") {
        toast.error("Please upload a JPEG, JPG or PNG image");
        return;
      }

      setSelectedImage(file);
      const formData = new FormData();
      formData.append("logo_file", file);

      try {
        uploadEnterpriseImages(formData, {
          onSuccess: () => {
            // Invalidate the enterprise-profile query to fetch fresh data
            queryClient.invalidateQueries({ queryKey: ["enterprise-profile"] });

            // The onUpload callback will be handled by the parent component
            // which will get the updated logo URL from the refreshed enterprise profile data
          },
          onError: () => {
            toast.error("Failed to upload logo");
          },
        });
      } catch (error) {
        toast.error("Failed to upload logo");
      }
    }
  };

  const getFileName = (url: string | null | undefined) => {
    if (!url) return "No file selected";
    const parts = url.split("/");
    return parts[parts.length - 1] || "logo.png";
  };

  return (
    <div className="absolute inset-0 group">
      <div className="relative inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 rounded-full flex h-full items-center justify-center gap-2">
        {!isPending && (
          <>
            <label
              className="hidden group-hover:flex items-center justify-center w-10 h-10 bg-blue-600 hover:bg-blue-700 
              rounded-full cursor-pointer transition-all duration-200 opacity-0 group-hover:opacity-100"
            >
              <CloudUpload className="w-5 h-5 text-white" />
              <input type="file" accept="image/jpeg,image/jpg,image/png" onChange={handleImageChange} className="hidden" />
            </label>
            {currentImage && (
              <Button
                type="button"
                onPress={onDelete}
                className="w-fit px-4 py-[1.5px] absolute bottom-1 left-1/2 transform -translate-x-1/2 flex items-center justify-center h-fit bg-[#1b2335]
                rounded-lg cursor-pointer transition-all duration-200 opacity-100 gap-4"
                size="sm"
                color="danger"
                variant="flat"
              >
                <span className="text-xs text-white">img_57779.png</span>
                <Trash2 className="w-5 h-5 text-red-500" />
              </Button>
            )}
          </>
        )}
        {isPending && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LogoImageUpload;
