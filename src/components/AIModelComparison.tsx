import { ChevronDown, Star } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";

const AIModelComparison = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="w-full space-y-4">
      <div className="flex md:block justify-center">
        <span className="text-xs cursor-pointer flex items-center gap-2 text-blue-500 hover:bg-transparent" onClick={() => setIsExpanded(!isExpanded)}>
          Learn about each AI Model based on your needs
          {/* use a chevron icon */}
          <ChevronDown className={`transition-transform duration-300 ease-in-out w-4 ${isExpanded ? "rotate-180" : ""}`} />
        </span>
      </div>

      {isExpanded && (
        <div className="space-y-6 p-4 rounded-lg bg-white dark:bg-slate-900 border border-gray-300 dark:border-gray-800">
          <div className="overflow-x-scroll space-y-2 lg:overflow-x-hidden">
            <h2 className="text-black dark:text-white">Overall Comparison of AI models:</h2>
            <Table
              aria-label="AI Model Comparison"
              removeWrapper
              className="w-full border-collapse"
              classNames={{
                th: "bg-gray-100 dark:bg-slate-950 text-xs",
              }}
            >
              <TableHeader>
                <TableColumn>
                  <h2 className="text-left py-2 px-4 text-black dark:text-white">Feature</h2>
                </TableColumn>
                <TableColumn>
                  <h2 className="text-left py-2 px-4 text-black dark:text-white">Claude Sonnet 3.7</h2>
                </TableColumn>
                <TableColumn>
                  <h2 className="text-left py-2 px-4 text-black dark:text-white">Gemini 1.5 Pro (May)</h2>
                </TableColumn>
                <TableColumn>
                  <h2 className="text-left py-2 px-4 text-black dark:text-white">Llama 3.2 1B</h2>
                </TableColumn>
              </TableHeader>
              <TableBody>
                <TableRow className="text-xs border-b border-gray-300 dark:border-gray-800">
                  <TableCell className="py-2 px-4 text-black dark:text-white">Intelligence Level</TableCell>
                  <TableCell className="py-2 px-4">
                    <div className="flex items-center gap-1">
                      {/*loop use 5 stars */}
                      {Array.from({ length: 5 }).map((_, index) => (
                        <Star key={index} className="w-4 h-4 text-yellow-500" />
                      ))}
                      <span className="text-xs text-black dark:text-white">(High Reasoning + Medical Reports)</span>
                    </div>
                  </TableCell>
                  <TableCell className="py-2 px-4">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 4 }).map((_, index) => (
                        <Star key={index} className="w-4 h-4 text-yellow-500" />
                      ))}
                      <span className="text-xs text-black dark:text-white">(High with Multimodal Capability)</span>
                    </div>
                  </TableCell>
                  <TableCell className="py-2 px-4">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 2 }).map((_, index) => (
                        <Star key={index} className="w-4 h-4 text-yellow-500" />
                      ))}
                      <span className="text-xs text-black dark:text-white">(Good for Basic Q&A)</span>
                    </div>
                  </TableCell>
                </TableRow>
                <TableRow className="border-b text-xs border-gray-300 dark:border-gray-800">
                  <TableCell className="py-2 px-4 text-black dark:text-white">Medical Knowledge Accuracy</TableCell>
                  <TableCell className="py-2 px-4">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 4 }).map((_, index) => (
                        <Star key={index} className="w-4 h-4 text-yellow-500" />
                      ))}
                      <span className="text-xs text-black dark:text-white">(Very High, detailed analysis)</span>
                    </div>
                  </TableCell>
                  <TableCell className="py-2 px-4">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 4 }).map((_, index) => (
                        <Star key={index} className="w-4 h-4 text-yellow-500" />
                      ))}
                      <span className="text-xs text-black dark:text-white">(Very High, with multimodal support)</span>
                    </div>
                  </TableCell>
                  <TableCell className="py-2 px-4">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 2 }).map((_, index) => (
                        <Star key={index} className="w-4 h-4 text-yellow-500" />
                      ))}
                      <span className="text-xs text-black dark:text-white">(Basic & Limited Medical Knowledge)</span>
                    </div>
                  </TableCell>
                </TableRow>
                <TableRow className="border-b border-gray-300 dark:border-gray-800 text-xs">
                  <TableCell className="py-2 px-4 text-black dark:text-white">Ideal Use Case</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Detailed Medical Reports + Complex Medical Q&A</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Comprehensive Medical Analysis + Multimodal Imaging Analysis</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Basic Medical Q&A</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="overflow-x-scroll md:overflow-x-hidden">
            <h3 className="text-xs mb-4 text-black dark:text-white">Best AI Model by Use Case:</h3>
            <Table
              aria-label="Best AI Model by Use Case"
              removeWrapper
              className="w-full border-collapse"
              classNames={{
                th: "bg-gray-100 dark:bg-slate-950 text-xs",
              }}
            >
              <TableHeader>
                <TableColumn>
                  <h2 className="text-left py-2 px-4 text-black dark:text-white">Use Case</h2>
                </TableColumn>
                <TableColumn>
                  <h2 className="text-left py-2 px-4 text-black dark:text-white">Best Model</h2>
                </TableColumn>
                <TableColumn>
                  <h2 className="text-left py-2 px-4 text-black dark:text-white">Reason</h2>
                </TableColumn>
              </TableHeader>
              <TableBody>
                <TableRow className="text-xs border-b border-gray-300 dark:border-gray-800">
                  <TableCell className="py-2 px-4 text-black dark:text-white">Detailed Medical Report Analysis</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Claude Sonnet 3.7</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Highest accuracy + reasoning</TableCell>
                </TableRow>
                <TableRow className="text-xs border-b border-gray-300 dark:border-gray-800">
                  <TableCell className="py-2 px-4 text-black dark:text-white">Long Medical Reports + Multimodal (Images + Text)</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Gemini 1.5 Pro</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Largest context window + multimodal support</TableCell>
                </TableRow>
                <TableRow className="text-xs border-b border-gray-300 dark:border-gray-800">
                  <TableCell className="py-2 px-4 text-black dark:text-white">Basic Option for Medical Q&A</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Llama 3.2 1B</TableCell>
                  <TableCell className="py-2 px-4 text-black dark:text-white">Easiest for basic medical questions</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
            Source:{" "}
            <Link className="text-blue-500 hover:text-blue-600" href="https://artificialanalysis.ai/leaderboards/models" target="_blank">
              https://artificialanalysis.ai/leaderboards/models
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIModelComparison;
