"use client";
import { languages } from "@/config/constants";
import { changeLanguage } from "@/lib/utils/index";
import { useStore } from "@/store/store";
import { Select, SelectItem, SelectProps } from "@heroui/select";

type LanguageToggleProps = {
  label?: string;
  size?: "sm" | "md" | "lg";
  className?: SelectProps["classNames"];
  radius?: "sm" | "md" | "lg";
};

export default function LanguageToggle({ label, size = "sm", className, radius = "sm" }: LanguageToggleProps) {
  const { language, setLanguage } = useStore();
  // const { currentLanguage, setLanguage } = useLanguage();
  const handleLanguageChange = (value: string) => {
    setLanguage(value);
    changeLanguage(value);
  };

  return (
    <Select
      size={size}
      radius={radius}
      disallowEmptySelection
      classNames={{
        trigger: "dark:bg-slate-900 text-xs",
        value: "dark:bg-slate-900 text-xs",
        label: "text-xs",
        ...className,
      }}
      aria-label="Language"
      selectionMode="single"
      label={label}
      selectedKeys={[language]}
      onChange={(e) => handleLanguageChange(e.target.value)}
    >
      {languages.map((lang) => (
        <SelectItem key={lang.key}>{lang.label}</SelectItem>
      ))}
    </Select>
  );
}
