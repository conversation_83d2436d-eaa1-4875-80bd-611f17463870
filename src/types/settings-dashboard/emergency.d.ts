declare interface EmergencyInfo {
  contact_information: EmergencyContact[];
  medical_information: MedicalInformation;
}

declare interface EmergencyContact {
  id?: string;
  contact_name?: string;
  email?: string;
  phone_number?: string;
  relationship?: string;
}

declare interface MedicalInformation {
  id?: string;
  allergies?: string[];
  emergency_medications?: string[];
  blood_type?: string;
  critical_information?: string;
  past_admissions?: string;
}
