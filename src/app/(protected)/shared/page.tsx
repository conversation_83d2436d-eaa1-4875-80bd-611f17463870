"use client";
import { useGetUser } from "@/hooks/useUser";
import { useStore } from "@/store/store";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
const Page = () => {
  const user_id = useStore((state) => state.user.user_id);
  const setUser2 = useStore((state) => state.setUser2);
  const setIsShared = useStore((state) => state.setIsShared);
  const { data: user2 } = useGetUser();
  const router = useRouter();
  const queryClient = useQueryClient();

  useEffect(() => {
    // Get shared token from URL
    const url = new URL(window.location.href);
    const shared_token = url.searchParams.get("shared_token");
    const user2_email = url.searchParams.get("shared_by_email");
    const user2_name = url.searchParams.get("shared_by_name");

    if (shared_token) {
      localStorage.setItem("shared_token", shared_token);
      if (user2_email) {
        localStorage.setItem("user2_email", user2_email);
      }

      if (user2_name) {
        localStorage.setItem("user2_name", user2_name);
      }

      // Set user2 state to true by default when shared token is set
      setIsShared(true);
      queryClient.invalidateQueries({ queryKey: ["user"] });

      // Redirect to home page
      router.push(`/my/${user2?.user_id}/home`);
    }
  }, [user_id, router, setUser2, queryClient]);

  return <div>Loading shared profile...</div>;
};

export default Page;
