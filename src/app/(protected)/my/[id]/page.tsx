"use client";
// "use cache";
import Ads from "@/components/Ads";
import DynamicProfileBuilder from "@/components/Forms/PublicProfile";
import ProfileCard from "@/components/Forms/PublicProfile/ProfileCard";
import SpinnerLoading from "@/components/SpinnerLoading";
import { useProfileCategory, useSaveProfileCategory } from "@/hooks/public-profile/useProfileCategory";
import { useGetUser } from "@/hooks/useUser";
import { FormProvider, useForm } from "react-hook-form";

const Page = () => {
  // hooks
  const { data: user, isLoading: isUserLoading } = useGetUser();
  const { data: categories, isLoading: isCategoriesLoading } = useProfileCategory();
  const { mutate: saveCategories } = useSaveProfileCategory();
  // Profile Form
  const profileForm = useForm({
    values: {
      username: user?.username || "",
      first_name: user?.first_name || "",
      middle_name: user?.middle_name || "",
      last_name: user?.last_name || "",
      title: user?.title || "",
      speciality: user?.speciality || "",
      locations: user?.locations || "",
      bio: user?.bio || "",
      profile_picture: user?.profile_picture || "",
    },
  });
  // Category Form
  const categoryForm = useForm<any>({
    values: {
      categories: categories || [],
    },
  });
  const { handleSubmit: handleCategorySubmit } = categoryForm;
  // Create a wrapper function to handle category form submission
  const triggerCategorySubmit = () => {
    handleCategorySubmit((data) => {
      saveCategories(data);
    })();
  };

  if (isCategoriesLoading || isUserLoading) return <SpinnerLoading />;
  return (
    <div className="w-full">
      <div className="flex gap-4">
        <div className="w-full space-y-6">
          <FormProvider {...profileForm}>
            <ProfileCard handleCategorySubmit={triggerCategorySubmit} />
          </FormProvider>
          <FormProvider {...categoryForm}>
            <DynamicProfileBuilder />
          </FormProvider>
        </div>
        {/* Ads Section */}
        <div className="hidden lg:block">
          <Ads />
        </div>
      </div>
    </div>
  );
};

export default Page;
