"use client";
import Disclaimer from "@/components/Disclaimer";
import { Chip } from "@heroui/chip";
import { Card } from "@heroui/card";
import { Download, ExternalLink, Printer } from "lucide-react";
import Link from "next/link";
import { Bar, Bar<PERSON>hart, CartesianGrid, Cell, Pie, PieChart, ResponsiveContainer, Tooltip, TooltipProps, XAxis, YAxis } from "recharts";

// Define types for the chart components
interface ChartContainerProps {
  children: React.ReactElement;
  config: {
    value: {
      label: string;
      color: string;
    };
  };
}

interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

// Custom types for the pie chart tooltip
interface PieChartPayload {
  name: string;
  value: number;
  percent: number;
  payload: any;
  color: string;
  [key: string]: any;
}

interface PieTooltipProps {
  active?: boolean;
  payload?: PieChartPayload[];
  label?: string;
}

// Add missing ChartContainer and ChartTooltipContent components
const ChartContainer = ({ children, config }: ChartContainerProps) => {
  return (
    <div className="w-full h-[250px]">
      <ResponsiveContainer width="100%" height="100%">
        {children}
      </ResponsiveContainer>
    </div>
  );
};

const ChartTooltipContent = ({ active, payload, label }: TooltipProps<number, string>) => {
  if (!active || !payload || !payload.length) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-slate-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow-sm">
      <p className="text-xs font-medium">{`${label}`}</p>
      <p className="text-xs text-blue-600 dark:text-blue-400">{`${payload[0].name}: ${payload[0].value}`}</p>
    </div>
  );
};

// Special tooltip for Pie Charts
const PieChartTooltipContent = ({ active, payload }: PieTooltipProps) => {
  if (!active || !payload || !payload.length) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-slate-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow-sm">
      <p className="text-xs font-medium">{payload[0].name}</p>
      <p className="text-xs text-blue-600 dark:text-blue-400">{`Value: ${payload[0].value}`}</p>
      <p className="text-xs text-blue-600 dark:text-blue-400">{`Percentage: ${(payload[0].percent * 100).toFixed(1)}%`}</p>
    </div>
  );
};

const chartConfig = {
  value: {
    label: "Variant Count",
    color: "#3b82f6",
  },
};

const GeneticSummaryPage = () => {
  const summaryStats = {
    totalVariants: 500,
    rareVariants: 121,
    pathogenicVariants: 45,
  };

  const clinicalData = [
    { name: "Conflicting Classifications of Pathogenicity", value: 98 },
    { name: "Others", value: 2 },
  ];

  const diseases = ["Age related cataract", "Adult onset diabetes", "Wolfram Syndrome", "Wolfram Syndrome Type 1", "ADNSHL Type 6"];

  const geneFrequencyData = [
    { gene: "PRDX3-TCP3", value: 30 },
    { gene: "TEAD3", value: 27 },
    { gene: "FOXP4", value: 23 },
    { gene: "MLK4", value: 20 },
    { gene: "RAB43", value: 18 },
    { gene: "MSC1", value: 17 },
    { gene: "ANKRD34-ANKRD35", value: 16 },
    { gene: "BRMS2", value: 15 },
    { gene: "HLA-DQB2", value: 15 },
    { gene: "UGT2A", value: 14 },
  ];

  const caddScore = [
    { name: "Low Impact Variants", value: 38.5 },
    { name: "High Impact Variants", value: 61.5 },
  ];

  const COLORS = ["#FF8042", "#4666FF"];

  return (
    <>
      <div className="w-full bg-white dark:bg-slate-950 p-4 rounded-xl mt-2">
        <div className="my-4">
          <Disclaimer />
        </div>

        <div className="space-y-2 mb-3 border-b pb-3">
          <h1 className="text-xs font-bold text-gray-900 dark:text-white">Importance Notice</h1>
          <p className="text-xs text-justify text-gray-700 dark:text-gray-300 mb-2">
            As we are yet in the development stage of the <span className="font-bold">R.A.V.I.D.</span> platform, we are currently testing algorithms to return
            back a completed personalized{" "}
            <Link href="/dna/analysis" className="text-blue-600 dark:text-blue-500 underline">
              DNA analysis
            </Link>{" "}
            from raw{" "}
            <Link href="/genome/sequencing" className="text-blue-600 dark:text-blue-500 underline">
              genome sequencing
            </Link>{" "}
            data.
          </p>
          <p className="text-xs text-justify text-gray-700 dark:text-gray-300">
            In the future on this page, you will be able to view your own{" "}
            <Link href="/dna" className="text-blue-600 dark:text-blue-500 underline">
              DNA
            </Link>{" "}
            Dashboard. We are currently testing algorithms to return back a completed personalized DNA analysis from raw genome sequencing data. Below is a
            visual representation of what to expect with the rapid advances of technology.
          </p>
        </div>

        <div className="mb-6">
          <h1 className="text-sm font-medium text-gray-900 dark:text-white">Whole Exome Sequencing (WES) Summary</h1>
          {/* <p className="text-xs text-muted-foreground">
            Detailed analysis of your genetic data
          </p> */}
        </div>

        <div className="space-y-6 bg-gray-50 dark:bg-slate-900 p-4 rounded-lg">
          {/* Summary Stats */}
          <div className="space-y-2">
            <div className="text-xs text-gray-800 dark:text-gray-200">Total Variants: {summaryStats.totalVariants}</div>
            <div className="text-xs text-gray-800 dark:text-gray-200">
              Rare Variants: {summaryStats.rareVariants} (Variant with a global allele frequency below 0.01%)
            </div>
            <div className="text-xs text-gray-800 dark:text-gray-200">Pathogenic Variants: {summaryStats.pathogenicVariants}</div>
          </div>

          {/* Top Diseases */}
          <div>
            <h2 className="text-xs font-semibold mb-4 text-gray-900 dark:text-white">Top Diseases(possibility):</h2>
            <div className="flex flex-wrap gap-2">
              {diseases.map((disease, index) => (
                <Chip key={index} variant="flat" className="text-xs">
                  {disease}
                </Chip>
              ))}
            </div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - CADD Score Card */}
            <div className="lg:row-span-2">
              <Card className="p-6 h-[400px] bg-white dark:bg-slate-950">
                <h3 className="text-xs font-semibold border-b pb-2 mb-4 text-gray-900 dark:text-white">CADD score Prediction</h3>
                <div className="flex justify-center items-center w-full h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie data={caddScore} cx="50%" cy="50%" innerRadius={45} outerRadius={90} fill="#8884d8" paddingAngle={0} dataKey="value" strokeWidth={0}>
                        {caddScore.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} stroke="none" />
                        ))}
                      </Pie>
                      <Tooltip content={<PieChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 space-y-2">
                  <div className="text-xs space-y-1 text-gray-800 dark:text-gray-200">
                    <p className="font-medium text-xs">CADD Reference:</p>
                    <p className="text-xs">0-10: Generally benign or likely neutral.</p>
                    <p className="text-xs">10-20: Potentially deleterious but not extreme.</p>
                    <p className="text-xs">20-40+: Likely highly deleterious with significant functional impact.</p>
                  </div>
                </div>
              </Card>

              <div className="mt-4">
                <h3 className="text-xs border-b pb-2 mb-4 text-gray-900 dark:text-white">Easy Access Options:</h3>
                <div className="flex items-center gap-2">
                  <Download className="text-xs p-1 rounded-md cursor-pointer" />
                  <ExternalLink className="text-xs p-1 rounded-md cursor-pointer" />
                  <Printer className="text-xs p-1 rounded-md cursor-pointer" />
                </div>
              </div>
            </div>

            {/* Right Column - Gene Frequency Charts */}
            <Card className="p-6 bg-white dark:bg-slate-950">
              <h3 className="text-xs font-semibold border-b pb-2 mb-4 text-gray-900 dark:text-white">Top 10 Genes With Allele Frequency Less Than 0.01</h3>
              <div className="w-full">
                <ChartContainer config={chartConfig}>
                  <BarChart data={geneFrequencyData} margin={{ top: 10, right: 0, left: 10, bottom: 60 }}>
                    <CartesianGrid horizontal={true} vertical={false} strokeDasharray="3 3" stroke="#e5e7eb" strokeWidth={0.5} />
                    <XAxis
                      dataKey="gene"
                      tickLine={false}
                      tickMargin={40}
                      axisLine={false}
                      fontSize={10}
                      angle={-45}
                      textAnchor="end"
                      interval={0}
                      label={{
                        value: "Gene",
                        position: "bottom",
                        offset: -15,
                        fontSize: 15,
                        fontWeight: "semibold",
                        color: "#ffffff",
                      }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      fontSize={10}
                      tickMargin={10}
                      label={{
                        value: "Variant Count",
                        angle: -90,
                        position: "left",
                        offset: 0,
                        fontSize: 15,
                        fontWeight: "bold",
                        color: "#ffffff",
                      }}
                    />
                    <Tooltip cursor={false} content={<ChartTooltipContent />} />
                    <Bar dataKey="value" fill="#3b82f6" radius={[4, 4, 0, 0]} barSize={30} />
                  </BarChart>
                </ChartContainer>
              </div>
            </Card>

            <Card className="p-6 bg-white dark:bg-slate-950">
              <h3 className="text-xs font-semibold border-b pb-2 mb-4 text-gray-900 dark:text-white">Top 10 Genes With Allele Frequency Less Than 0.05</h3>
              <div className="w-full">
                <ChartContainer config={chartConfig}>
                  <BarChart data={geneFrequencyData} margin={{ top: 10, right: 10, left: 20, bottom: 60 }}>
                    <CartesianGrid horizontal={true} vertical={false} strokeDasharray="3 3" stroke="#e5e7eb" strokeWidth={0.5} />
                    <XAxis
                      dataKey="gene"
                      tickLine={false}
                      tickMargin={40}
                      axisLine={false}
                      fontSize={10}
                      angle={-45}
                      textAnchor="end"
                      interval={0}
                      label={{
                        value: "Gene",
                        position: "bottom",
                        offset: -15,
                        fontSize: 15,
                        fontWeight: "semibold",
                        color: "#ffffff",
                      }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      fontSize={10}
                      tickMargin={10}
                      label={{
                        value: "Variant Count",
                        angle: -90,
                        position: "left",
                        offset: 0,
                        fontSize: 15,
                        fontWeight: "bold",
                        color: "#ffffff",
                      }}
                    />
                    <Tooltip cursor={false} content={<ChartTooltipContent />} />
                    <Bar dataKey="value" fill="#3b82f6" radius={[4, 4, 0, 0]} barSize={30} />
                  </BarChart>
                </ChartContainer>
              </div>
            </Card>
          </div>

          {/* Add the specific disclaimer at the bottom */}
        </div>
        <div className="mt-8 text-xs w-full text-gray-700 dark:text-gray-400 bg-gray-50 dark:bg-slate-900 px-4 py-3 rounded-lg">
          <p className="leading-relaxed">
            <span className="font-semibold">Disclaimer:</span> Please note that all the information displayed here is subject to computational and algorithimic
            errors. There is also a strong probability of false positives. It is highly recommended that you understand the values and information displayed
            with your trusted doctor/ care provider and your legal guardian if under the legal age as established in your country of residence and/ or
            citizenship. By using this information you assume all liability of disclosing your confidential information.
          </p>
        </div>
      </div>
    </>
  );
};

export default GeneticSummaryPage;
