"use client";
import SpinnerLoading from "@/components/SpinnerLoading";
import { useGetUser } from "@/hooks/useUser";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import toast from "react-hot-toast";

const EnterpriseLayout = ({ children }: { children: React.ReactNode }) => {
  const { data: user, isLoading } = useGetUser();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && user) {
      if (user.enterprise_member_role !== "owner") {
        toast.error("You are not authorized to access this page");
        router.push(`/my/${user.user_id}/home`);
      }
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return <SpinnerLoading />;
  }

  if (!user || user.enterprise_member_role !== "owner") {
    return null;
  }

  return <div>{children}</div>;
};

export default EnterpriseLayout;
