"use client";
import SideBar from "@/components/SideBar";
import { useStore } from "@/store/store";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const InnerLayout = ({ children }: { children: React.ReactNode }) => {
  const { user } = useStore();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.replace("/signin");
    }
  }, [user, router]);

  if (!user) {
    return null;
  }

  return (
    <div className="grid grid-cols-15 px-2 pb-20 md:pb-10">
      <div className="w-full sm:col-span-3 hidden lg:block p-2">
        <SideBar />
      </div>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="col-span-full lg:col-span-12 p-2"
      >
        {children}
      </motion.div>
    </div>
  );
};

export default InnerLayout;
