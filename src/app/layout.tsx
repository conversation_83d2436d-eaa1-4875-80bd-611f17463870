import AdminProtected from "@/components/Auth/AdminProtected";
import Background from "@/components/Background";
import Footer from "@/components/Footer";
import Loader from "@/components/Loader";
import { Navbar } from "@/components/navbar";
import RoutePreloader from "@/components/RoutePreloader";
import { fontSans } from "@/config/fonts";
import { siteConfig } from "@/config/site";
import { cn } from "@/lib/utils";
import QueryProvider from "@/providers/queryProvider";
import type { Metadata } from "next";
import { Toaster } from "react-hot-toast";
import { UIProvider } from "../providers/providers";
import "./globals.css";

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  description: siteConfig.description,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html suppressHydrationWarning lang="en">
      <body className={cn(fontSans.className)}>
        <UIProvider>
          <QueryProvider>
            <AdminProtected>
              <div className="flex flex-col min-h-screen">
                <Background />
                <Loader />
                <Navbar />
                <main className="flex-1">{children}</main>
                <RoutePreloader />
                <Toaster
                  toastOptions={{
                    duration: 2000,
                    className: "text-xs",
                  }}
                />
                <Footer />
              </div>
            </AdminProtected>
          </QueryProvider>
        </UIProvider>
      </body>
    </html>
  );
}
