"use client";

import RegisterForm from "@/components/Forms/RegisterForm";

const Page = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-12 p-2 gap-4 max-w-7xl mx-auto">
      <div className="col-span-8 mt-5 max-w-4xl mx-auto">
        <div className="p-4 rounded-lg ">
          <h1 className="text-sm mb-6">Why open a R.A.V.I.D. account?</h1>

          <div className="space-y-6">
            <div>
              <div className="flex gap-2">
                <span className="text-sm leading-relaxed">1.</span>
                <p className="text-justify text-xs leading-relaxed">
                  If you believe in the power of DNA data, you need to have a
                  storage platform where you can have full control over your own
                  genomic data.
                </p>
              </div>
              <p className="mt-1 text-justify text-xs leading-relaxed pl-5">
                R.A.V.I.D. provides you a permission based collaborative
                platform where you are always the owner and decision maker of
                your unique data.
              </p>
            </div>

            <div>
              <div className="flex gap-2">
                <span className="text-sm leading-relaxed">2.</span>
                <p className="text-justify text-xs leading-relaxed">
                  If you are a stakeholder of any of the many critical Medical
                  Communities, we want to empower you with dynamic tools to
                  better serve your patient population globally.
                </p>
              </div>
              <p className="mt-2 text-justify text-xs leading-relaxed pl-5">
                R.A.V.I.D. provides Medical Communities globally with dynamic
                tools & customized Solutions designed to decrease friction and
                increase positive outcomes.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="col-span-full md:col-span-4 border-none mt-10">
        <RegisterForm />
      </div>
    </div>
  );
};

export default Page;
