const Page = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4">
      <div className="space-y-4 sm:space-y-5">
        <h1 className="blue-heading break-words">Collaborate</h1>
        <p className="h2">
          To help augment our mission objective and vision of R.A.V.I.D., we intend to Collaborate with clinics, hospitals, enterprises (to include research
          institutions and related organizations and governments and empower them with next generation digital tools that will enable them to have access to
          critical DNA data at any time, especially when any individual / patient comes into their care for the first time and additionally offer them (DCHR)
          with best of class storage and seamless collaborative functions.
        </p>

        <p className="text-wrap">
          If you are a stakeholder using DNA / genome data to advance positive patient outcomes and you would like to support our journey with your domain
          expertise or you are managing specific DNA / genomic research initiatives or you belong to the executive decision-making leadership at an Institute of
          Excellence we would welcome the opportunity to engage with you.
        </p>

        <p className="text-wrap">
          Together, we can revolutionize the way healthcare institutions handle and utilize DNA data, creating a more efficient and connected future for patient
          care.
        </p>

        <div className="md:hidden mb-4 w-full mt-4 md:-mt-10 h-[1px] bg-gray-200 dark:bg-gray-800"></div>
          <p className="text-center md:text-left">
            For additional information please contact us via email at:{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
    </div>
  );
};

export default Page;
