const Page = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
      <div className="space-y-4 sm:space-y-5">
        <h1 className="blue-heading">About</h1>
        <p className="h2">
          We believe DNA related information (to include full genome sequencing ) will be the digital footprint of personalized medicine of the future and our
          current and immediate focus is to empower the individual / user / patient / caregiver / hospital / clinic with the capabilities to store this data
          with all its complexity in a safe and secure manner while giving the individual the complete ownership of their data to include multiple collaborative
          features at their option.
        </p>

        <p>
          The Mission objective of R.A.V.I.D. is to empower individuals to have ownership, secure access, privacy and a voice for collaboration over their
          digital DNA datasets/ records, globally.
        </p>

        <p>
          Our Vision for R.A.V.I.D. is to enable any individual with direct access and ownership control of their DNA / genomic data & for all of this data to
          be stored seamlessly within their privacy and consent guidelines, to be transformed into collaborative research (to include large language learning
          models using AI algorithms/ tools) at their option / consent with privacy, security and interoperability as the primary features within our envisioned
          products and services.
        </p>

        <div>
          <div className="md:hidden mb-4 w-full mt-4 md:-mt-10 h-[1px] bg-gray-200 dark:bg-gray-800"></div>
          <p className="text-center md:text-left">
            For additional information please contact us via email at:{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Page;
