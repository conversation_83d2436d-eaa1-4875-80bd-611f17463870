const Page = () => {
  return (
    <div className="container mx-auto p-8 max-w-7xl">
      <div className="space-y-6">
        <section>
          <h2 className="text-xs font-medium mb-2 text-blue-600 dark:text-blue-400">Definition & Scope</h2>
          <p className="text-xs text-justify">
            The genome is the complete set of genetic material of an organism. It includes all genes and non-coding DNA sequences. The genome encompasses coding
            sequences (genes that code for proteins) and non-coding DNA (regulatory elements, introns, telomeres, transposons) that maintain chromosome
            structure.
          </p>
        </section>

        <section>
          <h2 className="text-xs font-medium mb-2 text-blue-600 dark:text-blue-400">Function & Variation</h2>
          <p className="text-xs text-justify">
            The genome provides the entire genetic information needed for development, functioning, and reproduction of an organism. Each species has its own
            unique genome, with variation existing within species (e.g., human genome vs. chimpanzee genome, or variation among humans).
          </p>
        </section>

        <section>
          <h2 className="text-xs font-medium mb-2 text-blue-600 dark:text-blue-400">DNA vs. Genome</h2>
          <p className="text-xs text-justify">
            While DNA is a specific type of nucleic acid molecule, the genome is an abstract concept referring to the entire DNA content. DNA sequencing focuses
            on reading nucleotide sequences, whereas genome sequencing involves reading the entire genetic code of an organism. In essence, DNA is the material
            from which the genome is constructed.
          </p>
        </section>
      </div>
    </div>
  );
};

export default Page;
