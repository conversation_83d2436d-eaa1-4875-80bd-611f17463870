const Page = () => {
  return (
    <div className="min-h-screen max-w-7xl mx-auto p-8">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Left Side */}
        <div className="flex-1 space-y-8">
          {/* Header */}
          <div className="space-y-4">
            <h1 className="text-sm font-bold text-blue-600 dark:text-blue-400">
              Genome Sequencing Steps
            </h1>
            <p className="text-xs text-justify leading-relaxed">
              Here are the key steps involved in genome sequencing:
            </p>
          </div>

          {/* Steps */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h2 className="text-xs font-semibold">1. Sample Collection</h2>
              <p className="text-xs text-justify leading-relaxed">
                DNA is collected from a biological sample, typically blood or
                saliva.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">2. DNA Extraction</h2>
              <p className="text-xs text-justify leading-relaxed">
                The DNA is isolated from the cells. This involves breaking down
                the cell membranes to release the DNA, followed by purification
                to remove other cellular components.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">3. DNA Fragmentation</h2>
              <p className="text-xs text-justify leading-relaxed">
                The extracted DNA is fragmented into smaller pieces. This can be
                done mechanically (shearing) or enzymatically (using restriction
                enzymes).
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">4. Library Preparation</h2>
              <p className="text-xs text-justify leading-relaxed">
                The DNA fragments are prepared for sequencing. This includes:
                <br />
                <br />
                Adding adapters (small pieces of known DNA sequences) to the
                ends of the DNA fragments for binding to the sequencing
                platform.
                <br />
                <br />Amplification of the library to ensure enough DNA for
                sequencing.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">5. Sequencing</h2>
              <p className="text-xs text-justify leading-relaxed">
                The prepared DNA library is sequenced using one of several
                technologies:
                <br />
                <br />
                <strong>Sanger Sequencing:</strong> An older method, now mostly
                used for smaller scale sequencing.
                <br />
                <br />
                <strong>Next-Generation Sequencing (NGS):</strong> Including
                methods like Illumina sequencing, which sequences millions of
                fragments in parallel.
                <br />
                <br />
                <strong>Third-Generation Sequencing:</strong> Such as PacBio or
                Oxford Nanopore, which can sequence longer reads directly from
                DNA without amplification.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">6. Data Generation</h2>
              <p className="text-xs text-justify leading-relaxed">
                The sequencing machine reads the sequence of bases (A, T, C, G)
                in each fragment, producing raw sequence data.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">7. Data Analysis</h2>
              <p className="text-xs text-justify leading-relaxed">
                <strong>Alignment:</strong> The sequence reads are aligned to a
                reference human genome to determine where in the genome each
                fragment belongs.
                <br />
                <br />
                <strong>Assembly:</strong> If no reference is used or for de
                novo sequencing, the reads are pieced together to reconstruct
                the genome sequence.
                <br />
                <br />
                <strong>Variant Calling:</strong> Identification of differences
                (variants) from the reference sequence, including SNPs
                (Single Nucleotide Polymorphisms), insertions, deletions, and
                structural variants.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">
                8. Quality Control and Validation
              </h2>
              <p className="text-xs text-justify leading-relaxed">
                The data is checked for accuracy, coverage, and completeness.
                Some findings might be validated using other methods like PCR
                and Sanger sequencing for specific regions.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">9. Data Interpretation</h2>
              <p className="text-xs text-justify leading-relaxed">
                Bioinformatic analysis to interpret the genetic data, which
                might involve looking for disease-associated variants, studying
                genetic traits, or understanding evolutionary biology.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">
                10. Data Storage and Formats
              </h2>
              <p className="text-xs text-justify leading-relaxed">
                The sequenced data is stored, often in formats like FASTQ or BAM
                for raw data and VCF for variant calls. This data can be shared
                through databases or used for further research.
              </p>
            </div>
          </div>

          {/* Footer Note */}
          <p className="text-xs text-justify leading-relaxed">
            This process can be quite resource-intensive, requiring
            sophisticated equipment, computational resources, and bioinformatics
            expertise. The exact steps might vary slightly depending on the
            specific goals of the sequencing, the sequencing technology
            employed, and the scale of the study (in the event of more than an
            individual sequence).
          </p>
        </div>
      </div>
    </div>
  );
};

export default Page;
