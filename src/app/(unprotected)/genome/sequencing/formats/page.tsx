const Page = () => {
  return (
    <div className="min-h-screen max-w-7xl mx-auto p-8">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Left Side */}
        <div className="flex-1 space-y-8">
          {/* Header */}
          <div className="space-y-4">
            <h1 className="text-sm font-bold text-blue-600 dark:text-blue-400">Genome Sequencing Formats</h1>
            <p className="text-xs text-justify leading-relaxed">
              Genome sequencing involves various file formats, each serving a specific purpose in the data storage, analysis, and visualization process. Here
              are the primary types of files used in genome sequencing:
            </p>
          </div>

          {/* File Formats */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h2 className="text-xs font-semibold">1. FASTQ (.fastq, .fq, or .fq.gz)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Stores both the nucleotide sequence and the quality scores associated with each base called during
                sequencing.
                <br />
                <span className="font-bold">Structure:</span> Each sequence entry consists of four lines: sequence identifier, sequence, a '+' separator, and
                quality scores.
                <br />
                <span className="font-bold">Use:</span> Initial raw data from sequencing machines; used for quality control, alignment, and variant calling.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">2. FASTA (.fasta or .fa)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Stores nucleotide or protein sequences in a simple format.
                <br />
                <span className="font-bold">Structure:</span> Each sequence starts with a single-line description preceded by {">"} and followed by multiple
                lines of sequence data.
                <br />
                <span className="font-bold">Use:</span> Reference sequences, assembled genomes, or transcriptomes.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">3. BAM (Binary Alignment/Map, .bam)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Compressed version of SAM format for storing alignment data.
                <br />
                <span className="font-bold">Structure:</span> Binary, compressed format of SAM; includes headers and alignment records.
                <br />
                <span className="font-bold">Use:</span> Storing aligned reads to a reference genome, used in downstream analyses like variant calling.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">4. SAM (Sequence Alignment/Map, .sam)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Text version of BAM, used for representing alignments.
                <br />
                <span className="font-bold">Structure:</span> Tab-delimited text with headers and alignment information for each read.
                <br />
                <span className="font-bold">Use:</span> Human-readable alignment data, though BAM is preferred for storage and processing due to its compressed
                nature.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">5. CRAM (Compressed Reference-oriented Alignment/Map, .cram)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Highly compressed version of BAM that stores only differences from a reference sequence.
                <br />
                <span className="font-bold">Structure:</span> Similar to BAM but with enhanced compression techniques.
                <br />
                <span className="font-bold">Use:</span> Efficient storage of alignment data, especially useful for large datasets or when storage space is a
                concern.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">6. VCF (Variant Call Format, .vcf)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Stores genetic variations, including SNPs, insertions, deletions, etc.
                <br />
                <span className="font-bold">Structure:</span> Tab-delimited text file with a header describing the format and data lines for each variant.
                <br />
                <span className="font-bold">Use:</span> Variant calling and annotation, storage of genetic differences between individuals or against a
                reference.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">7. GFF/GTF (General Feature Format/Gene Transfer Format, .gff/.gtf)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Annotates genomic features like genes, exons, and transcripts.
                <br />
                <span className="font-bold">Structure:</span> Tab-delimited; GFF3 is more flexible, while GTF is specific for gene models.
                <br />
                <span className="font-bold">Use:</span> Gene annotation, visualization, and integration with genomic data.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">8. BED (Browser Extensible Data, .bed)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Describes genomic regions in a simple format.
                <br />
                <span className="font-bold">Structure:</span> Tab-delimited text file with chromosomal coordinates and feature names.
                <br />
                <span className="font-bold">Use:</span> Annotation, visualization in genome browsers, and analysis of genomic intervals.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">9. BigWig (.bw)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Efficient storage and visualization of continuous data like coverage or signal intensity across a
                genome.
                <br />
                <span className="font-bold">Structure:</span> Binary format, indexed for quick access.
                <br />
                <span className="font-bold">Use:</span> For displaying dense, continuous data in genome browsers like UCSC or IGV.
              </p>
            </div>

            <div className="space-y-4">
              <h2 className="text-xs font-semibold">10. 2bit (.2bit)</h2>
              <p className="text-xs text-justify leading-relaxed">
                <span className="font-bold">Purpose:</span> Compressed binary format for storing whole genome sequences.
                <br />
                <span className="font-bold">Structure:</span> Two bits per nucleotide for A, C, G, T; other bases encoded separately.
                <br />
                <span className="font-bold">Use:</span> Storage of reference genomes, where space efficiency is crucial.
              </p>
            </div>
          </div>

          {/* Footer Note */}
          <p className="text-xs text-justify leading-relaxed">
            These file formats are integral to the bioinformatics pipeline, each playing a role from data acquisition to analysis and visualization. The choice
            of format often depends on the specific needs of the analysis, storage constraints, and the software tools being used.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Page;
