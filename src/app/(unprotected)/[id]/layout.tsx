"use client";
import SideBar from "@/components/SideBar";
import { useGetUser } from "@/hooks/useUser";

const InnerLayout = ({ children }: { children: any }) => {
  const { data: user } = useGetUser();
  return (
    <div className="grid grid-cols-15 min-h-screen px-2 gap-2">
      {user && (
        <div className="w-full sm:col-span-3 hidden lg:block p-2">
          <SideBar />
        </div>
      )}
      <div className={`col-span-full ${user ? "lg:col-span-12" : "col-span-full"} p-2`}>{children}</div>
    </div>
  );
};

export default InnerLayout;
