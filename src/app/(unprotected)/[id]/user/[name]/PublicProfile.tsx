"use client";

import Ads from "@/components/Ads";
import SocialMediaSection from "@/components/Forms/PublicProfile/SocialMediaSection";
import PublicProfileActionBtns from "@/components/PublicProfileActionBtns";
import { useGetUser, useUpdateUser } from "@/hooks/useUser";
import { Button } from "@heroui/button";
import { Card, CardBody, CardHeader, cn, Modal, Modal<PERSON>ontent, Modal<PERSON>ooter, ModalHeader } from "@heroui/react";
import { changeLanguage } from "i18next";
import { BadgeCheck, Check, Copy, MapPin, Stethoscope } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";

type PublicProfileProps = {
  id: string;
  name: string;
  initialProfile: any;
  initialCategories: any[];
  isVerified: boolean;
  hasVerifiedProfile: boolean;
};

const PublicProfile = ({ id, name, initialProfile, initialCategories, isVerified, hasVerifiedProfile }: PublicProfileProps) => {
  const { t } = useTranslation();
  const [copy, setCopy] = useState(false);
  const [activeTab, setActiveTab] = useState("");
  const router = useRouter();

  const { data: user } = useGetUser();
  const { mutate: updateProfile } = useUpdateUser();
  const categoryRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const [showUnpublishDialog, setShowUnpublishDialog] = useState(false);

  // Use server-provided data
  const [publicProfile] = useState(initialProfile);
  const [categories] = useState(initialCategories);
  const [isSuccess] = useState(!!initialCategories);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedLanguage = localStorage.getItem("selectedLanguage");
      if (savedLanguage) {
        changeLanguage(savedLanguage);
      }
    }
  }, []);

  const [isCurrentUserProfile, setIsCurrentUserProfile] = useState(false);

  useEffect(() => {
    // Check if the profile being viewed belongs to the current user
    if (user?.user_id && id) {
      // Convert both IDs to strings for consistent comparison
      const currentUserId = String(user.user_id);
      const profileId = String(id);
      setIsCurrentUserProfile(currentUserId === profileId);
    }
  }, [user?.user_id, id]);

  const copyLink = () => {
    const origin = window.location.origin;
    // Use the props id and name if not current user's profile, otherwise use current user's details
    const linkId = isCurrentUserProfile ? user?.user_id : id;
    const linkUsername = isCurrentUserProfile ? user?.username : name;
    navigator.clipboard.writeText(`${origin}/${linkId}/user/${linkUsername}`);
    setCopy(true);
    toast.success("Copied to clipboard");
    setTimeout(() => {
      setCopy(false);
    }, 3000);
  };

  const scrollToCategory = (categoryName: string) => {
    categoryRefs.current[categoryName]?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    setActiveTab(categoryName);
  };

  const hasValidContent = (content: any) => {
    if (!content || !content.content) return false;

    const contentValues = Object.entries(content.content);

    return contentValues.some(([key, value]) => {
      if (key === "title") return false;
      return value && String(value).trim() !== "";
    });
  };

  const capitalizeFirstLetter = (str: string) => {
    return str
      .split(" ")
      .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const categoriesWithContent = categories?.filter((category: any) => {
    if (category.hidden || !category.contents || !category.contents.length) {
      return false;
    }

    return category.contents.some((content: any) => hasValidContent(content));
  });

  const handleUnpublish = async () => {
    try {
      await updateProfile({ is_public_profile: false });
      router.push(`/my/${user?.user_id}`);
    } catch (error) {
      console.error("Error unpublishing profile:", error);
    }
    setShowUnpublishDialog(false);
  };

  if (isSuccess)
    return (
      <div className="w-full">
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-4 px-2 sm:px-4 mx-auto">
          {/* Main content wrapper */}
          <div className={`${user ? "xl:col-span-10" : "xl:col-span-9 xl:col-start-2"}`}>
            <div>
              <div className="flex flex-col items-center sm:items-start sm:flex-row gap-9 sm:gap-3 w-full">
                {/* Profile Card */}
                <Card
                  className={`
                shadow-xl rounded-xl border-none backdrop-blur-xs dark:bg-[#060C1B]
                overflow-hidden
                sm:sticky top-4 h-fit w-[300px] sm:w-auto min-w-[220px] md:min-w-[270px]
              `}
                >
                  <CardBody className="bg-transparent p-3 max-w-[300px]">
                    <div className="flex flex-col items-center gap-4">
                      {/* Profile Image */}
                      <div className="flex flex-col items-center gap-2">
                        <div
                          className={cn("w-24 h-24 rounded-full overflow-hidden", {
                            "bg-gradient-to-r from-yellow-400 to-white p-[1px]": isCurrentUserProfile
                              ? publicProfile?.paid_for_verification
                              : publicProfile?.paid_for_verification && !isVerified,
                            "bg-gradient-to-r from-yellow-400 to-white p-[2px]": !isCurrentUserProfile && publicProfile?.paid_for_verification && isVerified,
                            "ring-2 ring-gray-700": isCurrentUserProfile ? !publicProfile?.paid_for_verification : !publicProfile?.paid_for_verification,
                          })}
                        >
                          <div className="w-full h-full rounded-full overflow-hidden">
                            {publicProfile?.profile_picture ? (
                              <Image
                                src={publicProfile?.profile_picture}
                                alt="Public Profile Picture"
                                width={96}
                                height={96}
                                className="object-cover w-full h-full"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-gray-800 rounded-full">
                                <div className="animate-pulse">
                                  <div className="w-16 h-16 bg-gray-700 rounded-full"></div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        {((isCurrentUserProfile && publicProfile?.paid_for_verification) ||
                          (!isCurrentUserProfile && publicProfile?.paid_for_verification && isVerified)) && (
                          <div className="bg-white px-4 py-1 rounded-full">
                            <div className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 text-xs font-bold">
                              Verified
                            </div>
                          </div>
                        )}
                      </div>
                      {user?.user_id === id && (
                        <Button size="sm" className="bg-[#531143] hover:bg-[#4d1340] text-white w-fit">
                          Verify Profile
                        </Button>
                      )}

                      {/* Profile Info */}
                      <div className="flex flex-col items-center w-full gap-2">
                        <div className="text-center mb-2">
                          <h1 className="text-sm font-bold flex items-center gap-1">
                            {publicProfile?.title || (
                              <>{[publicProfile?.first_name, publicProfile?.middle_name, publicProfile?.last_name].filter(Boolean).join(" ")}</>
                            )}
                            {(isCurrentUserProfile ? user?.is_id_verified : publicProfile?.paid_for_verification && isVerified) && (
                              <BadgeCheck className="h-4 w-4 text-white animate-in zoom-in duration-300 flex-shrink-0" />
                            )}
                          </h1>
                        </div>
                        <div className="flex flex-col gap-2 w-full mb-4">
                          {publicProfile?.username && <div className="text-gray-400 text-xs">@{publicProfile.username}</div>}

                          <div className="flex items-center gap-2">
                            {!copy ? (
                              <Copy
                                onClick={copyLink}
                                className="h-4 w-4 text-gray-500 cursor-pointer transition-all duration-300 hover:scale-110 flex-shrink-0"
                              />
                            ) : (
                              <Check className="h-4 w-4 text-green-400 animate-in zoom-in duration-300 flex-shrink-0" />
                            )}
                            <Link
                              className="text-blue-500 text-xs"
                              href={`/${publicProfile?.user_id}/user/${publicProfile?.username}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              My Url
                            </Link>
                          </div>

                          {publicProfile?.locations && (
                            <span className="text-xs text-gray-200 flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              {publicProfile.locations}
                            </span>
                          )}

                          {publicProfile?.speciality && (
                            <span className="text-xs text-gray-200 flex items-center gap-2">
                              <Stethoscope className="h-4 w-4" />
                              {publicProfile.speciality}
                            </span>
                          )}
                        </div>
                        {/* Social Media Icons */}
                        <SocialMediaSection />
                        {/* Bio */}
                        {publicProfile?.bio && <p className="text-xs py-2 text-gray-400 text-justify w-full mb-4">{publicProfile?.bio}</p>}
                        {/* Action Buttons */}
                        <PublicProfileActionBtns publicProfile={publicProfile} user={user} isCurrentUserProfile={isCurrentUserProfile} />
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Main Content Section */}
                <div className={`flex-grow w-full overflow-y-auto sm:px-4 max-w-full`}>
                  {/* Tab Navigation */}
                  {categoriesWithContent.length > 0 && (
                    <div className="bg-transparent rounded-xl w-full">
                      <nav className="flex flex-wrap gap-2 pb-1">
                        {categoriesWithContent.map((category: any, index: number) => (
                          <button
                            key={index}
                            className={cn(
                              "relative whitespace-nowrap py-2 px-4 font-medium text-xs cursor-pointer transition-all duration-200 bg-white dark:bg-slate-900 rounded-b-none rounded-md overflow-hidden border-b-2",
                              activeTab === category.name
                                ? "text-gray-900 dark:text-white border-blue-500"
                                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 border-gray-300 dark:border-gray-500"
                            )}
                            onClick={() => scrollToCategory(category.name)}
                          >
                            {capitalizeFirstLetter(category.name)}
                          </button>
                        ))}
                      </nav>
                    </div>
                  )}

                  {/* Categories Cards */}
                  <div className="space-y-4 mt-2">
                    {categoriesWithContent.map((category: any, index: number) => (
                      <Card
                        key={index}
                        ref={(el) => {
                          if (el) categoryRefs.current[category.name] = el;
                        }}
                        className="shadow-xl rounded-xl transition-all duration-300 dark:bg-[#060C1B]"
                      >
                        <CardHeader className="p-2 sm:p-3 border-b dark:border-gray-700">
                          <h1 className="text-xs">{capitalizeFirstLetter(category.name)}</h1>
                        </CardHeader>
                        <CardBody className="p-2 sm:p-4 space-y-4">
                          {category?.contents
                            ?.filter((content: any) => hasValidContent(content))
                            ?.map((content: any, contentIndex: number) => (
                              <div key={contentIndex} className="p-3 rounded-lg">
                                {content.content.title && <h3 className="text-xs font-semibold mb-2">{content.content.title}</h3>}
                                <div className="space-y-2">
                                  {Object.entries(content.content)
                                    .filter(([key, value]) => key !== "title" && value && String(value).trim() !== "")
                                    .map(([key, value], i) => (
                                      <p key={i} className="flex items-center text-xs gap-2">
                                        <span className="h-1.5 w-1.5 rounded-full bg-blue-500"></span>
                                        {value as string}
                                      </p>
                                    ))}
                                </div>
                              </div>
                            ))}
                        </CardBody>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Ads Section - Only visible on XL screens */}
          <div className="hidden xl:block xl:col-span-2 xl:sticky top-4 h-fit">
            <Ads />
          </div>
        </div>

        <Modal className="bg-slate-950" isOpen={showUnpublishDialog} onOpenChange={setShowUnpublishDialog}>
          <ModalContent className="sm:max-w-[425px]">
            <ModalHeader className="flex flex-col gap-2">
              <h1>Unpublish Profile</h1>
              <p>Are you sure you want to unpublish your profile? This will make your profile private and inaccessible to others.</p>
            </ModalHeader>
            <ModalFooter className="flex gap-2">
              <Button type="button" size="sm" color="danger" onPress={() => setShowUnpublishDialog(false)}>
                Cancel
              </Button>
              <Button type="button" variant="bordered" size="sm" onPress={handleUnpublish}>
                Unpublish
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    );
};

export default PublicProfile;
