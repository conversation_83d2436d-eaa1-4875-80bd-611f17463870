"use client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
// import useAuthStore from "@/store/store";

const Page = () => {
  const router = useRouter();
  //   const setUser = useAuthStore((state) => state.setUser);

  useEffect(() => {
    const url = new URL(window.location.href);
    const accessToken = url.searchParams.get("access_token");
    const refreshToken = url.searchParams?.get("refresh_token");
    const name = url.searchParams?.get("name");
    const email = url.searchParams?.get("email");
    const image = url.searchParams?.get("profile_image_url");

    const user_id = url.searchParams?.get("user_id");
    const first_name = url.searchParams?.get("first_name");
    const last_name = url.searchParams?.get("last_name");

    if (accessToken && refreshToken) {
      localStorage.setItem("accessToken", accessToken);
      localStorage.setItem("refreshToken", refreshToken);
      localStorage.setItem("name", name as string);
      localStorage.setItem("email", email as string);
      localStorage.setItem("image", image as string);
      localStorage.setItem("user_id", user_id as string);
      localStorage.setItem("first_name", first_name as string);
      localStorage.setItem("last_name", last_name as string);

      // Update Zustand store directly
      //   setUser({
      //     first_name: first_name as string,
      //     last_name: last_name as string,
      //     user_id: user_id as string,
      //     email: email as string,
      //     image: image as string,
      //   });

      console.log("SSO login successful", accessToken, refreshToken);
      router.push(`/my/edit/${user_id}`);
    } else {
      router.push("/signup");
    }
  }, [router]);

  return <div>Processing SSO login...</div>;
};

export default Page;
