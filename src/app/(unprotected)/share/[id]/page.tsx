"use client";

import { useSearchParams } from "next/navigation";
import Image from "next/image";
import { useShareQR } from "@/hooks/settings-dashboard/useShareQR";
import { Card } from "@heroui/react";
import { Check } from "@/components/icons";

export default function SharePage() {
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const { data: verifiedUserData, isLoading, isError } = useShareQR(token || "");

  // Show error state
  if (isError || !verifiedUserData) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center">
        <div className="text-white">Invalid or expired token</div>
      </div>
    );
  }

  const age = verifiedUserData?.user?.basic_information?.dob
    ? new Date().getFullYear() - new Date(verifiedUserData.user.basic_information.dob).getFullYear()
    : null;

  return (
    <div className="min-h-screen  dark:bg-slate-950">
      <div className="p-4 md:p-8">
        <div className="space-y-6 md:space-y-8">
          <div className="flex flex-row items-start gap-4 sm:gap-6">
            <div className="relative w-24 h-24 sm:w-32 sm:h-32 flex-shrink-0">
              {verifiedUserData?.user?.basic_information?.private_profile_picture ? (
                <div className="w-full h-full rounded-full dark:bg-blue-500/20 flex items-center justify-center overflow-hidden">
                  <Image src={verifiedUserData.user.basic_information.private_profile_picture} alt="Profile" fill className="object-cover rounded-full" />
                  <span className="text-3xl opacity-0 initials-fallback">
                    {verifiedUserData?.user?.basic_information?.first_name?.[0] ?? ""}
                    {verifiedUserData?.user?.basic_information?.last_name?.[0] ?? ""}
                  </span>
                </div>
              ) : (
                <div className="w-full h-full rounded-full bg-blue-500/20 flex items-center justify-center">
                  <span className="text-3xl">
                    {verifiedUserData?.user?.basic_information?.first_name?.[0] ?? ""}
                    {verifiedUserData?.user?.basic_information?.last_name?.[0] ?? ""}
                  </span>
                </div>
              )}
            </div>

            {/* Name and Details Section */}
            <div className="flex-1 space-y-3">
              {/* Name */}
              <div>
                <span className="flex items-center gap-2">
                  {verifiedUserData?.user?.basic_information?.first_name} {verifiedUserData?.user?.basic_information?.last_name}
                  {verifiedUserData?.user?.basic_information?.paid_for_verification && <Check/>}
                </span>
                <p>
                  {verifiedUserData?.user?.medical_information?.gender}
                  {age ? `, ${age} Years` : ""}
                </p>
              </div>

              {/* Details */}
              <div className="space-y-2">
                {verifiedUserData?.user?.medical_information?.blood_group && (
                  <div className="flex gap-2">
                    <span className="muted">Blood Type:</span>
                    <span className="text-xs">{verifiedUserData.user.medical_information.blood_group}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <span className="muted">DOB:</span>
                  <span className="text-xs">{verifiedUserData?.user?.basic_information?.dob}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Information Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            {/* Contact Information */}
            {(verifiedUserData?.user?.address_information || verifiedUserData?.user?.basic_information?.phone_number) && (
              <Card className="dark:bg-slate-900 border border-slate-800 p-4 md:p-6 rounded-xl">
                <h2 className="text-sm font-medium mb-3 md:mb-4">Address & Contact Information</h2>
                <div className="space-y-3">
                  {verifiedUserData?.user?.address_information && (
                    <div className="text-xs">
                      <span className="text-gray-400">Address: </span>
                      <span>
                        {[
                          verifiedUserData.user.address_information.address,
                          verifiedUserData.user.address_information.city,
                          verifiedUserData.user.address_information.state,
                          verifiedUserData.user.address_information.zipcode,
                        ]
                          .filter(Boolean)
                          .join(", ")}
                      </span>
                    </div>
                  )}
                  {verifiedUserData?.user?.basic_information?.phone_number && (
                    <div className="text-xs">
                      <span className="text-gray-400">Phone: </span>
                      <span>{verifiedUserData.user.basic_information.phone_number}</span>
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* Emergency Contact Information */}
            {verifiedUserData?.user?.emergency_information?.contact_information?.length > 0 && (
              <Card className="bg-slate-900 border border-slate-800 p-4 md:p-6 rounded-xl">
                <h2 className="text-sm font-medium mb-3 md:mb-4">Emergency Contact Information</h2>
                {verifiedUserData.user.emergency_information.contact_information.map((contact: EmergencyContact, index: number) => (
                  <div key={contact.id}>
                    <div className="space-y-2">
                      {contact.contact_name && (
                        <div className="text-xs">
                          <span className="text-gray-400">Name: </span>
                          <span>{contact.contact_name}</span>
                        </div>
                      )}
                      {contact.email && (
                        <div className="text-xs">
                          <span className="text-gray-400">Email: </span>
                          <span>{contact.email}</span>
                        </div>
                      )}
                      {contact.phone_number && (
                        <div className="text-xs">
                          <span className="text-gray-400">Phone: </span>
                          <span>{contact.phone_number}</span>
                        </div>
                      )}
                      {contact.relationship && (
                        <div className="text-xs">
                          <span className="text-gray-400">Relationship: </span>
                          <span>{contact.relationship}</span>
                        </div>
                      )}
                    </div>
                    {index < verifiedUserData.user.emergency_information.contact_information.length - 1 && (
                      <div className="my-4 border-b border-slate-800"></div>
                    )}
                  </div>
                ))}
              </Card>
            )}

            {/* Emergency Medical Information */}
            {verifiedUserData?.user?.emergency_information?.medical_information && (
              <Card className="bg-slate-900 border border-slate-800 p-4 md:p-6 rounded-xl">
                <h2 className="text-sm font-medium mb-3 md:mb-4">Emergency Medical Information</h2>
                <div className="space-y-4">
                  {verifiedUserData.user.emergency_information.medical_information.allergies?.length > 0 && (
                    <div>
                      <h3 className="text-xs text-gray-400 mb-2">Allergies:</h3>
                      <div className="flex flex-wrap gap-2">
                        {verifiedUserData.user.emergency_information.medical_information.allergies.map((allergy: string, index: number) => (
                          <span key={index} className="bg-gray-700/50 px-3 py-1 rounded-full text-xs">
                            {allergy}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {verifiedUserData.user.emergency_information.medical_information.emergency_medications?.length > 0 && (
                    <div>
                      <h3 className="text-xs text-gray-400 mb-2">Medication:</h3>
                      <div className="flex flex-wrap gap-2">
                        {verifiedUserData.user.emergency_information.medical_information.emergency_medications.map((med: string, index: number) => (
                          <span key={index} className="bg-gray-700/50 px-3 py-1 rounded-full text-xs">
                            {med}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {verifiedUserData.user.medical_information?.blood_group && (
                    <div className="flex gap-2 items-center">
                      <span className="muted">Blood Type:</span>
                      <span className="text-xs">{verifiedUserData.user.medical_information.blood_group}</span>
                    </div>
                  )}

                  {verifiedUserData.user.emergency_information.medical_information.critical_information && (
                    <div className="flex gap-2 items-center">
                      <span className="muted">Miscellaneous:</span>
                      <span className="text-xs">{verifiedUserData.user.emergency_information.medical_information.critical_information}</span>
                    </div>
                  )}

                  {verifiedUserData.user.emergency_information.medical_information.past_admissions && (
                    <div className="flex gap-2 items-center">
                      <span className="muted">Past Admissions:</span>
                      <span className="text-xs">{verifiedUserData.user.emergency_information.medical_information.past_admissions}</span>
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* Medical Team Information */}
            {verifiedUserData?.user?.medical_team?.length > 0 && (
              <Card className="bg-slate-900 border border-slate-800 p-4 md:p-6 rounded-xl">
                <h2 className="text-sm font-medium mb-3 md:mb-4">Medical Team Information</h2>
                <div className="space-y-4">
                  {verifiedUserData.user.medical_team.map((member: MedicalTeamMember, index: number) => (
                    <div key={member.id}>
                      <div className="space-y-1">
                        {member.role && (
                          <div className="text-xs">
                            <span className="font-medium">{member.role}</span>
                          </div>
                        )}
                        <div className="ml-1 space-y-1">
                          {member.name && <p className="text-xs text-gray-400">{member.name}</p>}
                          {member.email && <p className="text-xs text-gray-400">{member.email}</p>}
                          {member.contact_number && <p className="text-xs text-gray-400">{member.contact_number}</p>}
                          {member.affiliation && <p className="text-xs text-gray-400">{member.affiliation}</p>}
                        </div>
                      </div>
                      {index < verifiedUserData.user.medical_team.length - 1 && <div className="my-4 border-b border-slate-800"></div>}
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Insurance Information */}
            {verifiedUserData?.user?.insurances?.length > 0 && (
              <Card className="bg-slate-900 border border-slate-800 p-4 md:p-6 rounded-xl">
                <h2 className="text-sm font-medium mb-4 md:mb-6">Insurance Information</h2>
                <div className="space-y-6">
                  {verifiedUserData.user.insurances.map((insurance: Insurance, index: number) => (
                    <div key={insurance.id}>
                      <div className="space-y-3">
                        {/* Insurance Type as Heading */}
                        <div>
                          <h3 className="text-sm font-medium text-blue-400">
                            {insurance.type ? insurance.type.charAt(0).toUpperCase() + insurance.type.slice(1) : "Insurance"}
                          </h3>
                        </div>

                        {/* Insurance Details */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          {insurance.provider && (
                            <div className="text-xs">
                              <span className="text-gray-400">Provider: </span>
                              <span>{insurance.provider}</span>
                            </div>
                          )}
                          {insurance.policy_number && (
                            <div className="text-xs">
                              <span className="text-gray-400">Policy Number: </span>
                              <span>{insurance.policy_number}</span>
                            </div>
                          )}
                          {insurance.group_number && (
                            <div className="text-xs">
                              <span className="text-gray-400">Group Number: </span>
                              <span>{insurance.group_number}</span>
                            </div>
                          )}
                          {insurance.policy_holder_name && (
                            <div className="text-xs">
                              <span className="text-gray-400">Policy Holder: </span>
                              <span>{insurance.policy_holder_name}</span>
                            </div>
                          )}
                          {insurance.dependent_information && (
                            <div className="text-xs">
                              <span className="text-gray-400">Dependent: </span>
                              <span className="capitalize">{insurance.dependent_information}</span>
                            </div>
                          )}
                          {(insurance.start_date || insurance.end_date) && (
                            <div className="text-xs sm:col-span-2">
                              <span className="text-gray-400">Validity: </span>
                              <span>
                                {insurance.start_date && `From ${insurance.start_date}`}
                                {insurance.end_date && ` to ${insurance.end_date}`}
                              </span>
                            </div>
                          )}
                          {insurance.deductable_amount && (
                            <div className="text-xs">
                              <span className="text-gray-400">Deductible: </span>
                              <span>${insurance.deductable_amount}</span>
                            </div>
                          )}
                          {insurance.copay_amount && (
                            <div className="text-xs">
                              <span className="text-gray-400">Copay: </span>
                              <span>${insurance.copay_amount}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      {index < verifiedUserData.user.insurances.length - 1 && <div className="my-4 border-b border-slate-800"></div>}
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
