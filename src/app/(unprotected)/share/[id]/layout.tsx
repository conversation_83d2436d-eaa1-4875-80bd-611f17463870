"use client";
import SideBar from "@/components/SideBar";
import { motion } from "framer-motion";
import React from "react";

const InnerLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="grid grid-cols-15 h-screen gap-2">
      <div className="col-span-3 h-[80%] hidden lg:block">
        <SideBar />
      </div>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="col-span-full lg:col-span-12  h-[80%]"
      >
        {children}
      </motion.div>
    </div>
  );
};

export default InnerLayout;
