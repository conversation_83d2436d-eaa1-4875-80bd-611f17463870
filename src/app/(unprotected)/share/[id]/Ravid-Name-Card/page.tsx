"use client";

import { useGetCard } from "@/hooks/useCard";
import { convertImageToBase64 } from "@/lib/imageUtils";
import { Card, CardBody } from "@heroui/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

const RavidCardPage = () => {
  const params = useParams();
  const id = params.id as string;
  const [profilePictureBase64, setProfilePictureBase64] = useState<string>("");

  const { data: cardData, isLoading, isError } = useGetCard(id);

  // Convert profile picture to base64 when card data is loaded
  useEffect(() => {
    if (cardData?.profile_picture) {
      convertImageToBase64(cardData.profile_picture).then(setProfilePictureBase64);
    }
  }, [cardData]);

  if (isLoading) {
    return (
      <div className="flex w-full justify-center items-center min-h-screen">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  if (isError || !cardData) {
    return (
      <div className="flex w-full justify-center items-center min-h-screen">
        <div className="text-white">Error loading card data or card not found</div>
      </div>
    );
  }

  // cardData is already the card object, not an array
  const card = cardData;

  // Create full name from first, middle, and last name
  const fullName = [card?.first_name, card?.middle_name, card?.last_name].filter((name) => name && name.trim() !== "").join(" ");

  // Create vCard data with actual card information
  const createVCard = () => {
    // Build vCard fields array
    const vCardFields = ["BEGIN:VCARD", "VERSION:3.0"];
    // Add contact info fields if they exist
    if (fullName) vCardFields.push(`FN:${fullName}`);
    if (card?.professional_title) vCardFields.push(`TITLE:${card.professional_title}`);
    if (card?.phone) vCardFields.push(`TEL:${card.phone}`);
    if (card?.email) vCardFields.push(`EMAIL:${card.email}`);
    if (card?.address) vCardFields.push(`ADR:;;${card.address};;;;`);
    if (card?.url) vCardFields.push(`URL:${card.url}`);
    if (profilePictureBase64) vCardFields.push(`PHOTO;TYPE=PNG;ENCODING=b:${profilePictureBase64}`);
    if (card?.additional_information) vCardFields.push(`NOTE:${card.additional_information}`);
    vCardFields.push("END:VCARD");

    // Join fields with newlines and encode
    const vCardString = vCardFields.join("%0A");

    return `data:text/vcard;charset=utf-8,${vCardString}`;
  };

  return (
    <div className="flex w-full  justify-center">
      <Card className="max-w-3xl w-full bg-slate-950 border border-yellow-800 rounded-lg overflow-hidden">
        <CardBody className="p-0">
          <div className="flex flex-col md:flex-row">
            <div className="p-8 flex flex-col items-center">
              <div className="w-40 h-40 rounded-full overflow-hidden">
                <img src={card?.profile_picture} alt="Profile" className="w-full h-full object-cover" />
              </div>
            </div>

            <div className="flex-1 p-4 md:p-8 space-y-6">
              <div className="rounded-md space-y-2 p-2 relative">
                <a
                  href={createVCard()}
                  download={`${fullName || "contact"}.vcf`}
                  className="inline-block bg-yellow-800 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded mb-4 transition-colors"
                >
                  Save Contact
                </a>
                {fullName && <h2 className="text-white">{fullName}</h2>}
                {card.professional_title && <p className="text-gray-300">{card.professional_title}</p>}
              </div>

              <div className="rounded-md space-y-2 p-2 relative">
                {card.address && (
                  <>
                    <p className="text-gray-300 font-bold">Address</p>
                    <p className="text-gray-300">{card.address}</p>
                  </>
                )}
                {card.phone && <p className="text-gray-300 mt-2 font-bold">Phone</p>}
                {card.phone && <p className="text-gray-300">{card.phone}</p>}
                {card.email && <p className="text-gray-300 font-bold">Email</p>}
                {card.email && <p className="text-gray-300">{card.email}</p>}
                {card.additional_information && (
                  <>
                    <p className="text-gray-300 mt-2 font-bold">Additional Information:</p>
                    <p className="text-gray-300">{card.additional_information}</p>
                  </>
                )}
                {card.url && (
                  <p className="text-gray-300 mt-2 font-bold">
                    URL:{" "}
                    <Link href={card.url} target="_blank" className="text-blue-400 hover:text-blue-300">
                      {card.url}
                    </Link>
                  </p>
                )}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default RavidCardPage;
