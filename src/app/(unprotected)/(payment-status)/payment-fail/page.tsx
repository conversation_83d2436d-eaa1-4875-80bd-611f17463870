"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function PaymentFail() {
  const searchParams = useSearchParams();
  const redirect_url = searchParams.get("redirect_url");
  const router = useRouter();

  useEffect(() => {
    if (redirect_url) {
      setTimeout(() => {
        router.push(redirect_url);
      }, 2000);
    }
  }, [redirect_url]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Payment Fail!</h2>
        <p>Redirecting you back...</p>
      </div>
    </div>
  );
}
