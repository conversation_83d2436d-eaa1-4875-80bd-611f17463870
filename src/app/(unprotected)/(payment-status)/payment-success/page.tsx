"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function PaymentSuccess() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirect_url = searchParams.get("redirect_url");
  const user_id = searchParams.get("user_id");

  useEffect(() => {
    if (redirect_url) {
      const timer = setTimeout(() => {
        router.push(redirect_url);
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      // Fallback if no redirect URL is provided
      const timer = setTimeout(() => {
        router.push(`${process.env.NEXT_PUBLIC_BASE_URL}/my/${user_id}/subscription`);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [redirect_url, router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Payment Successful!</h2>
        <p>Redirecting you back{!redirect_url && " to homepage"}...</p>
      </div>
    </div>
  );
}
