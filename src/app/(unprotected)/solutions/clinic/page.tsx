"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import Disclaimer from "@/components/Disclaimer";
import SolutionContactInfo from "@/components/SolutionContactInfo";
import { useClinicSolution } from "@/hooks/useClinicSolution";
import { useAdminAuth } from "@/hooks/settings-dashboard/useAdminAuth";
import SubmitButton from "@/components/SubmitButton";
import { ArrowRightIcon } from "lucide-react";

const Page = () => {
  const { getClinicProfile } = useClinicSolution();
  const { isAdminAuthenticated } = useAdminAuth();
  const router = useRouter();

  const handleCreateClinic = async () => {
    if (!isAdminAuthenticated) {
      toast.error("Please log in to create a clinic solution");
      return;
    }
    try {
      const clinicProfile = await getClinicProfile();
      if (clinicProfile) {
        router.replace(`/my/cs/${clinicProfile?.unique_identifier}/edit`);
      } else {
        toast.error("Failed to create clinic solution");
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <section className="py-4">
      <Disclaimer />
      <div className="h-fit md:min-h-screen max-w-6xl mx-auto px-4 sm:px-6">
        <div className="flex items-center justify-between w-full sm:w-auto mb-4 border-b border-gray-600 pb-4">
          <h1 className="blue-heading mb-2">Clinic Solutions</h1>
          <SubmitButton
            label="Create Clinic Solution"
            isLoadingValue="Creating..."
            size="sm"
            onClick={handleCreateClinic}
            className="bg-blue-600 hover:bg-blue-700 text-white text-xs"
            icon={<ArrowRightIcon className="w-4 h-4" />}
          />
        </div>

        {/* Demo Images Grid */}
        <div className="w-full aspect-video relative mb-4">
          <Image
            src="/images/clinic_solution_landing_page.png"
            alt="Enterprise Demo 1"
            fill
            className="object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow"
            priority
          />
        </div>

        <SolutionContactInfo />
      </div>
    </section>
  );
};

export default Page;
