import { comingSoonSolutions, platformFeatures, solutionsContent, solutionsIntro } from "@/config/solutions/clinic_constants";
import { But<PERSON> } from "@heroui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

import SolutionContactInfo from "@/components/SolutionContactInfo";
import Image from "next/image";

const Page = () => {
  return (
    <div className="min-h-screen max-w-7xl mx-auto py-4 px-4 sm:px-6">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Left Side */}
        <div className="flex-1 space-y-8">
          {/* Header */}
          <div className="space-y-2">
            <div>
              <h1 className="blue-heading mb-1">{solutionsIntro.title}</h1>
              <p className="text-xs text-justify leading-relaxed mb-4 max-w-full sm:max-w-[86%]">{solutionsIntro.description}</p>
            </div>
            <h1 className="blue-heading">Our Solutions</h1>
            <div className="w-full h-[1px] bg-gray-200 dark:bg-gray-800"></div>
          </div>

          {/* Solutions List */}
          <div className="space-y-8">
            {/* Dynamic Solutions */}
            {solutionsContent.map((solution, index) => (
              <div key={index}>
                <div className="space-y-4">
                  <div className="flex flex-col md:flex-row gap-6 items-center">
                    <div className="flex-1 order-2 md:order-1">
                      <Link href={solution.buttonLink ?? ""} prefetch={true} className="flex items-center gap-1">
                        <h2 className="blue-heading hover:underline">{solution.title}</h2>
                        <span className="text-xs text-gray-500 dark:text-gray-400">({solution.status})</span>
                      </Link>
                      {solution.paragraphs.map((paragraph, pIndex) => (
                        <p key={pIndex} className="text-xs text-justify leading-relaxed mb-3">
                          {paragraph}
                        </p>
                      ))}
                      {solution.buttonText && solution.buttonLink && (
                        <Link href={solution.buttonLink}>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white text-xs mt-3">
                            {solution.buttonText} <ArrowRight className="w-3 h-3 inline" />
                          </Button>
                        </Link>
                      )}
                    </div>
                    <div className="md:h-full h-40 md:w-40 w-full order-1 md:order-2 mb-4 md:mb-0">
                      <Image width={160} height={120} src={solution.imageSrc} alt={solution.imageAlt} className=" rounded-lg object-contain w-full h-full" />
                    </div>
                  </div>
                </div>
                <div className="w-full h-[1px] bg-gray-200 dark:bg-gray-800 mt-8"></div>
              </div>
            ))}

            {/* Coming Soon Solutions */}
            <div className="space-y-1">
              {comingSoonSolutions.map((solution, index) => (
                <div key={index} className="space-y-2">
                  <h2 className="text-sm text-blue-600 dark:text-blue-400">
                    {solution}
                    <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">(Coming Soon)</span>
                  </h2>
                </div>
              ))}
            </div>
          </div>

          {/* Features List */}
          <div className="space-y-4">
            <div>
              <div className="flex flex-col md:flex-row gap-6 items-center">
                <div className="flex-1 order-2 md:order-1">
                  <h3 className="text-sm text-blue-600 dark:text-blue-400">{platformFeatures.title}</h3>
                  <div className="space-y-2">
                    <p className="text-xs text-justify leading-relaxed">{platformFeatures.description}</p>
                  </div>
                </div>
                <div className="md:h-20 h-40 md:w-40 w-full order-1 md:order-2 mb-4 md:mb-0">
                  <Image width={160} height={80} src={platformFeatures.imageSrc} alt={platformFeatures.imageAlt} className="rounded-lg object-contain w-full h-full" />
                </div>
              </div>
            </div>

            <div className="w-full mt-4 md:-mt-10 h-[1px] bg-gray-200 dark:bg-gray-800"></div>
          </div>
          <SolutionContactInfo />
        </div>
      </div>
    </div>
  );
};

export default Page;
