"use client";
import { EnterpriseCreateBtn } from "@/components/EntepriseCreateBtn";
import SolutionContactInfo from "@/components/SolutionContactInfo";
import { ArrowRightIcon } from "lucide-react";
import Link from "next/link";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import { useState, useEffect } from "react";

const demoImages = [
  { src: "/images/ravid-solution-images/adminInfo.png", alt: "Enterprise Demo 1", text: "Admin Info" },
  { src: "/images/ravid-solution-images/companyInfo.png", alt: "Enterprise Demo 2", text: "Company Info" },
  { src: "/images/ravid-solution-images/companyInfoImage.png", alt: "Enterprise Demo 3", text: "Company Info with your logo" },
  { src: "/images/ravid-solution-images/license.png", alt: "Enterprise Demo 4", text: "License" },
  { src: "/images/ravid-solution-images/manager.png", alt: "Enterprise Demo 5", text: "License Manager" },
  { src: "/images/ravid-solution-images/subscription.png", alt: "Enterprise Demo 6", text: "Subscription" },
  { src: "/images/ravid-solution-images/billing.png", alt: "Enterprise Demo 7", text: "Billing" },
  { src: "/images/ravid-solution-images/metric.png", alt: "Enterprise Demo 8", text: "Metrics" },
];

const Page = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [activeView, setActiveView] = useState<"main" | "features">("main");
  const [showBreadcrumbs, setShowBreadcrumbs] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % demoImages.length);
    }, 3000);

    return () => clearInterval(timer);
  }, []);

  const handleFeaturesClick = () => {
    setActiveView("features");
    setShowBreadcrumbs(true);
  };

  const handleBackToMain = () => {
    setActiveView("main");
    setShowBreadcrumbs(false);
  };

  return (
    <>
      <div className="h-fit md:min-h-screen max-w-6xl mx-auto p-4 sm:p-6 my-4">
        <section className={activeView === "main" ? "" : "border-b border-gray-600 mb-4"}>
          <div className="flex items-center justify-between w-full sm:w-auto mb-4">
            <div>
              <h1 className="blue-heading mb-2">Enterprise Solutions</h1>
              {showBreadcrumbs ? (
                <Breadcrumbs>
                  <BreadcrumbItem>
                    <span
                      className="text-xs cursor-pointer text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300"
                      onClick={handleBackToMain}
                    >
                      Enterprise Solutions
                    </span>
                  </BreadcrumbItem>
                  <BreadcrumbItem>
                    <span className="text-xs cursor-pointer text-gray-900 dark:text-white transition-colors duration-300 flex items-center gap-1">
                      Features
                    </span>
                  </BreadcrumbItem>
                </Breadcrumbs>
              ) : (
                <button
                  onClick={handleFeaturesClick}
                  className="text-xs flex items-center gap-1 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300"
                >
                  Features <ArrowRightIcon className="w-3 h-3" />
                </button>
              )}
            </div>
            <EnterpriseCreateBtn />
          </div>
        </section>

        {activeView === "main" ? (
          <>
            <section className="border-b border-gray-600 pb-4 mb-4">
              <ul className="muted space-y-1">
                <li>Incorporating cutting edge AI Analysis features form the basis of our Enterprise Solutions.</li>
                <li>
                  Increase the loyalty of your employees & stakeholders across the Enterprise to have all the R.A.V.I.D. premium subscription features in a
                  cost-efficient license (per seat) economic model.
                </li>
                <li>
                  We will enable your logo to be integrated across all your licensed users along with a user- friendly dashboard for your administrators to
                  simply and delete users along with various subscriptions at your discretion.
                </li>
              </ul>
            </section>

            {/* Demo Images Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-4 mb-8 md:mb-12">
              <img src="/images/enterprise_demo_1.png" alt="Enterprise Demo 1" className="w-full rounded-lg shadow-md hover:shadow-lg transition-shadow" />
              <img src="/images/enterprise_demo_2.png" alt="Enterprise Demo 2" className="w-full rounded-lg shadow-md hover:shadow-lg transition-shadow" />
              <img src="/images/enterprise_demo_3.png" alt="Enterprise Demo 3" className="w-full rounded-lg shadow-md hover:shadow-lg transition-shadow" />
              <img src="/images/enterprise_demo_4.png" alt="Enterprise Demo 4" className="w-full rounded-lg shadow-md hover:shadow-lg transition-shadow" />
            </div>

            <section className="border-b border-gray-600 pb-4 mb-14">
              <p className="text-xs">
                Disclaimer: Your employees & stakeholder's personal data on the R.A.V.I.D. platform is and always remains personal to them (the individual
                account holder) and their information is not shared with any company or administrator at any time during the duration of the license.
                <br />
                Furthermore, in compliance with our Best Practices when an Administration deletes the License access or deletes any particular Subscription
                access of any employees & stakeholders, the individual account holder in question, will lose all or specific subscriptions privileges, however
                their login credentials and data ownership privilege will remain with them at all times.
              </p>
            </section>
          </>
        ) : (
          /* Features View */
          <div className="relative w-full h-[500px] mb-8 md:mb-12 overflow-hidden rounded-lg shadow-md">
            {demoImages.map((image, index) => (
              <div
                key={index}
                className={`absolute inset-0 w-full h-full transition-opacity duration-1000 ${index === currentSlide ? "opacity-100" : "opacity-0"}`}
              >
                <img src={image.src} alt={image.alt} className="w-full bg-slate-950 h-[450px] object-contain" />
                <div className="absolute rounded-b-lg bottom-12 left-1/2 transform -translate-x-1/2 w-full backdrop-blur-md bg-white/30 dark:bg-black/30 p-3 text-black dark:text-white text-center font-medium">
                  {image.text}
                </div>
              </div>
            ))}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
              {demoImages.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${index === currentSlide ? "bg-blue-600" : "bg-gray-400"}`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>
          </div>
        )}

        <SolutionContactInfo />
      </div>
    </>
  );
};

export default Page;
