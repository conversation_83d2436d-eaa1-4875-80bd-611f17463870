"use client";

import { useGetEnterpriseProfile } from "@/hooks/useEnterpriseSolution";
import { Card } from "@heroui/card";
import { CheckCircle2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const EnterprisePaymentSuccess = () => {
  const router = useRouter();
  const { data: enterpriseProfile, isLoading } = useGetEnterpriseProfile(true);

  useEffect(() => {
    if (!isLoading && enterpriseProfile) {
      router.push(`/my/es/${enterpriseProfile.euid}/edit`);
    }
  }, [enterpriseProfile, isLoading, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-transparent p-4">
      <Card className="max-w-md w-full p-8 text-center dark:bg-slate-950">
        <div className="flex flex-col items-center gap-4">
          <CheckCircle2 className="w-16 h-16 text-green-500" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Payment Successful!</h1>
          <p className="text-gray-600 dark:text-gray-400">Thank you for upgrading to Enterprise Solution. Your payment has been processed successfully.</p>
          <p className="text-sm text-gray-500 dark:text-gray-500">You will be redirected to the dashboard in a few seconds...</p>
        </div>
      </Card>
    </div>
  );
};

export default EnterprisePaymentSuccess;
