"use client";

import { useChangePassword } from "@/hooks/settings-dashboard/useChangePassword";
import { <PERSON><PERSON>, Card, CardBody, Card<PERSON>ooter, CardHeader, Input } from "@heroui/react";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

const Page = () => {
  const [email, setEmail] = useState("");
  const { mutate: changePassword, isPending, isSuccess, isError } = useChangePassword();
  const router = useRouter();

  useEffect(() => {
    if (isSuccess) {
      router.push("/signin");
    }
  }, [isSuccess, router]);

  return (
    <div className="h-[80vh] flex justify-center items-center ">
      <Card className="min-w-md dark:bg-slate-950">
        <CardHeader className="flex flex-col gap-2">
          <h2>Forgot Password?</h2>
          <p>No worries! Enter your email and we'll send you reset instructions.</p>
        </CardHeader>
        <CardBody>
          <Input
            classNames={{
              input: "text-xs",
              label: "text-xs text-gray-500 ",
              inputWrapper: "dark:bg-slate-900",
            }}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            size="sm"
            type="email"
            label="Email"
          />
        </CardBody>
        <CardFooter className="flex justify-between">
          <Button onPress={() => router.push("/signin")} size="sm" variant="light">
            Back to Login
          </Button>
          <Button onPress={() => changePassword(email)} size="sm" color="primary">
            Reset Password
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};
export default Page;
