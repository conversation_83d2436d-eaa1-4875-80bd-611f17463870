import { Image } from "@heroui/image";
import { Link } from "@heroui/link";

const Page = () => {
  return (
    <div className="min-h-screen max-w-7xl mx-auto px-4 py-4">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Left Side */}
        <div className="flex-1 space-y-6">
          {/* Header */}
          <div className="space-y-3">
            <h1 className="blue-heading">Communities</h1>
            <p className="text-xs text-justify leading-relaxed">
              We endeavor to empower the <span className="boldSpan">global healthcare community </span> to include doctors, dentists, nurses, researchers,
              scientists, surgeons, medical students, administrators etc with a <span className="boldSpan">Public Profile builder </span> to showcase their
              themselves and their credentials with sophisticated and dynamic digital features &amp; functions with the objective to accelerate your
              professional interactions with your respective stakeholders.
            </p>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 sm:gap-3 justify-center max-w-3xl mx-auto py-4">
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c-4.png" alt="Healthcare Professional in Scrubs" width={128} height={128} className="w-full h-full object-contain" />
              </div>
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c73.png" alt="Healthcare Professional with Stethoscope" width={128} height={128} className="w-full h-full object-contain" />
              </div>
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c-3.png" alt="Healthcare Support Staff" width={128} height={128} className="w-full h-full object-contain" />
              </div>
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c-m.png" alt="Patient Care Scene" width={128} height={128} className="w-full h-full object-contain" />
              </div>
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c-lab.png" alt="Laboratory Professional" width={128} height={128} className="w-full h-full object-contain" />
              </div>
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c-n.png" alt="Healthcare Professional" width={128} height={128} className="w-full h-full object-contain" />
              </div>
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c-5.png" alt="Healthcare Professional" width={128} height={128} className="w-full h-full object-contain" />
              </div>
              <div className="aspect-square max-w-32 mx-auto">
                <Image src="/images/c-l.png" alt="Laboratory Professional" width={128} height={128} className="w-full h-full object-contain" />
              </div>
            </div>

            <p className="text-xs text-justify">
              A simple{" "}
              <Link href="/signup" className="text-xs underline">
                Sign up
              </Link>{" "}
              enables any <span className="boldSpan">global healthcare community </span> stakeholder to have their own personalized{" "}
              <span className="boldSpan">R.A.V.I.D. </span> account and also create their own personalized <span className="boldSpan">Public Profiles</span>{" "}
              which is dynamically editable and directly under your control.
            </p>

            <p className="text-xs text-justify">
              You can refer to this this feature as <span className="boldSpan">&quot;My Public Profile&quot;</span>.{" "}
              <Link href="/signup" className="text-xs underline">
                Sign up
              </Link>{" "}
              now and give it a try.
            </p>

            <p className="text-xs text-justify">
              Our <span className="boldSpan">vision</span> for Communities @ R.A.V.I.D. is to constantly develop tools every that stakeholder within the global
              healthcare community feels empowered to do their jobs more efficiently.
            </p>

            <p className="text-xs text-justify">
              Our <span className="boldSpan">goal</span> at Communities @ R.A.V.I.D. is to bring maximum ease of informational transparency to the patient
              population globally and to enable their interactions with least amount of friction.
            </p>
          </div>

          {/* Contact Information */}
      <div className="md:hidden mb-4 w-full mt-4 md:-mt-10 h-[1px] bg-gray-200 dark:bg-gray-800"></div>
          <p className="text-center md:text-left">
            For additional information please contact us via email at:{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Page;
