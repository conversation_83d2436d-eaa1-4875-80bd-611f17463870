"use client";

import { useRouter } from "next/navigation";
import { useEffect, useRef } from "react";
import { Spinner } from "@heroui/react";
import { useAcceptEnterpriseInvite } from "@/hooks/useEnterpriseSolution";
import { toast } from "react-hot-toast";
import { useGetUser } from "@/hooks/useUser";

const EnterpriseInviteAcceptPage = () => {
  const router = useRouter();
  const { data: user, isLoading: isUserLoading } = useGetUser();
  const { mutate: acceptEnterpriseInvite, isSuccess: isAcceptSuccess } = useAcceptEnterpriseInvite();
  const isProcessing = useRef(false);
  const hasRedirected = useRef(false);

  // Handle redirect logic separately to ensure we only redirect once with valid data
  const handleRedirect = (userData: any) => {
    if (hasRedirected.current) return;

    if (userData?.user_id) {
      // Changed condition to check for user_id first
      const redirectPath = userData.enterprise_member_role === "owner" && userData.euid ? `/my/es/${userData.euid}/edit` : `/my/${userData.user_id}/edit`;

      console.log("Redirecting to:", redirectPath, "Role:", userData.enterprise_member_role);
      hasRedirected.current = true;
      router.push(redirectPath);
    } else {
      console.error("No user_id found in user data:", userData);
      toast.error("User ID not found");
      hasRedirected.current = true;
      router.push("/my/dashboard");
    }
  };

  useEffect(() => {
    const url = new URL(window.location.href);
    const token = url.searchParams.get("token");
    const processedToken = localStorage.getItem("processed_invite_token");

    console.log("Effect running with:", {
      isUserLoading,
      hasUser: !!user,
      userEuid: user?.euid,
      userId: user?.user_id,
      userRole: user?.enterprise_member_role,
      token,
      processedToken,
      isProcessing: isProcessing.current,
      hasRedirected: hasRedirected.current,
    });

    if (!token) {
      router.push("/signin");
      return;
    }

    if (!isUserLoading && user && !isProcessing.current) {
      try {
        // Handle case where user is not authenticated
        if (!user) {
          toast.error("Please log in to accept the invite");
          router.push("/login");
          return;
        }

        // If token was already processed, redirect based on role
        if (token === processedToken) {
          handleRedirect(user);
          return;
        }

        // If token hasn't been processed yet, process it
        if (token !== processedToken) {
          console.log("Processing new invite for user:", user);
          isProcessing.current = true;
          localStorage.setItem("processed_invite_token", token);

          acceptEnterpriseInvite(token, {
            onSuccess: () => {
              toast.success("Enterprise invite accepted successfully");
              localStorage.removeItem("processed_invite_token");
              // Wait for the next user data update before redirecting
              if (user?.user_id) {
                // Changed condition to check for user_id
                handleRedirect(user);
              }
            },
            onError: (error: any) => {
              console.error("Error accepting enterprise invite:", error);
              localStorage.removeItem("processed_invite_token");
              isProcessing.current = false;
              hasRedirected.current = false;

              // Handle specific error cases
              if (error?.response?.status === 404) {
                toast.error("Invite link is invalid or expired");
              } else if (error?.response?.status === 403) {
                toast.error("You don't have permission to accept this invite");
              } else {
                toast.error("Failed to accept enterprise invite");
              }

              router.push("/my/dashboard");
            },
          });
        }
      } catch (error) {
        console.error("Unexpected error during invite processing:", error);
        isProcessing.current = false;
        hasRedirected.current = false;
        toast.error("An unexpected error occurred");
        router.push("/my/dashboard");
      }
    }

    if (!isUserLoading && !user) {
      console.log("No user found, redirecting to signin");
      localStorage.setItem("pending_enterprise_invite", token);
      toast.success("Please sign in or create an account to join the enterprise", {
        duration: 5000,
        icon: "🏢",
      });
      router.push("/signin");
    }
  }, [user, router, isUserLoading, acceptEnterpriseInvite]);

  if (isUserLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="flex flex-col items-center gap-4">
          <Spinner />
          <p className="text-sm">Loading user data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-[60vh]">
      <div className="flex flex-col items-center gap-4">
        <Spinner />
        <p className="text-sm">Processing enterprise invite...</p>
      </div>
    </div>
  );
};

export default EnterpriseInviteAcceptPage;
