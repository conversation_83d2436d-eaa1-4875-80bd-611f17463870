@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  h1 {
    @apply text-sm text-gray-800 dark:text-gray-200;
  }
  h2 {
    @apply text-xs text-gray-800 dark:text-gray-200;
  }
  .red-heading {
    @apply text-xs text-red-500 font-medium;
  }
  .blue-heading {
    @apply text-sm dark:text-blue-500 text-blue-600 font-medium text-justify;
  }
  p {
    @apply text-xs text-justify font-light dark:text-gray-300 text-gray-800;
  }
  .muted {
    @apply dark:text-gray-400 text-gray-800 text-xs text-justify;
  }

  .badge {
    @apply text-xs flex items-center w-fit py-1 px-2 justify-center border border-gray-800 rounded-lg;
  }

  .as-button {
    @apply text-xs flex items-center p-2 justify-center border border-gray-800 bg-blue-600 hover:bg-blue-700 rounded-lg;
  }

  .muted-bg{
    @apply dark:bg-gray-700 text-xs w-fit bg-gray-200 px-1 rounded-md;
  }

  .boldSpan {
    @apply font-bold  dark:text-gray-200 text-gray-800;
  }
  .disclaimer {
    @apply text-xs text-justify dark:text-gray-500 text-gray-800;
  }

  .button-danger {
    @apply bg-red-700 hover:bg-red-800 text-white text-xs;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 222.2 84% 4.9%;
    --chart-2: 217.2 91.2% 59.8%;
    --chart-3: 280 40% 50%;
    --chart-4: 346.8 77.2% 49.8%;
    --chart-5: 142.1 76.2% 36.3%;
    --chart-6: 47.9 95.8% 53.1%;
    --radius: 0.5rem;
    --color-harmful: hsl(var(--chart-1));
    --color-safe: hsl(var(--chart-2));
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 210 40% 98%;
    --chart-2: 217.2 91.2% 59.8%;
    --chart-3: 280 40% 50%;
    --chart-4: 346.8 77.2% 49.8%;
    --chart-5: 142.1 76.2% 36.3%;
    --chart-6: 47.9 95.8% 53.1%;
  }
}

/* Calendar container styling */
.calendar-container {
  --fc-border-color: rgba(229, 231, 235, 0.5);
  --fc-button-bg-color: #4f46e5;
  --fc-button-border-color: #4f46e5;
  --fc-button-hover-bg-color: #4338ca;
  --fc-button-hover-border-color: #4338ca;
  --fc-button-active-bg-color: #3730a3;
  --fc-button-active-border-color: #3730a3;
  --fc-today-bg-color: rgba(79, 70, 229, 0.1);
  --fc-event-bg-color: #4f46e5;
  --fc-event-border-color: transparent;
  --fc-page-bg-color: transparent;
  --fc-small-font-size: 0.75rem;
  --selection-color: #22c55e;
  --selection-color-dark: #22c55e;
  --confirmed-event-color: #e58646;
}

/* Selection styling */
.fc-highlight {
  background-color: var(--selection-color) !important;
  opacity: 0.3;
}

.dark .fc-highlight {
  background-color: var(--selection-color-dark) !important;
  opacity: 0.4;
}

/* Selected date styling */
.fc-day-selected {
  background-color: var(--selection-color) !important;
  opacity: 0.2;
}

.dark .fc-day-selected {
  background-color: var(--selection-color-dark) !important;
  opacity: 0.3;
}

/* Day cell hover effect */
.fc-day:not(.fc-day-disabled):hover {
  background-color: rgba(34, 197, 94, 0.1) !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .fc-day:not(.fc-day-disabled):hover {
  background-color: rgba(34, 197, 94, 0.2) !important;
}

/* Past dates styling */
.fc-day-past {
  opacity: 0.5 !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.dark .fc-day-past {
  background-color: rgba(0, 0, 0, 0.2) !important;
}

.fc-day-past:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
  cursor: not-allowed !important;
}

.dark .fc-day-past:hover {
  background-color: rgba(0, 0, 0, 0.2) !important;
}

/* Today highlighting */
.fc-day-today {
  background-color: rgba(34, 197, 94, 0.1) !important;
}

.dark .fc-day-today {
  background-color: rgba(34, 197, 94, 0.15) !important;
}

/* Dark mode overrides */
.dark .calendar-container {
  --fc-border-color: rgba(75, 85, 99, 0.5);
  --fc-button-text-color: #fff;
  --fc-button-bg-color: #4f46e5;
  --fc-button-border-color: #4f46e5;
  --fc-button-hover-bg-color: #4338ca;
  --fc-button-hover-border-color: #4338ca;
  --fc-today-bg-color: rgba(79, 70, 229, 0.2);
  --fc-page-bg-color: transparent;
  --fc-neutral-bg-color: rgba(31, 41, 55, 0.6);
  --fc-list-event-hover-bg-color: rgba(31, 41, 55, 0.8);
  color-scheme: dark;
}

/* Header styles */
.fc .fc-toolbar-title {
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: -0.01em;
}

.fc .fc-button {
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.3rem 0.6rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  text-transform: none;
  font-size: 0.8rem;
}

.fc .fc-button-primary:not(:disabled):active,
.fc .fc-button-primary:not(:disabled).fc-button-active {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.5);
}

/* Make column headers smaller */
.fc .fc-col-header-cell-cushion {
  padding: 4px 2px;
  font-weight: 500;
  font-size: 0.8rem;
}

/* Make time labels smaller */
.fc-timegrid-axis-cushion,
.fc-timegrid-slot-label-cushion {
  font-size: 0.75rem;
}

/* Event styling */
.fc-event {
  border-radius: 4px;
  padding: 1px 2px;
  font-size: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 2px !important;
  overflow: hidden !important;
}

.fc-event-main {
  padding: 1px 3px;
  font-weight: 400;
}

.fc-theme-standard .fc-scrollgrid {
  border-radius: 8px;
  overflow: hidden;
}

/* Time grid styling */
.fc-timegrid-slot {
  height: 1.5rem !important;
}

.fc-timegrid-slot-lane {
  border-bottom: 1px solid var(--fc-border-color);
}

.fc-timegrid-now-indicator-line {
  border-color: #ef4444;
}

.fc-timegrid-now-indicator-arrow {
  border-color: #ef4444;
  border-width: 5px;
}

/* Day grid styling */
.fc-daygrid-day.fc-day-today {
  background-color: var(--fc-today-bg-color);
}

.fc-day-past {
  opacity: 0.75;
}

.fc-day-future:hover {
  background-color: rgba(243, 244, 246, 0.5);
}

.dark .fc-day-future:hover {
  background-color: rgba(31, 41, 55, 0.3);
}

/* Add pointer cursor on date hover for all views */
.fc-daygrid-day:hover,
.fc-timegrid-col:hover,
.fc-daygrid-day-frame:hover,
.fc-timegrid-slot:hover,
.fc-day:hover {
  cursor: pointer !important;
}

/* Additional pointer cursors specifically for week view */
.fc-timegrid-event:hover,
.fc-timegrid-more-link:hover,
.fc-daygrid-more-link:hover,
.fc-timegrid-col-events:hover,
.fc-timegrid-now-indicator-container:hover,
.fc-timegrid-slot-lane:hover {
  cursor: pointer !important;
}

/* Daygrid view smaller fonts */
.fc-daygrid-day-number {
  font-size: 0.75rem;
  padding: 3px 4px !important;
}

.fc-daygrid-event {
  border-color: transparent !important;
  border-radius: 4px !important;
  margin-bottom: 2px !important;
  min-height: auto !important;
}

.fc-daygrid-event .fc-event-title,
.fc-timegrid-event .fc-event-title {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 0 2px !important;
  line-height: 1.3 !important;
}

.fc-daygrid-event .bg-blue-500,
.fc-timegrid-event .bg-blue-500 {
  display: inline-block !important;
  margin-top: 2px !important;
  line-height: 1.1 !important;
  padding: 1px 4px !important;
  border-radius: 3px !important;
}

.fc-daygrid-event .fc-event-time,
.fc-timegrid-event .fc-event-time {
  font-size: 0.7rem !important;
  line-height: 1.2 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  padding: 0 2px !important;
}

.fc .fc-daygrid-day-events {
  margin-bottom: 0 !important;
  padding-top: 1px !important;
  position: relative !important;
  min-height: 0 !important;
}

.fc-daygrid-day-frame {
  min-height: 85px !important;
}

.fc-daygrid-event-harness {
  margin: 1px 0 2px 0 !important;
}

.fc-daygrid-event-harness {
  display: block !important;
  clear: both !important;
}

/* Month view event styling - ensure colored backgrounds */
.fc-daygrid-event {
  /* background-color: #4f46e5 !important; */
  border-color: transparent !important;
  /* color: white !important; */
  /* padding: 2px 4px !important; */
  border-radius: 4px !important;
  /* margin-top: 1px !important; */
  margin-bottom: 2px !important;
  min-height: auto !important;
}

.fc-daygrid-dot-event {
  display: block !important;
  text-align: center !important;
  padding: 2px 4px !important;
}

/* Confirmed event styling */
.fc-daygrid-event.confirmed-event {
  background-color: var(--confirmed-event-color) !important;
  border-left: 3px solid #fff !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* Style for dot events in month view */
.fc-daygrid-event-dot {
  border-color: #4f46e5 !important;
}

/* Ensure event time text is visible */
.fc-daygrid-event .fc-event-time,
.fc-daygrid-event .fc-event-title {
  color: white !important;
  font-weight: 500;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  /* Toolbar adjustments */
  .fc .fc-toolbar {
    flex-direction: column;
    gap: 0.75rem;
    padding: 0.5rem;
  }

  .fc .fc-toolbar-title {
    font-size: 1rem;
    text-align: center;
  }

  /* Button group adjustments */
  .fc .fc-button-group {
    gap: 0.25rem;
  }

  .fc .fc-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }

  /* Grid adjustments */
  .fc .fc-view-harness {
    min-height: 400px !important;
  }

  /* Day cell adjustments */
  .fc-daygrid-day-frame {
    min-height: 80px !important;
  }

  .fc-daygrid-day-number {
    font-size: 0.7rem !important;
    padding: 2px !important;
    margin: 2px !important;
  }

  /* Event adjustments */
  .fc-daygrid-event-harness {
    margin: 1px 2px !important;
  }

  .fc-daygrid-event {
    font-size: 0.65rem !important;
    padding: 1px 3px !important;
    margin: 1px 0 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* More link styling */
  .fc-daygrid-more-link {
    font-size: 0.65rem !important;
    margin: 1px 2px !important;
    padding: 1px !important;
  }

  /* Week view adjustments */
  .fc-timegrid-slot {
    height: 2.5rem !important;
  }

  .fc-timegrid-axis-cushion,
  .fc-timegrid-slot-label-cushion {
    font-size: 0.65rem !important;
  }

  /* Header adjustments */
  .fc-col-header-cell-cushion {
    font-size: 0.7rem !important;
    padding: 4px 2px !important;
  }

  /* Event content adjustments */
  .fc-event-main {
    padding: 1px !important;
  }

  .fc-event-time,
  .fc-event-title {
    font-size: 0.65rem !important;
    line-height: 1.2 !important;
  }

  /* Time range display in column format */
  .fc-daygrid-event .fc-event-time {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
    padding: 1px 2px;
  }

  .fc-daygrid-event .fc-event-time::after {
    content: "-";
    font-size: 0.6rem;
    margin: -2px 0;
  }

  /* Adjust event container for column time display */
  .fc-daygrid-event-harness {
    margin: 1px 2px !important;
    min-height: 35px !important;
  }

  .fc-daygrid-event {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    gap: 2px;
    font-size: 0.65rem !important;
    padding: 1px 3px !important;
    margin: 1px 0 !important;
  }

  .fc-daygrid-event .fc-event-main {
    display: flex;
    flex-direction: column;
    min-height: 30px;
    justify-content: center;
  }

  .fc-daygrid-event .fc-event-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.6rem !important;
  }

  /* Adjust day cell height to accommodate column format */
  .fc-daygrid-day-frame {
    min-height: 85px !important;
  }

  /* Week view time format adjustments */
  .fc-timegrid-event .fc-event-time {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
    font-size: 0.6rem !important;
  }

  .fc-timegrid-event .fc-event-time::after {
    content: "-";
    font-size: 0.6rem;
    margin: -2px 0;
  }

  /* Touch interactions */
  .fc-event,
  .fc-daygrid-day,
  .fc-daygrid-day-frame,
  .fc-timegrid-slot,
  .fc-timegrid-slot-lane {
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  /* Improved tap targets */
  .fc-daygrid-day-frame {
    padding: 2px !important;
  }

  /* Event spacing in month view */
  .fc-dayGridMonth-view .fc-daygrid-day-events {
    margin: 0 !important;
    padding: 0 1px !important;
  }

  /* Modal adjustments for mobile */
  .modal-content {
    margin: 1rem;
    max-height: 90vh;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .fc .fc-toolbar-title {
    font-size: 0.9rem;
  }

  .fc .fc-button {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .fc-daygrid-day-number {
    font-size: 0.65rem !important;
  }

  .fc-daygrid-event {
    font-size: 0.6rem !important;
  }

  /* Further optimize time display for smaller screens */
  .fc-daygrid-event {
    font-size: 0.6rem !important;
    min-height: 32px !important;
  }

  .fc-daygrid-event .fc-event-time {
    font-size: 0.58rem !important;
  }

  .fc-daygrid-event .fc-event-title {
    font-size: 0.58rem !important;
  }

  /* Ensure sufficient height for column format */
  .fc-daygrid-day-frame {
    min-height: 70px !important;
  }

  /* Compact view for very small screens */
  .fc-dayGridMonth-view .fc-daygrid-day-frame {
    min-height: 60px !important;
  }
}

/* Landscape mode adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .fc .fc-view-harness {
    min-height: 300px !important;
  }

  .fc-dayGridMonth-view .fc-daygrid-day-frame {
    min-height: 70px !important;
  }
} 