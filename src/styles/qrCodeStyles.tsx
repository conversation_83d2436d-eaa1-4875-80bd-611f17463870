import { Options } from "qr-code-styling";

export const getVerifiedQRCodeOptions = (): Options => ({
  width: 300,
  height: 300,
  type: "svg",
  image: "/images/ravid.png",
  imageOptions: {
    saveAsBlob: true,
    hideBackgroundDots: true,
    imageSize: 0.5,
    margin: 0,
  },
  margin: 5,
  qrOptions: {
    typeNumber: 0,
    mode: "Byte",
    errorCorrectionLevel: "Q",
  },
  dotsOptions: {
    type: "rounded",
    color: "#d7bb70",
    roundSize: true,
    gradient: {
      type: "radial",
      rotation: 0,
      colorStops: [
        { offset: 0, color: "#d7bb70" },
        { offset: 1, color: "#c6a74e" },
      ],
    },
  },
  cornersSquareOptions: {
    type: "extra-rounded",
    color: "#d7bb70",
    gradient: {
      type: "linear",
      rotation: 0,
      colorStops: [
        { offset: 0, color: "#d7bb70" },
        { offset: 1, color: "#c6a74e" },
      ],
    },
  },
  cornersDotOptions: {
    type: "dot",
    color: "#d7bb70",
    gradient: {
      type: "linear",
      rotation: 0,
      colorStops: [
        { offset: 0, color: "#d7bb70" },
        { offset: 1, color: "#c6a74e" },
      ],
    },
  },
  backgroundOptions: { round: 0, color: "#000000" },
});

export const getUnverifiedQRCodeOptions = (): Options => ({
  width: 300,
  height: 300,
  type: "svg",
  image: "/images/ravid.png",
  margin: 5,
  qrOptions: {
    typeNumber: 0,
    mode: "Byte",
    errorCorrectionLevel: "Q",
  },
  dotsOptions: {
    type: "square",
    color: "#000000",
  },
  cornersSquareOptions: {
    type: "square",
    color: "#000000",
  },
  cornersDotOptions: {
    type: "dot",
    color: "#000000",
  },
  backgroundOptions: { round: 0, color: "#FFFFFF" },
});

export const getQRCodeOptions = (isPaidForVerification: boolean = false): Options => {
  return isPaidForVerification ? getVerifiedQRCodeOptions() : getUnverifiedQRCodeOptions();
};