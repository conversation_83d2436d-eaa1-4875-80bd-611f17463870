# Playwright E2E Tests with Global Authentication

This directory contains end-to-end tests using <PERSON><PERSON> with separate tests for authenticated and guest users.

## Folder Structure

The tests are organized in the following structure:

```
e2e/
├── auth/               # Tests that need authentication
│   ├── authenticated.spec.ts
│   └── ...
├── guest/              # Tests for unauthenticated users
│   ├── public-pages.spec.ts
│   └── ...
├── global-setup.ts     # Global authentication setup
└── README.md           # Documentation
```

- `auth/`: All tests that require a logged-in user
- `guest/`: All tests for guest users (not logged in)

## Authentication Setup

Authentication is handled globally using <PERSON><PERSON>'s `storageState` feature:

1. `global-setup.ts` runs before tests to:
   - Log in to the application
   - Save authentication state to `storageState.json`

2. `playwright.config.ts` is configured with two separate projects:
   - `authenticated`: Uses the saved authentication state
   - `guest`: Uses an empty storage state (no authentication)

## Running Tests for Different User Types

### For Authenticated Users

To run tests that need authentication:

```bash
npx playwright test --project=authenticated
```

### For Guest Users

To run tests for unauthenticated/guest users:

```bash
npx playwright test --project=guest
```

Always specify the project when running tests to ensure you're using the correct authentication state.

## Environment Variables

To run the tests, you need to set the following environment variables:

```
NEXT_PUBLIC_YOUR_EMAIL=<EMAIL>
NEXT_PUBLIC_YOUR_PASSWORD=your_password
NEXT_PUBLIC_ADMIN_PASSWORD=admin_password
```

You can set these in a `.env` file in the project root.

## Running Individual Tests

To run a specific test file:

```bash
# For authenticated tests
npx playwright test e2e/auth/authenticated.spec.ts --project=authenticated

# For guest tests
npx playwright test e2e/guest/public-pages.spec.ts --project=guest
```

## Troubleshooting

If guest tests are still using authentication state:

1. Ensure you're using the `--project=guest` flag when running tests
2. Clear browser cache/storage between test runs
3. Delete `storageState.json` and run tests again
4. Verify the test is in the correct directory (`e2e/guest/`)

## VS Code Extension

For convenience, it is recommended to install the "Playwright Test for VS Code" extension to help with test management, debugging, and execution directly from the editor. 