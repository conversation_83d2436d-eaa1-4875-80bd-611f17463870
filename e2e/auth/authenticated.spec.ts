import { expect, test } from "@playwright/test";

const ADMIN_PASSWORD = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || "";

test.describe("Authenticated tests", () => {
  test("should access authenticated page without login", async ({ page }) => {
    // Since we're using storageState, we should already be authenticated
    // Go to the homepage
    await page.goto("/");
    const store = await page.evaluate(() => {
      return window.localStorage.getItem("store");
    });
    const userId = JSON.parse(store || "{}").state.user.user_id;
    await expect(page).toHaveURL(`/my/${userId}/home`);
  });
});
