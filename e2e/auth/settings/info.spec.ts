import { test } from "@playwright/test";

test.describe("Personal Information Settings", () => {
  test("should be able to edit first name", async ({ page }) => {
    // Navigate to settings page
    await page.goto("/");
    // find the button with text "Settings"
    await page.getByRole("link", { name: "My Settings" }).click();
    // find the button with test id "personal"
    await page.getByTestId("personal").click();
    // find the input with name "first_name"
    await page.locator("#first_name").fill("NewFirstName");
    // find the button with text "Save"
    await page.getByRole("button", { name: "Save" }).click();
  });
});

test.describe("Emergency Information Settings", () => {
  test("should be able to edit last name", async ({ page }) => {
    // Navigate to settings page
    await page.goto("/");
    // find the button with text "Settings"
    await page.getByRole("link", { name: "<PERSON> Settings" }).click();
    // find the button with test id "emergency"
    await page.getByTestId("emergency").click();
    // find the input with name "last_name"
    await page.locator("#contact_information_0_contact_name").fill("New Contact Name");
    // find the button with text "Save"
    await page.getByRole("button", { name: "Save" }).click();
  });
});
