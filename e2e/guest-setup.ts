import { chromium } from "@playwright/test";

async function globalGuestSetup() {
  // Create a clean browser context with no stored state
  const browser = await chromium.launch();
  const context = await browser.newContext();

  // Save an empty storage state to a separate file for guest tests
  await context.storageState({ path: "guestStorageState.json" });

  await browser.close();
}

export default globalGuestSetup;
