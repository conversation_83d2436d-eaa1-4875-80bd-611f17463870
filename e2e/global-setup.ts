import { test as setup } from "@playwright/test";
import fs from "fs";
import path from "path";

const ADMIN_PASSWORD = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || "Mango99";
const YOUR_EMAIL = process.env.NEXT_PUBLIC_YOUR_EMAIL || "";
const YOUR_PASSWORD = process.env.NEXT_PUBLIC_YOUR_PASSWORD || "";

// Ensure the auth directory exists
const authDir = path.join(__dirname, "auth");
if (!fs.existsSync(authDir)) {
  fs.mkdirSync(authDir, { recursive: true });
}

const storageStatePath = path.join(authDir, "storageState.json");

setup("authenticate user", async ({ browser }) => {
  // Create a new context (login process will be invisible in test reports)
  const context = await browser.newContext();
  const page = await context.newPage();

  // Hide the login process from test reports and console
  console.log("Performing authentication setup (hidden from UI)...");

  // Navigate to login page
  await page.goto("http://localhost:3001/signin");

  // Wait for the admin protection popup to appear
  await page.waitForSelector('input[type="password"]');
  // Enter admin password
  await page.locator('input[type="password"]').fill(ADMIN_PASSWORD);
  // Click submit button
  await page.getByRole("button", { name: "Submit" }).click();

  // Fill in login credentials
  await page.getByLabel("Email").fill(YOUR_EMAIL);
  await page.waitForSelector('input[type="password"]');
  await page.locator('input[type="password"]').fill(YOUR_PASSWORD);

  // Click login button
  await page.getByRole("button", { name: "Login" }).click();

  // Wait for login to complete
  const loginToast = page.locator("text=Logged in successfully");
  await loginToast.waitFor();

  // Save storage state to file
  await context.storageState({ path: storageStatePath });
  console.log("Authentication completed and state saved. Ready for running tests.");
  await context.close();
});
