import { expect, test } from "@playwright/test";

const ADMIN_PASSWORD = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || "";
const YOUR_EMAIL = process.env.NEXT_PUBLIC_YOUR_EMAIL || "";
const YOUR_PASSWORD = process.env.NEXT_PUBLIC_YOUR_PASSWORD || "";

test.describe("Login functionality", () => {
  test.beforeEach(async ({ page, context }) => {
    // Navigate to login page before each test
    await page.goto("/signin");
    // Wait for the admin protection popup to appear
    await page.waitForSelector('input[type="password"]');
    // Enter admin password
    await page.locator('input[type="password"]').fill(ADMIN_PASSWORD);
    // Click submit button
    await page.getByRole("button", { name: "Submit" }).click();
    // Wait for redirection to complete and ensure we're on the signin page
    await page.waitForURL("/signin");
  });

  test("should login successfully with your account", async ({ page, context }) => {
    // Fill in login credentials
    await page.getByLabel("Email").fill(YOUR_EMAIL);
    await page.waitForSelector('input[type="password"]');
    await page.locator('input[type="password"]').fill(YOUR_PASSWORD);
    // Click login button
    await page.getByRole("button", { name: "Login" }).click();
    // show toast message
    const loginToast = page.locator("text=Logged in successfully");
    await expect(loginToast).toBeVisible();
    // Verify redirect to user dashboard
    // how can i get the user id from the database?
    const store = await page.evaluate(() => {
      return window.localStorage.getItem("store");
    });
    const userId = JSON.parse(store || "{}").state.user.user_id;
    await expect(page).toHaveURL(`/my/${userId}/agent`);
    await context.storageState({ path: "./e2e/auth/storageState.json" });
  });
});
