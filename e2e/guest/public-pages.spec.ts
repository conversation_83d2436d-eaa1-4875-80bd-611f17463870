import { expect, test } from "@playwright/test";

const ADMIN_PASSWORD = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || "Mango99";

test.describe("Guest user tests", () => {
  test.beforeEach(async ({ page, context }) => {
    // Navigate to login page before each test
    await page.goto("/signin");
    // Wait for the admin protection popup to appear
    await page.waitForSelector('input[type="password"]');
    // Enter admin password
    await page.locator('input[type="password"]').fill(ADMIN_PASSWORD);
    // Click submit button
    await page.getByRole("button", { name: "Submit" }).click();
    // Wait for redirection to complete and ensure we're on the signin page
    await page.waitForURL("/signin");
  });

  test("should be redirected to login when accessing protected page", async ({ page }) => {
    // Try to access a protected page directly for example /my/dna
    await page.goto("/my/dna");
    // Should be redirected to login page
    await expect(page).toHaveURL("/signin");
  });

  // go to public page like /about, /collaborate, /communities, /solutions
  test("should be able to view the public page", async ({ page }) => {
    // Go to the public page (e.g., homepage or landing page)
    await page.goto("/about");
    await expect(page).toHaveURL("/about");

    await page.goto("/collaborate");
    await expect(page).toHaveURL("/collaborate");

    await page.goto("/communities");
    await expect(page).toHaveURL("/communities");

    await page.goto("/solutions");
    await expect(page).toHaveURL("/solutions");
  });
});
