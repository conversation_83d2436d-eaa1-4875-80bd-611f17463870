# Build stage
FROM oven/bun:1.1-alpine AS builder
WORKDIR /app

# Add build argument for git commit
ARG GIT_COMMIT=latest

# Copy package files
COPY package.json bun.lockb* ./

# Install dependencies using bun
RUN bun install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN bun run build

# Production stage
FROM oven/bun:1.1-alpine AS runner
WORKDIR /app

# Add build argument for git commit
ARG GIT_COMMIT=latest
# Add label with git commit for traceability
LABEL git_commit=$GIT_COMMIT

ENV NODE_ENV=production

# Create a non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy built application from builder stage
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/package.json ./
COPY --from=builder /app/bun.lockb* ./

# Install only production dependencies
RUN bun install --production --frozen-lockfile

# Switch to non-root user
USER nextjs

EXPOSE 3000

# Use bun to start the application
CMD ["bun", "start"] 