name: ravid
services:
  nextjs:
    container_name: ravid-new:${GIT_COMMIT:-latest}
    image: ravid-new:${GIT_COMMIT:-latest}
    build:
      context: .
      dockerfile: Dockerfile.bun
      args:
        - GIT_COMMIT=${GIT_COMMIT:-latest}
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    env_file:
      - .env
    environment:
      - CHOKIDAR_USEPOLLING=true
      - NODE_ENV=production
