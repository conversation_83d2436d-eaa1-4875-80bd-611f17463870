type User = {
  id?: number;
  euid?: string;
  first_name?: string;
  middle_name?: string;
  last_name?: string;
  title?: string;
  username?: string;
  email: string;
  image?: string;
  user_id?: string;
  role?: string;
  clinic_id?: string;
  dob?: string;
  gender?: string;
  locations?: string;
  language?: string;
  blood_group?: string;
  digital_blood?: string;
  genome_tested?: string;
  private_profile_picture?: string;
  cuid?: string;
  logo?: string;
  is_public_profile?: boolean;
  enterprise_logo?: string;
  enterprise_website?: string;
  paid_for_verification?: boolean;
  is_id_verified?: boolean;
  enterprise_member_role?: "owner" | "admin" | "member";

  // address
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  zipcode?: string;
  mobile?: string;

  // Merged Profile fields
  bio?: string;
  profile_picture?: string;
  credentials?: string;
  is_email_verified?: boolean;
  is_credentials_verified?: boolean;
  title?: string;
  education?: any[];
  about_me?: string;
  affiliations?: string;
  practices?: string;
  research_papers?: any[];
  awards?: any[];
  youtube_videos?: any[];
  speciality?: string;
  practice_locations?: any[];
  custom_fields?: any[];
};

type EmergencyContact = {
  id: string;
  contact_name: string;
  email: string;
  phone_number: string;
  relationship: string;
  type: string;
  created_at: string;
  updated_at: string;
};

type MedicalTeamMember = {
  id: string;
  name: string;
  email: string;
  contact_number: string;
  affiliation: string;
  role: string;
  created_at: string;
  updated_at: string;
};

type Insurance = {
  id: string;
  provider: string;
  policy_number: string;
  type: string;
  start_date: string | null;
  end_date: string | null;
  policy_holder_name: string;
  dependent_information: string;
  website: string;
  group_number: string;
  deductable_amount: number | null;
  copay_amount: number | null;
  additional_document: string;
};

type Tab = {
  name: string;
  color?: string;
  content?: ReactElement;
};

type Notes = {
  id?: string;
  title?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
};

type EnterpriseContent = {
  id: number;
  unique_identifier?: string;
  identification_number?: string | "";
  admin_email?: string | null;
  name?: string | null;
  about?: string | null;
  contact_number?: string | null;
  additional_email?: string | null;
  language_spoken?: string | null;
  insurance_accepted?: boolean;
  logo?: string | null;
  logo_file?: string | null;
  location?: {
    id?: number | null;
    address?: string | null;
    city?: string | null;
    state?: string | null;
    zip_code?: string | null;
    country?: string | null;
  } | null;
  services?: [] | null;
  doctors?: [] | null;
  operating_hours?: [] | null;
  departments?: [] | null;
  enterprise_department_doctors?: [] | null;
  enterprise_images?: [] | null;
  is_enterprise_public?: boolean;
  date_of_incorporation?: string | null;
  state_of_incorporation?: string | "";
  website?: string | null;
};

type EnterpriseSubmitData = Omit<Partial<EnterpriseContent>, "logo">;

// Add a type for the form fields
type AdminFields = {
  id?: string;
  name: string;
  email: string;
  phone_number: string;
  position: string;
}[];

type SolutionContent = {
  id: number | null;
  unique_identifier: string | null;
  admin_email: string | null;
  name: string | null;
  about: string | null;
  contact_number: string | null;
  additional_email: string | null;
  language_spoken: string | null;
  insurance_accepted: boolean | null;
  logo: string | null;
  location: string | null;
  services: [] | null;
  doctors: [] | null;
  operating_hours: [] | null;
  departments: [] | null;
  clinic_department_doctors: [] | null;
  clinic_images: [] | null;
  is_clinic_public: boolean | null;
};

type ServiceResponse = {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  storage_size: string;
  features: string[];
  price: string;
  is_active: boolean;
  service_type: string;
  order: number;
  button_text: string;
};

// Define the alert type
type TabAlert = {
  id: string;
  message: string;
  tab: string;
  start_date: Date | string;
  end_date: Date | string;
  // Support both naming conventions for backward compatibility
  createdAt?: Date | string;
  updatedAt?: Date | string;
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
};

// Form data for adding/editing tab alerts
type AlertFormData = {
  message: string;
  tab: string;
  start_date: Date;
  end_date: Date;
};

// DNA Dashboard Types
type PromotionFormValues = {
  code: string;
  promotion_type: "ONLINE" | "OFFLINE";
  discount_percentage: number;
  discountType: "Percentage" | "Fixed" | "None";
  start_date: string;
  end_date: string;
  timezone: string;
  is_active: boolean;
  description?: string;
};

type Service = {
  id: string;
  name: string;
  features: string[];
  price: string;
  discounted_price: string;
  active_promotions?: PromotionFormValues[];
};

type CancerServiceDetails = {
  serviceId: string | null;
  genes: string[];
  depth: string;
};
