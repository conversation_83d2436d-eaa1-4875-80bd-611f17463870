{"name": "ravid-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/resource": "^6.1.17", "@fullcalendar/resource-timeline": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@heroui/autocomplete": "^2.3.18", "@heroui/button": "2.2.16", "@heroui/date-input": "^2.3.16", "@heroui/drawer": "2.2.13", "@heroui/dropdown": "2.3.16", "@heroui/form": "2.1.15", "@heroui/input": "2.4.16", "@heroui/link": "2.2.13", "@heroui/modal": "2.2.13", "@heroui/progress": "^2.2.13", "@heroui/react": "2.7.5", "@heroui/select": "2.4.16", "@heroui/spinner": "2.2.13", "@heroui/switch": "2.2.14", "@heroui/system": "2.4.12", "@heroui/table": "2.2.15", "@heroui/tabs": "2.2.13", "@heroui/theme": "2.4.12", "@heroui/user": "^2.2.13", "@hookform/devtools": "^4.4.0", "@hookform/resolvers": "^3.10.0", "@internationalized/date": "3.6.0", "@tanstack/query-sync-storage-persister": "^5.73.1", "@tanstack/react-query": "^5.72.2", "@tanstack/react-query-devtools": "^5.72.2", "@tanstack/react-query-persist-client": "^5.73.1", "axios": "^1.8.4", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^11.18.2", "i18next": "^24.2.3", "jotai": "^2.12.2", "lucide-react": "^0.471.2", "next": "^15.3.0", "next-themes": "^0.4.6", "prettierrc": "^0.0.0-5", "qr-code-styling": "^1.9.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "recharts": "^2.15.2", "tailwind-merge": "^3.2.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@playwright/test": "^1.52.0", "@types/node": "^20.17.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "eslint": "^9.24.0", "eslint-config-next": "15.1.4", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}