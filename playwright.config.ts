import { defineConfig, devices } from "@playwright/test";
import fs from "fs";
import path from "path";

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
import dotenv from "dotenv";
dotenv.config({ path: path.resolve(__dirname, ".env") });

// Define storage state path
const storageStatePath = path.join(__dirname, "e2e/auth/storageState.json");

// Create a fallback empty storage state
const emptyStorageState = { cookies: [], origins: [] };

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: "./e2e",
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  // retries: process.env.CI ? 2 : 0,
  retries: 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: "html",
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: "http://localhost:3001",
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "on-first-retry",

    /* Run tests in headed (non-headless) mode */
    headless: false,

    /* Slow down Playwright operations by this milliseconds */
    launchOptions: {
      slowMo: 1000,
    },
  },

  /* Configure projects for major browsers */
  projects: [
    // Only include setup project if needed
    ...(needsAuth()
      ? [
          {
            name: "setup",
            testMatch: "**/global-setup.ts",
            // Only include setup if storage state doesn't exist or is too old
            use: { headless: true },
          },
        ]
      : []),

    // Authenticated project (default)
    {
      name: "authenticated",
      testDir: "./e2e/auth",
      // Only depend on setup if it's included
      dependencies: needsAuth() ? ["setup"] : [],
      use: {
        ...devices["Desktop Chrome"],
        /* Use the saved authentication state if exists, otherwise use empty state */
        storageState: fs.existsSync(storageStatePath) ? storageStatePath : emptyStorageState,
      },
    },

    // Guest user project (no authentication)
    {
      name: "guest",
      testDir: "./e2e/guest",
      use: {
        ...devices["Desktop Chrome"],
        // Use a blank/empty storage state for guest tests
        storageState: emptyStorageState,
      },
    },

    // {
    //   name: "firefox",
    //   use: { ...devices["Desktop Firefox"] },
    // },

    // {
    //   name: "webkit",
    //   use: { ...devices["Desktop Safari"] },
    // },

    /* Test against mobile viewports. */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },

    /* Test against branded browsers. */
    // {
    //   name: 'Microsoft Edge',
    //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
    // },
    // {
    //   name: 'Google Chrome',
    //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    // },
  ],

  /* Run your local dev server before starting the tests */
  // webServer: {
  //   command: 'npm run start',
  //   url: 'http://127.0.0.1:3000',
  //   reuseExistingServer: !process.env.CI,
  // },
});

// Helper function to determine if auth is needed
function needsAuth() {
  // Check if storage state exists
  if (!fs.existsSync(storageStatePath)) return true;

  // Check if it's recent enough (e.g., less than 1 day old)
  const stats = fs.statSync(storageStatePath);
  const ageInDays = (Date.now() - stats.mtimeMs) / (1000 * 60 * 60 * 24);
  return ageInDays > 1; // Re-auth if older than 1 day
}
