name: Docker Compose Build and Up

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to (test, prod)'
        required: true
        type: string
        default: 'test' # Default to test environment if not specified
      branch:
        description: 'Branch to deploy from'
        required: true
        type: string
        default: 'main' # Default to master branch 

run-name: Deploy to TEST - ${{ inputs.environment }}

jobs:
  build-and-up-us:
    environment: ${{ inputs.environment }}
    name: Build and Up on US Runner
    runs-on: [ 'self-hosted','us', '${{ inputs.environment }}' ]
    steps:
      - name: Pull Latest Code and Checkout Branch
        run: |
          cd /home/<USER>/ravid
          git fetch origin
          git checkout ${{ inputs.branch }}
          git pull origin ${{ inputs.branch }}
        continue-on-error: false

      - name: Docker system prune
        run: |
          docker system prune -f
          echo "Cleaned up unused Docker resources"

      - name: Set Commit SHA as Image Tag
        run: |
          cd /home/<USER>/ravid
          echo "GIT_COMMIT=$(git rev-parse --short HEAD)" >> $GITHUB_ENV
          echo "Using commit SHA as tag: $(git rev-parse --short HEAD)"

      - name: Build and Start Docker Compose Services
        run: |
          cd /home/<USER>/ravid
          echo "Building Docker images..."
          export GIT_COMMIT=${{ env.GIT_COMMIT }}
          docker compose -f docker-compose.yml build --no-cache --build-arg GIT_COMMIT=${GIT_COMMIT} || exit 1
          echo "Starting services..."
          docker compose -f docker-compose.yml up -d --force-recreate || exit 1
          echo "Services started successfully"
        continue-on-error: false
