# Build stage
FROM node:20-alpine AS builder
WORKDIR /app

# Add build argument for git commit
ARG GIT_COMMIT=latest

COPY package*.json ./package.json
RUN apk add --no-cache git
RUN npm install -g npm@11.1.0
RUN npm install --legacy-peer-deps

COPY . .
RUN npm run build

# Production stage
FROM node:20-alpine AS runner
WORKDIR /app

# Add build argument for git commit
ARG GIT_COMMIT=latest
# Add label with git commit for traceability
LABEL git_commit=$GIT_COMMIT

ENV NODE_ENV production

# Copy only necessary files from the builder stage
COPY --from=builder /app/next.config.ts ./next.config.ts
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/node_modules ./node_modules

EXPOSE 3000

CMD ["npm", "start"]

