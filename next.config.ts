import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */

  // Tăng tốc development
  experimental: {
    // Turbopack đã được enable qua CLI flag
    optimizePackageImports: ['@heroui/react', 'lucide-react', 'react-icons'],
    // Giảm memory usage
    memoryBasedWorkers: true,
  },

  // T<PERSON><PERSON> compiler
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Tăng tốc build
  swcMinify: true,

  // Tối ưu images
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**", // Allows all domains
      },
    ],
    // <PERSON><PERSON><PERSON><PERSON> kích thước images
    formats: ['image/webp', 'image/avif'],
  },

  // Tối ưu webpack cho dev
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Tăng tốc hot reload
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };

      // Giảm memory usage
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
          },
        },
      };
    }
    return config;
  },

};

export default nextConfig;
